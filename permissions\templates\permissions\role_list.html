{% extends 'layouts/base.html' %}

{% block title %}Quản lý vai trò | Hospital Manager{% endblock %}

{% block page_title %}Quản lý vai trò{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Quản lý vai trò</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="row">
          <div class="col-md-6">
            <h3 class="card-title">Danh sách vai trò</h3>
          </div>
          <div class="col-md-6">
            <form action="{% url 'permissions:role_list' %}" method="get" class="d-flex w-100">
              <input type="text" name="search" class="form-control float-right" placeholder="Tìm kiếm theo tên" value="{{ search_query|default:'' }}">
              <div class="input-group-append">
                <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body">
        <div class="mb-3">
          <a href="{% url 'permissions:role_create' %}" class="btn btn-primary">
            <i class="fas fa-plus mr-1"></i> Thêm vai trò mới
          </a>
          <a href="{% url 'permissions:role_template_manager' %}" class="btn btn-info ml-2">
            <i class="fas fa-cogs mr-1"></i> Quản lý mẫu vai trò
          </a>
        </div>

        <!-- Default Roles Section -->
        <div class="card mb-4">
          <div class="card-header bg-primary">
            <h3 class="card-title">Vai trò mặc định</h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          <div class="card-body">
            <p class="text-muted">Các vai trò mặc định được cấu hình sẵn với các quyền phù hợp. Bạn có thể tạo nhanh các vai trò này bằng cách nhấn vào nút tương ứng.</p>

            <div class="row">
              <!-- Super Admin Role -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-danger">
                    <h5 class="card-title">Super Admin</h5>
                  </div>
                  <div class="card-body">
                    <p>Vai trò có toàn quyền quản trị hệ thống, bao gồm tất cả các chức năng.</p>
                    <ul>
                      <li>Quản lý người dùng</li>
                      <li>Quản lý khoa phòng</li>
                      <li>Quản lý thiết bị</li>
                      <li>Quản lý phân quyền</li>
                      <li>Quản lý luyện tập gõ chữ</li>
                    </ul>
                    <form action="{% url 'permissions:role_create' %}" method="post" class="mt-3">
                      {% csrf_token %}
                      <input type="hidden" name="name" value="Super Admin">
                      <input type="hidden" name="description" value="Vai trò có toàn quyền quản trị hệ thống, bao gồm tất cả các chức năng.">
                      <input type="hidden" name="is_default" value="1">
                      <input type="hidden" name="default_type" value="superadmin">
                      <button type="submit" class="btn btn-danger btn-block">
                        <i class="fas fa-plus-circle mr-1"></i> Tạo vai trò Super Admin
                      </button>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Admin Role -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-warning">
                    <h5 class="card-title">Admin</h5>
                  </div>
                  <div class="card-body">
                    <p>Vai trò quản trị viên với quyền quản lý hầu hết các chức năng, ngoại trừ một số chức năng nhạy cảm.</p>
                    <ul>
                      <li>Quản lý người dùng (không xóa)</li>
                      <li>Quản lý khoa phòng</li>
                      <li>Quản lý thiết bị</li>
                      <li>Xem thông tin phân quyền</li>
                      <li>Quản lý luyện tập gõ chữ</li>
                    </ul>
                    <form action="{% url 'permissions:role_create' %}" method="post" class="mt-3">
                      {% csrf_token %}
                      <input type="hidden" name="name" value="Admin">
                      <input type="hidden" name="description" value="Vai trò quản trị viên với quyền quản lý hầu hết các chức năng, ngoại trừ một số chức năng nhạy cảm.">
                      <input type="hidden" name="is_default" value="1">
                      <input type="hidden" name="default_type" value="admin">
                      <button type="submit" class="btn btn-warning btn-block">
                        <i class="fas fa-plus-circle mr-1"></i> Tạo vai trò Admin
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>

            <div class="row mt-3">
              <!-- Department Manager Role -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-success">
                    <h5 class="card-title">Quản lý khoa phòng</h5>
                  </div>
                  <div class="card-body">
                    <p>Vai trò quản lý khoa phòng với quyền quản lý người dùng và thiết bị trong phạm vi khoa phòng.</p>
                    <ul>
                      <li>Xem danh sách người dùng</li>
                      <li>Quản lý thiết bị trong khoa phòng</li>
                      <li>Xem thông tin khoa phòng</li>
                      <li>Quản lý luyện tập gõ chữ</li>
                    </ul>
                    <form action="{% url 'permissions:role_create' %}" method="post" class="mt-3">
                      {% csrf_token %}
                      <input type="hidden" name="name" value="Quản lý khoa phòng">
                      <input type="hidden" name="description" value="Vai trò quản lý khoa phòng với quyền quản lý người dùng và thiết bị trong phạm vi khoa phòng.">
                      <input type="hidden" name="is_default" value="1">
                      <input type="hidden" name="default_type" value="department_manager">
                      <button type="submit" class="btn btn-success btn-block">
                        <i class="fas fa-plus-circle mr-1"></i> Tạo vai trò Quản lý khoa phòng
                      </button>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Staff Role -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-info">
                    <h5 class="card-title">Nhân viên</h5>
                  </div>
                  <div class="card-body">
                    <p>Vai trò nhân viên với quyền sử dụng các chức năng cơ bản của hệ thống.</p>
                    <ul>
                      <li>Xem thông tin cá nhân</li>
                      <li>Xem danh sách thiết bị</li>
                      <li>Sử dụng chức năng luyện tập gõ chữ</li>
                      <li>Gửi và nhận tin nhắn</li>
                    </ul>
                    <form action="{% url 'permissions:role_create' %}" method="post" class="mt-3">
                      {% csrf_token %}
                      <input type="hidden" name="name" value="Nhân viên">
                      <input type="hidden" name="description" value="Vai trò nhân viên với quyền sử dụng các chức năng cơ bản của hệ thống.">
                      <input type="hidden" name="is_default" value="1">
                      <input type="hidden" name="default_type" value="staff">
                      <button type="submit" class="btn btn-info btn-block">
                        <i class="fas fa-plus-circle mr-1"></i> Tạo vai trò Nhân viên
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Roles table -->
        <div class="table-responsive">
          <table class="table table-hover text-nowrap">
            <thead>
              <tr>
                <th>Tên vai trò</th>
                <th>Mô tả</th>
                <th>Số quyền</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {% for role in roles %}
              <tr>
                <td>{{ role.name }}</td>
                <td>{{ role.description|default:"-" }}</td>
                <td>{{ role.permissions.count }}</td>
                <td>
                  <a href="{% url 'permissions:role_edit' role.id %}" class="btn btn-primary btn-sm" title="Chỉnh sửa">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{% url 'permissions:role_delete' role.id %}" class="btn btn-danger btn-sm" title="Xóa">
                    <i class="fas fa-trash"></i>
                  </a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="4" class="text-center">Không tìm thấy vai trò nào.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      <!-- /.card-body -->
      <div class="card-footer clearfix">
        {% if roles.has_other_pages %}
        <ul class="pagination pagination-sm m-0 float-right">
          {% if roles.has_previous %}
          <li class="page-item"><a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">&laquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ roles.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">&lsaquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&lsaquo;</span></li>
          {% endif %}

          {% for i in roles.paginator.page_range %}
            {% if roles.number == i %}
              <li class="page-item active"><span class="page-link">{{ i }}</span></li>
            {% elif i > roles.number|add:'-3' and i < roles.number|add:'3' %}
              <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}

          {% if roles.has_next %}
          <li class="page-item"><a class="page-link" href="?page={{ roles.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">&rsaquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ roles.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">&raquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&rsaquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
          {% endif %}
        </ul>
        {% endif %}
      </div>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}
