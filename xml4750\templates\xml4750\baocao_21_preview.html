<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Báo cáo 21</title>
    <style>
        table {
            border: none;
            border-collapse: collapse;
        }
        .left { text-align: left; }
        .right { text-align: right; }
        .bold { font-weight: bold; }
        @page {
            size: A4 portrait;
            margin: 20mm 15mm 20mm 15mm;
        }

        /* Loại bỏ header/footer khi in */
        @media print {
            @page {
                margin: 10mm 5mm 10mm 5mm; /* top, right, bottom, left */
            }

            .report-wrapper {
                page-break-inside: avoid;
            }

            table.data-table {
                page-break-inside: auto;
                width: 100%;
            }

            table.data-table th, table.data-table td {
                font-size: 12px;
                padding: 4px;
                word-wrap: break-word;
                border: 1px solid #000;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            table {
                border: none;
                border-collapse: collapse;
                page-break-inside: avoid;
            }

            th, td {
                word-wrap: break-word;
                font-size: 12px;
                padding: 4px;
            }

            /* <PERSON><PERSON><PERSON> bảng khác không có border */
            .no-border-table td {
                border: none !important;
            }

            /* Ẩn about:blank và ngày giờ */
            header, footer {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="report-wrapper">
    <!-- Tiêu đề -->
    <table style="width: 100%; border: none; padding-top: 10px;">
        <tr>
            <td colspan="7" style="border: none;">Tên cơ sở y tế: <strong>Bệnh viện Quân Y 211</strong></td>
            <td colspan="2" style="text-align: right; font-weight: bold; border: none;">Mẫu số 21/BHYT</td>
        </tr>
        <tr>
            <td colspan="9" style="border: none;">Mã cơ sở y tế: <strong>64020</strong></td>
        </tr>
        <tr>
            <td colspan="9" style="text-align: center; font-size: 18px; font-weight: bold; border: none; padding-bottom: 10px; padding-top: 20px;">THỐNG KÊ DỊCH VỤ KỸ THUẬT THANH TOÁN BHYT</td>
        </tr>
        <tr>
            <td colspan="9" style="text-align: center; border: none; padding-bottom: 10px;">{{ thang_nam }}</td>
        </tr>
    </table>

    <!-- Bảng dữ liệu -->
    <table class="data-table" style="width: 100%; border-collapse: collapse; table-layout: fixed; border: 1px solid black;" border="1">
        <thead>
            <tr>
                <th style="width: 4%;" rowspan="2">STT</th>
                <th style="width: 15%;" rowspan="2">Mã DVKT</th>
                <th style="width: 20%;" rowspan="2">Tên DVKT</th>
                <th style="width: 8%;" rowspan="2">Tỷ lệ thanh toán</th>
                <th style="width: 18%;" colspan="2">Số lượng</th>
                <th style="width: 9%;" rowspan="2">Đơn giá</th>
                <th style="width: 13%;" rowspan="2">Thành tiền</th>
                <th style="width: 13%;" rowspan="2">Đề nghị quỹ BHYT thanh toán</th>
            </tr>
            <tr>
                <th style="width: 9%;">Ngoại trú</th>
                <th style="width: 9%;">Nội trú</th>
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
            <tr>
                <td style="text-align: center;">{{ forloop.counter }}</td>
                <td class="left">{{ item.ma_dvkt }}</td>
                <td class="left">{{ item.ten_dvkt }}</td>
                <td class="right">{{ item.ty_le }}</td>
                <td class="right money">{{ item.sl_ngoaitru }}</td>
                <td class="right money">{{ item.sl_noitru }}</td>
                <td class="right money">{{ item.don_gia|floatformat:0 }}</td>
                <td class="right money">{{ item.thanh_tien_bv|floatformat:0 }}</td>
                <td class="right money">{{ item.thanh_tien|floatformat:0 }}</td>
            </tr>
            {% endfor %}
            <tr class="bold">
                <td colspan="4" style="text-align: center;">TỔNG CỘNG</td>
                <td class="right money">{{ total_ngoai }}</td>
                <td class="right money">{{ total_noi }}</td>
                <td colspan="2" style="text-align: right;" class="money">{{ total_tien_bv|floatformat:0 }}</td>
                <td class="right money">{{ total_tien_bh|floatformat:0 }}</td>
            </tr>
        </tbody>
    </table>

    <br><br>
    <!-- Chữ ký -->
    <table style="width:100%; text-align:center;" class="no-border-table">
        <tr>
            <td><strong>Người lập biểu</strong><br>(Ký, họ tên)</td>
            <td><strong>Trưởng phòng</strong><br>(Ký, họ tên)</td>
            <td><strong>Giám đốc</strong><br>(Ký, họ tên, đóng dấu)</td>
        </tr>
    </table>
</div>
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/jquery/jquery.min.js"></script>
<script>
function formatNumberWithDot(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

$(document).ready(function () {
    $('.money').each(function () {
        let raw = $(this).text().replace(/[^0-9\-]/g, '');  // loại bỏ ký tự thừa
        if (raw) {
            let formatted = formatNumberWithDot(raw);
            $(this).text(formatted);
        }
    });
});
</script>

</body>
</html>
