{% extends 'layouts/base.html' %}

{% block title %}Quản lý phân quyền người dùng | Hospital Manager{% endblock %}

{% block page_title %}Quản lý phân quyền người dùng{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Quản lý phân quyền người dùng</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="row">
          <div class="col-md-6">
            <h3 class="card-title">Danh sách phân quyền người dùng</h3>
          </div>
          <div class="col-md-6">
            <form action="{% url 'permissions:user_role_list' %}" method="get" class="d-flex w-100">
              <input type="text" name="search" class="form-control float-right" placeholder="Tìm kiếm theo tên người dùng hoặc vai trò" value="{{ search_query|default:'' }}">
              <div class="input-group-append">
                <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body">
        <div class="mb-3">
          <a href="{% url 'permissions:user_role_create' %}" class="btn btn-primary">
            <i class="fas fa-plus mr-1"></i> Thêm phân quyền mới
          </a>
        </div>

        <!-- User Roles table -->
        <div class="table-responsive">
          <table class="table table-hover text-nowrap">
            <thead>
              <tr>
                <th>Người dùng</th>
                <th>Vai trò</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {% for user_role in user_roles %}
              <tr>
                <td>{{ user_role.user.username }} ({{ user_role.user.get_full_name }})</td>
                <td>{{ user_role.role.name }}</td>
                <td>
                  <a href="{% url 'permissions:user_role_edit' user_role.id %}" class="btn btn-primary btn-sm" title="Chỉnh sửa">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{% url 'permissions:user_role_delete' user_role.id %}" class="btn btn-danger btn-sm" title="Xóa">
                    <i class="fas fa-trash"></i>
                  </a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="3" class="text-center">Không tìm thấy phân quyền nào.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      <!-- /.card-body -->
      <div class="card-footer clearfix">
        {% if user_roles.has_other_pages %}
        <ul class="pagination pagination-sm m-0 float-right">
          {% if user_roles.has_previous %}
          <li class="page-item"><a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">&laquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ user_roles.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">&lsaquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&lsaquo;</span></li>
          {% endif %}

          {% for i in user_roles.paginator.page_range %}
            {% if user_roles.number == i %}
              <li class="page-item active"><span class="page-link">{{ i }}</span></li>
            {% elif i > user_roles.number|add:'-3' and i < user_roles.number|add:'3' %}
              <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}

          {% if user_roles.has_next %}
          <li class="page-item"><a class="page-link" href="?page={{ user_roles.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">&rsaquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ user_roles.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">&raquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&rsaquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
          {% endif %}
        </ul>
        {% endif %}
      </div>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}
