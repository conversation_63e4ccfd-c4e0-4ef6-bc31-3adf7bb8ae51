<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Báo cáo 19</title>
    <style>
        table {
            border: none;
            border-collapse: collapse;
        }
        .left { text-align: left; }
        .right { text-align: right; }
        .bold { font-weight: bold; }
        @page {
            size: A4 portrait;
            margin: 20mm 15mm 20mm 15mm;
        }

        /* Loại bỏ header/footer khi in */
        @media print {
            @page {
                margin: 10mm 5mm 10mm 5mm; /* top, right, bottom, left */
            }

            .report-wrapper {
                page-break-inside: avoid;
            }

            table.data-table {
                page-break-inside: auto;
                width: 100%;
            }

            table.data-table th, table.data-table td {
                font-size: 12px;
                padding: 4px;
                word-wrap: break-word;
                border: 1px solid #000;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            table {
                border: none;
                border-collapse: collapse;
                page-break-inside: avoid;
            }

            th, td {
                word-wrap: break-word;
                font-size: 12px;
                padding: 4px;
            }

            /* <PERSON><PERSON><PERSON> bảng kh<PERSON>c không có border */
            .no-border-table td {
                border: none !important;
            }

            /* Ẩn about:blank và ngày giờ */
            header, footer {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="report-wrapper">
    <table style="width: 100%; border: none; padding-top: 10px;">
        <tr>
            <td colspan="9" style="border: none;">Tên cơ sở y tế: <strong>Bệnh viện Quân Y 211</strong></td>
            <td colspan="2" style="text-align: right; font-weight: bold; border: none;">Mẫu số 19/BHYT</td>
        </tr>
        <tr>
            <td colspan="9" style="border: none;">Mã cơ sở y tế: <strong>64020</strong></td>
        </tr>
        <tr>
            <td colspan="11" style="text-align: center; font-size: 18px; font-weight: bold; border: none; padding-bottom: 10px; padding-top: 20px;">THỐNG KÊ VẬT TƯ Y TẾ THANH TOÁN BHYT</td>
        </tr>
        <tr>
            <td colspan="11" style="text-align: center; border: none; padding-bottom: 10px;">{{ thang_nam }}</td>
        </tr>
    </table>

    <table style="width: 100%; border-collapse: collapse; table-layout: fixed; border: 1px solid black;" border="1">
        <thead>
            <tr>
                <th style="width: 4%;" rowspan="2">STT</th>
                <th style="width: 12%;" rowspan="2">Mã số BYT</th>
                <th style="width: 12%;" rowspan="2">Tên VTYT</th>
                <th style="width: 12%;" rowspan="2">Tên thương mại</th>
                <th style="width: 10%;" rowspan="2">Quy cách</th>
                <th style="width: 5%;" rowspan="2">Đơn vị</th>
                <th style="width: 8%;" rowspan="2">Giá mua vào (đồng)</th>
                <th style="width: 14%;" colspan="2">Số lượng</th>
                <th style="width: 9%;" rowspan="2">Giá thanh toán</th>
                <th style="width: 9%;" rowspan="2">Thành tiền</th>
            </tr>
            <tr>
                <th style="width: 7%;">Ngoại trú</th>
                <th style="width: 7%;">Nội trú</th>
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
            <tr>
                <td style="text-align: center;">{{ forloop.counter }}</td>
                <td class="left">{{ item.ma_vtyt }}</td>
                <td class="left">{{ item.ten_vtyt }}</td>
                <td class="left">{{ item.ten_vtyt }}</td>
                <td class="left">{{ item.quy_cach }}</td>
                <td>{{ item.don_vi }}</td>
                <td class="right money">{{ item.gia_mua|floatformat:0 }}</td>
                <td class="right money">{{ item.sl_ngoaitru }}</td>
                <td class="right money">{{ item.sl_noitru }}</td>
                <td class="right money">{{ item.gia_mua|floatformat:0 }}</td>
                <td class="right money">{{ item.thanh_tien|floatformat:0 }}</td>
            </tr>
            {% endfor %}
            <tr class="bold">
                <td colspan="7" style="text-align: center;">TỔNG CỘNG</td>
                <td class="right money">{{ total_ngoai }}</td>
                <td class="right money">{{ total_noi }}</td>
                <td></td>
                <td class="right money">{{ total_tien|floatformat:0 }}</td>
            </tr>
        </tbody>
    </table>

    <br><br>

    <table style="width:100%; text-align:center;" class="no-border-table">
        <tr>
            <td><strong>Người lập biểu</strong><br>(Ký, họ tên)</td>
            <td><strong>Trưởng phòng</strong><br>(Ký, họ tên)</td>
            <td><strong>Giám đốc</strong><br>(Ký, họ tên, đóng dấu)</td>
        </tr>
    </table>
</div>
</body>
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/jquery/jquery.min.js"></script>
<script>
function formatNumberWithDot(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

$(document).ready(function () {
    $('.money').each(function () {
        let raw = $(this).text().replace(/[^0-9\-]/g, '');  // loại bỏ ký tự thừa
        if (raw) {
            let formatted = formatNumberWithDot(raw);
            $(this).text(formatted);
        }
    });
});
</script>
</html>
