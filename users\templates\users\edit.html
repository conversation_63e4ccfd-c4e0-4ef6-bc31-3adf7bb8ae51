{% extends 'layouts/base.html' %}

{% block title %}Chỉnh sửa người dùng | LAN Insight Guardian{% endblock %}

{% block extra_css %}
<!-- Select2 -->
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
{% endblock %}

{% block page_title %}Chỉnh sửa người dùng{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:list' %}">Quản lý ngườ<PERSON> dùng</a></li>
<li class="breadcrumb-item active">Chỉnh sửa người dùng</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="card card-primary">
    <!-- form start -->
    <form role="form" method="post" enctype="multipart/form-data">
      {% csrf_token %}
      <div class="card-body">
        {% if form.non_field_errors %}
        <div class="alert alert-danger">
          {% for error in form.non_field_errors %}
            {{ error }}
          {% endfor %}
        </div>
        {% endif %}

        <!-- Django messages will be handled by SweetAlert2 -->

        <div class="row">
          <!-- Thông tin cá nhân - 1 cột bên trái -->
          <div class="col-md-6">
            <div class="card card-outline card-primary">
              <div class="card-header">
                <h3 class="card-title">Thông tin cá nhân</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label for="{{ profile_form.profile_image.id_for_label }}">Ảnh đại diện</label>
                  {{ profile_form.profile_image }}
                  {% if profile_form.profile_image.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.profile_image.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <div class="row">
                  <div class="col-md-3 form-group">
                    <label for="id_username">Tên đăng nhập</label>
                    <input type="text" name="username" class="form-control {% if form.username.errors %}is-invalid{% endif %}" id="id_username" placeholder="Nhập tên đăng nhập" value="{{ form.username.value }}">
                    {% if form.username.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.username.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-5 form-group">
                    <label for="id_email">Email</label>
                    <input type="email" name="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" id="id_email" placeholder="Nhập email" value="{{ form.email.value }}">
                    {% if form.email.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.email.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.ma_loai_kcb.id_for_label }}">Mã loại KCB</label>
                    {{ profile_form.ma_loai_kcb }}
                    {% if profile_form.ma_loai_kcb.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.ma_loai_kcb.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4 form-group">
                    <label for="id_first_name">Họ và tên</label>
                    <input type="text" name="first_name" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" id="id_first_name" placeholder="Nhập tên" value="{{ form.first_name.value }}">
                    {% if form.first_name.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.first_name.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-3 form-group">
                    <label for="{{ profile_form.ma_bhxh.id_for_label }}">Mã BHXH</label>
                    {{ profile_form.ma_bhxh }}
                    {% if profile_form.ma_bhxh.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.ma_bhxh.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-2 form-group">
                    <label for="{{ profile_form.gioi_tinh.id_for_label }}">Giới tính</label>
                    {{ profile_form.gioi_tinh }}
                    {% if profile_form.gioi_tinh.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.gioi_tinh.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-3 form-group">
                    <label for="{{ profile_form.phone_number.id_for_label }}">Số điện thoại</label>
                    {{ profile_form.phone_number }}
                    {% if profile_form.phone_number.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.phone_number.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.practice_certificate.id_for_label }}">Chứng chỉ hành nghề</label>
                    {{ profile_form.practice_certificate }}
                    {% if profile_form.practice_certificate.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.practice_certificate.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.ngay_cap_cchn.id_for_label }}">Ngày cấp CCHN</label>
                    {{ profile_form.ngay_cap_cchn }}
                    {% if profile_form.ngay_cap_cchn.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.ngay_cap_cchn.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.noi_cap_cchn.id_for_label }}">Nơi cấp CCHN</label>
                    {{ profile_form.noi_cap_cchn }}
                    {% if profile_form.noi_cap_cchn.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.noi_cap_cchn.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-3 form-group">
                    <label for="{{ profile_form.practice_certificate.id_for_label }}">Mã khoa</label>
                    {{ profile_form.ma_khoa }}
                    {% if profile_form.ma_khoa.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.ma_khoa.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-5 form-group">
                    <label for="{{ profile_form.department.id_for_label }}">Khoa làm việc</label>
                    {{ profile_form.department }}
                    {% if profile_form.department.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.department.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.position.id_for_label }}">Chức vụ</label>
                    {{ profile_form.position }}
                    {% if profile_form.position.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.position.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 form-group">
                    <label for="{{ profile_form.vi_tri.id_for_label }}">Vị trí</label>
                    {{ profile_form.vi_tri }}
                    {% if profile_form.vi_tri.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.vi_tri.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-6 form-group">
                    <label for="{{ profile_form.chuc_danh_nn.id_for_label }}">Chức danh nghề nghiệp</label>
                    {{ profile_form.chuc_danh_nn }}
                    {% if profile_form.chuc_danh_nn.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.chuc_danh_nn.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="form-group">
                  <label for="{{ profile_form.pham_vi_cm.id_for_label }}">Phạm vi chuyên môn</label>
                  {{ profile_form.pham_vi_cm }}
                  {% if profile_form.pham_vi_cm.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.pham_vi_cm.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <div class="form-group">
                  <label for="{{ profile_form.pham_vi_cm_bs.id_for_label }}">Phạm vi chuyên môn bác sĩ</label>
                  {{ profile_form.pham_vi_cm_bs }}
                  {% if profile_form.pham_vi_cm_bs.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.pham_vi_cm_bs.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <div class="row">
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.dvkt_khac.id_for_label }}">DVKT khác</label>
                    {{ profile_form.dvkt_khac }}
                    {% if profile_form.dvkt_khac.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.pham_vi_cm_bs.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                  
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.vb_phan_cong.id_for_label }}">Văn bản phân công</label>
                    {{ profile_form.vb_phan_cong }}
                    {% if profile_form.vb_phan_cong.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.vb_phan_cong.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.thoi_gian_dk.id_for_label }}">Thời gian đăng kí</label>
                    {{ profile_form.thoi_gian_dk }}
                    {% if profile_form.thoi_gian_dk.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.thoi_gian_dk.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="form-group">
                  <label for="{{ profile_form.thoi_gian_ngay.id_for_label }}">Thời gian ngày</label>
                  {{ profile_form.thoi_gian_ngay }}
                  {% if profile_form.thoi_gian_ngay.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.thoi_gian_ngay.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <div class="row">
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.thoi_gian_tuan.id_for_label }}">Thời gian tuần</label>
                    {{ profile_form.thoi_gian_tuan }}
                    {% if profile_form.thoi_gian_tuan.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.thoi_gian_tuan.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.cskcb_khac.id_for_label }}">Cơ sở KCB khác</label>
                    {{ profile_form.cskcb_khac }}
                    {% if profile_form.cskcb_khac.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.cskcb_khac.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.cskcb_cgkt.id_for_label }}">Cơ sở KCB chuyển giao kĩ thuật</label>
                    {{ profile_form.cskcb_cgkt }}
                    {% if profile_form.cskcb_cgkt.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.cskcb_cgkt.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.qd_cgkt.id_for_label }}">Quyết định chuyển giao kĩ thuật</label>
                    {{ profile_form.qd_cgkt }}
                    {% if profile_form.qd_cgkt.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.qd_cgkt.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.tu_ngay.id_for_label }}">Từ ngày</label>
                    {{ profile_form.tu_ngay }}
                    {% if profile_form.tu_ngay.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.tu_ngay.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                  <div class="col-md-4 form-group">
                    <label for="{{ profile_form.den_ngay.id_for_label }}">Đến ngày</label>
                    {{ profile_form.den_ngay }}
                    {% if profile_form.den_ngay.errors %}
                    <div class="invalid-feedback">
                      {% for error in profile_form.den_ngay.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>

                <div class="form-group">
                  <label>Trạng thái</label>
                  <div class="custom-control custom-checkbox">
                    <input class="custom-control-input" type="checkbox" id="id_is_active" name="is_active" {% if form.is_active.value %}checked{% endif %}>
                    <label for="id_is_active" class="custom-control-label">Hoạt động</label>
                  </div>
                </div>

                <div class="form-group">
                  <a href="{% url 'users:change_password' user_id %}" class="btn btn-warning">Đổi mật khẩu</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Phân quyền - 1 cột bên phải -->
          <div class="col-md-6">
            <div class="card card-outline card-primary">
              <div class="card-header">
                <h3 class="card-title">Phân quyền người dùng</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label>Vai trò người dùng</label>
                  <div class="input-group mb-3">
                    <select id="role_selector" name="user_role" class="form-control select2">
                      <option value="">-- Chọn vai trò --</option>
                      {% for role in available_roles %}
                      <option value="{{ role.id }}" {% if user_roles %}{% for user_role in user_roles %}{% if user_role.role.id == role.id %}selected{% endif %}{% endfor %}{% endif %}>
                        {{ role.name }} - {{ role.description|default:"" }}
                      </option>
                      {% endfor %}
                    </select>
                  </div>
                  <small class="form-text text-muted">Chọn vai trò để tự động chọn các quyền tương ứng.</small>
                </div>

                <div class="form-group">
                  <label>Quyền chi tiết</label>
                  <div class="card">
                    <div class="card-header">
                      <h3 class="card-title">Chọn quyền cho người dùng</h3>
                      <div class="card-tools">
                        <div class="form-check">
                          <input type="checkbox" class="form-check-input" id="check_all_permissions">
                          <label class="form-check-label" for="check_all_permissions">
                            <strong>Chọn tất cả quyền</strong>
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                      <div class="row">
                        {% for group_name, permissions in grouped_permissions.items %}
                        <div class="col-md-12 mb-3 permission-group" data-group="{{ group_name|slugify }}">
                          <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>{{ group_name }}</h5>
                            <div class="form-check">
                              <input type="checkbox" class="form-check-input check-group" id="check_group_{{ group_name|slugify }}" data-group="{{ group_name|slugify }}">
                              <label class="form-check-label" for="check_group_{{ group_name|slugify }}">
                                <strong>Chọn tất cả</strong>
                              </label>
                            </div>
                          </div>
                          <div class="row">
                            {% for permission in permissions %}
                            <div class="col-md-6">
                              <div class="form-check">
                                <input type="checkbox" class="form-check-input permission-checkbox" id="permission_{{ permission.id }}"
                                        name="user_permissions" value="{{ permission.id }}" data-group="{{ group_name|slugify }}"
                                        {% if permission.id in user_permissions %}checked{% endif %}>
                                <label class="form-check-label" for="permission_{{ permission.id }}">
                                  {{ permission.name_vi|default:permission.name }}
                                </label>
                              </div>
                            </div>
                            {% endfor %}
                          </div>
                        </div>
                        {% empty %}
                        <div class="col-md-12">
                          <p class="text-muted">Không có quyền nào được định nghĩa.</p>
                        </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
      <!-- /.card-body -->

      <div class="card-footer">
        <button type="submit" class="btn btn-primary" id="save-button">
          <i class="fas fa-save"></i> Lưu thay đổi
        </button>
        <a href="{% url 'users:list' %}" class="btn btn-default">
          <i class="fas fa-times"></i> Hủy
        </a>
      </div>
    </form>
  </div>
  <!-- /.card -->
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js"></script>
<script>
  $(function () {
    $('.select2').select2({
      theme: 'bootstrap4'  // nếu bạn dùng AdminLTE 3 + Bootstrap 4
    });

    // Xử lý nút lưu thay đổi
    $('#save-button').on('click', function(e) {
      e.preventDefault();

      // Hiển thị thông báo xác nhận
      Swal.fire({
        title: 'Xác nhận lưu thay đổi',
        text: 'Bạn có chắc chắn muốn lưu thay đổi cho người dùng này?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Lưu thay đổi',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Hiển thị loading
          Swal.fire({
            title: 'Đang xử lý...',
            html: 'Vui lòng đợi trong giây lát',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });

          // Submit form
          $(this).closest('form').submit();
        }
      });
    });

    // Tạo đối tượng JavaScript từ dữ liệu Python
    var rolesPermissions = {
      {% for role_id, permissions in roles_permissions.items %}
        "{{ role_id }}": {{ permissions|safe }},
      {% endfor %}
    };

    console.log('Dữ liệu quyền của vai trò:', rolesPermissions);

    // Xử lý sự kiện khi chọn vai trò
    $('#role_selector').on('change', function() {
      var roleId = $(this).val();
      console.log('Vai trò được chọn:', roleId);

      // Bỏ chọn tất cả các quyền hiện tại
      $('.permission-checkbox').prop('checked', false);

      if (!roleId) {
        // Nếu không chọn vai trò nào, bỏ chọn tất cả các quyền
        updateGroupCheckboxes();
        return;
      }

      // Lấy danh sách quyền của vai trò từ dữ liệu đã có
      var permissions = rolesPermissions[roleId];
      console.log('Quyền của vai trò:', permissions);

      if (permissions && permissions.length > 0) {
        console.log('Số lượng quyền:', permissions.length);
        console.log('Số lượng checkbox quyền:', $('.permission-checkbox').length);

        // Chọn các quyền của vai trò
        permissions.forEach(function(permissionId) {
          var checkboxId = '#permission_' + permissionId;
          var checkbox = $(checkboxId);
          console.log('Chọn quyền:', checkboxId, 'Tồn tại:', checkbox.length > 0);
          checkbox.prop('checked', true);
        });

        // Cập nhật trạng thái của các checkbox nhóm
        updateGroupCheckboxes();
      } else {
        console.log('Không có quyền nào cho vai trò này hoặc mảng rỗng');
      }
    });

    // Xử lý checkbox "Chọn tất cả quyền"
    $('#check_all_permissions').on('change', function() {
      var isChecked = $(this).prop('checked');
      $('.permission-checkbox').prop('checked', isChecked);
      $('.check-group').prop('checked', isChecked);
    });

    // Xử lý checkbox "Chọn tất cả" cho từng nhóm
    $('.check-group').on('change', function() {
      var groupName = $(this).data('group');
      var isChecked = $(this).prop('checked');
      $('.permission-checkbox[data-group="' + groupName + '"]').prop('checked', isChecked);
      updateMainCheckbox();
    });

    // Xử lý khi checkbox quyền thay đổi
    $('.permission-checkbox').on('change', function() {
      updateGroupCheckboxes();
      updateMainCheckbox();
    });

    // Cập nhật trạng thái của các checkbox nhóm
    function updateGroupCheckboxes() {
      $('.check-group').each(function() {
        var groupName = $(this).data('group');
        var totalCheckboxes = $('.permission-checkbox[data-group="' + groupName + '"]').length;
        var checkedCheckboxes = $('.permission-checkbox[data-group="' + groupName + '"]:checked').length;
        $(this).prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
      });
    }

    // Cập nhật trạng thái của checkbox "Chọn tất cả quyền"
    function updateMainCheckbox() {
      var totalCheckboxes = $('.permission-checkbox').length;
      var checkedCheckboxes = $('.permission-checkbox:checked').length;
      $('#check_all_permissions').prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
    }

    // Khởi tạo trạng thái ban đầu của các checkbox nhóm
    updateGroupCheckboxes();
    updateMainCheckbox();
  });
</script>
{% endblock %}