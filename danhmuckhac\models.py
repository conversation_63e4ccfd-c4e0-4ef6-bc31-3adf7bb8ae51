from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _

class BaseModel(models.Model):
    """Base model with common fields"""
    hieu_luc = models.BooleanField(default=True, verbose_name=_("<PERSON><PERSON><PERSON> lực"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('<PERSON><PERSON><PERSON> tạo'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('Cập nhật lần cuối'))

    class Meta:
        abstract = True

# danh mục tỉnh
class Tinh(BaseModel):
    """Model cho Tỉnh/Thành phố"""
    ma_tinh = models.CharField(
        max_length=2,
        unique=True,
        validators=[RegexValidator(r'^\d{2}$', 'Mã tỉnh phải có 2 chữ số')],
        verbose_name="Mã tỉnh"
    )
    ten_tinh = models.Char<PERSON>ield(max_length=100, verbose_name="Tên tỉnh")

    class Meta:
        verbose_name = "Tỉnh/Thành phố"
        verbose_name_plural = "Tỉnh/Thành phố"
        ordering = ['ma_tinh']

    def __str__(self):
        return f"{self.ma_tinh} - {self.ten_tinh}"

#  Danh mục quận huyện
class QuanHuyen(BaseModel):
    """Model cho Quận/Huyện"""
    ma_quan_huyen = models.CharField(
        max_length=3,
        unique=True,
        validators=[RegexValidator(r'^\d{3}$', 'Mã quận huyện phải có 3 chữ số')],
        verbose_name="Mã quận huyện"
    )
    ten_quan_huyen = models.CharField(max_length=100, verbose_name="Tên quận huyện")
    ma_tinh = models.ForeignKey(
        Tinh,
        on_delete=models.CASCADE,
        to_field='ma_tinh',
        db_column='ma_tinh',
        verbose_name="Tỉnh/Thành phố"
    )

    class Meta:
        verbose_name = "Quận/Huyện"
        verbose_name_plural = "Quận/Huyện"
        ordering = ['ma_tinh', 'ma_quan_huyen']
        unique_together = ['ma_quan_huyen', 'ma_tinh']

    def __str__(self):
        return f"{self.ma_quan_huyen} - {self.ten_quan_huyen}"

# Danh mục xã phường
class XaPhuong(BaseModel):
    """Model cho Xã/Phường"""
    ma_xa_phuong = models.CharField(
        max_length=5,
        unique=True,
        validators=[RegexValidator(r'^\d{5}$', 'Mã xã phường phải có 5 chữ số')],
        verbose_name="Mã xã phường"
    )
    ten_xa_phuong = models.CharField(max_length=100, verbose_name="Tên xã phường")
    ma_quan_huyen = models.ForeignKey(
        QuanHuyen,
        on_delete=models.CASCADE,
        to_field='ma_quan_huyen',
        db_column='ma_quan_huyen',
        verbose_name="Quận/Huyện"
    )
    ma_tinh = models.ForeignKey(
        Tinh,
        on_delete=models.CASCADE,
        to_field='ma_tinh',
        db_column='ma_tinh',
        verbose_name="Tỉnh/Thành phố"
    )

    class Meta:
        verbose_name = "Xã/Phường"
        verbose_name_plural = "Xã/Phường"
        ordering = ['ma_tinh', 'ma_quan_huyen', 'ma_xa_phuong']
        unique_together = ['ma_xa_phuong', 'ma_quan_huyen', 'ma_tinh']

    def __str__(self):
        return f"{self.ma_xa_phuong} - {self.ten_xa_phuong}"

# Danh mục nơi đăng kí ban đầu
class NoiDangKiBanDau(BaseModel):
    """Model cho nơi đăng kí ban đầu"""
    ma = models.CharField(
        max_length=5,
        unique=True,
        verbose_name="Mã nơi đăng kí ban đầu"
    )
    ten = models.CharField(
        max_length=100,
        verbose_name="Tên nơi đăng kí ban đầu"
    )

    tuyen_cmkt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Tuyến CMKT"
    )

    hang_benh_vien = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Hạng bệnh viện"
    )

    cap_cmkt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Cấp CMKT"
    )

    diem = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="Điểm"
    )

    dia_chi = models.CharField(
        max_length=250,
        null=True,
        blank=True,
        verbose_name="Địa chỉ"
    )

    class Meta:
        verbose_name = "Nơi đăng kí ban đầu"
        verbose_name_plural = "Nơi đăng kí ban đầu"
        ordering = ['ma']

    def __str__(self):
        return f"{self.ma} - {self.ten}"


class PhamViChuyenMon(BaseModel):
    """Model cho Phạm vi chuyên môn"""
    ma_pham_vi = models.CharField(
        max_length=250,
        unique=True,
        verbose_name="Mã phạm vi"
    )
    ten_chuyen_khoa = models.CharField(max_length=100, verbose_name="Tên chuyên khoa")

    class Meta:
        verbose_name = "Phạm vi chuyên môn"
        verbose_name_plural = "Phạm vi chuyên môn"
        ordering = ['ma_pham_vi']

    def __str__(self):
        return f"{self.ma_pham_vi} - {self.ten_chuyen_khoa}"