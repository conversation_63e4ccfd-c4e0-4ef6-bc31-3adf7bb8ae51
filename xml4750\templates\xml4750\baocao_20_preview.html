<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Báo cáo 20</title>
    <style>
        table {
            border: none;
            border-collapse: collapse;
        }
        .left { text-align: left; }
        .right { text-align: right; }
        .bold { font-weight: bold; }
        @page {
            size: A4 portrait;
            margin: 20mm 15mm 20mm 15mm;
        }

        /* Loại bỏ header/footer khi in */
        @media print {
            @page {
                margin: 10mm 5mm 10mm 5mm; /* top, right, bottom, left */
            }

            .report-wrapper {
                page-break-inside: avoid;
            }

            table.data-table {
                page-break-inside: auto;
                width: 100%;
            }

            table.data-table th, table.data-table td {
                font-size: 12px;
                padding: 4px;
                word-wrap: break-word;
                border: 1px solid #000;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            table {
                border: none;
                border-collapse: collapse;
                page-break-inside: avoid;
            }

            th, td {
                word-wrap: break-word;
                font-size: 12px;
                padding: 4px;
            }

            /* <PERSON><PERSON><PERSON> bảng khác không có border */
            .no-border-table td {
                border: none !important;
            }

            /* Ẩn about:blank và ngày giờ */
            header, footer {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="report-wrapper">
    <!-- Tiêu đề -->
    <table style="width: 100%; border: none; padding-top: 10px;">
        <tr>
            <td colspan="9" style="border: none;">Tên cơ sở y tế: <strong>Bệnh viện Quân Y 211</strong></td>
            <td colspan="3" style="text-align: right; font-weight: bold; border: none;">Mẫu số 20/BHYT</td>
        </tr>
        <tr>
            <td colspan="9" style="border: none;">Mã cơ sở y tế: <strong>64020</strong></td>
        </tr>
        <tr>
            <td colspan="12" style="text-align: center; font-size: 18px; font-weight: bold; border: none; padding-bottom: 10px; padding-top: 20px;">THỐNG KÊ THUỐC THANH TOÁN BHYT</td>
        </tr>
        <tr>
            <td colspan="12" style="text-align: center; border: none; padding-bottom: 10px;">{{ thang_nam }}</td>
        </tr>
    </table>

    <!-- Bảng dữ liệu -->
    <table class="data-table" style="width: 100%; border-collapse: collapse; table-layout: fixed; border: 1px solid black;" border="1">
        <thead>
            <tr>
                <th style="width: 4%;" rowspan="2">STT</th>
                <th style="width: 10%;" rowspan="2">Mã thuốc</th>
                <th style="width: 11%;" rowspan="2">Tên hoạt chất</th>
                <th style="width: 11%;" rowspan="2">Tên thuốc thành phẩm</th>
                <th style="width: 7%;" rowspan="2">Đường dùng</th>
                <th style="width: 9%;" rowspan="2">Hàm lượng</th>
                <th style="width: 8%;" rowspan="2">Số đăng kí</th>
                <th style="width: 5%;" rowspan="2">Đơn vị tính</th>
                <th style="width: 16%;" colspan="2">Số lượng</th>
                <th style="width: 9%;" rowspan="2">Đơn giá</th>
                <th style="width: 10%;" rowspan="2">Thành tiền</th>
            </tr>
            <tr>
                <th style="width: 8%;">Ngoại trú</th>
                <th style="width: 8%;">Nội trú</th>
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
            <tr>
                <td style="text-align: center;">{{ forloop.counter }}</td>
                <td class="left">{{ item.ma_thuoc }}</td>
                <td class="left">{{ item.ten_hoatchat }}</td>
                <td class="left">{{ item.ten_thuoc }}</td>
                <td class="left">{% if item.duong_dung is not none %}{{ item.duong_dung }}{% endif %}</td>
                <td class="left">{{ item.ham_luong }}</td>
                <td class="left">{{ item.so_dky }}</td>
                <td>{{ item.don_vi }}</td>
                <td class="right money">{{ item.sl_ngoaitru }}</td>
                <td class="right money">{{ item.sl_noitru }}</td>
                <td class="right money">{{ item.don_gia|floatformat:0 }}</td>
                <td class="right money">{{ item.thanh_tien|floatformat:0 }}</td>
            </tr>
            {% endfor %}
            <tr class="bold">
                <td colspan="8" style="text-align: center;">TỔNG CỘNG</td>
                <td class="right money">{{ total_ngoai }}</td>
                <td class="right money">{{ total_noi }}</td>
                <td></td>
                <td class="right money">{{ total_tien|floatformat:0 }}</td>
            </tr>
        </tbody>
    </table>

    <br><br>
    <!-- Chữ ký -->
    <table style="width:100%; text-align:center;" class="no-border-table">
        <tr>
            <td><strong>Người lập biểu</strong><br>(Ký, họ tên)</td>
            <td><strong>Trưởng phòng</strong><br>(Ký, họ tên)</td>
            <td><strong>Giám đốc</strong><br>(Ký, họ tên, đóng dấu)</td>
        </tr>
    </table>
</div>
</body>
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/jquery/jquery.min.js"></script>
<script>
function formatNumberWithDot(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

$(document).ready(function () {
    $('.money').each(function () {
        let raw = $(this).text().replace(/[^0-9\-]/g, '');  // loại bỏ ký tự thừa
        if (raw) {
            let formatted = formatNumberWithDot(raw);
            $(this).text(formatted);
        }
    });
});
</script>
</html>
