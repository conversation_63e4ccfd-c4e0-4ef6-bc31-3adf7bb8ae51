<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">

  <title>LAN Insight Guardian | Đăng nhập</title>

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/dist/css/adminlte.min.css">
  <!-- Google Font: Source Sans Pro -->
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    <a href="{% url 'home' %}"><b>LAN</b> Insight Guardian</a>
  </div>
  <!-- /.login-logo -->
  <div class="card">
    <div class="card-body login-card-body">
      <p class="login-box-msg">Đăng nhập để bắt đầu phiên làm việc</p>

      <form action="{% url 'users:login' %}" method="post">
        {% csrf_token %}
        {% if form.non_field_errors %}
        <div class="alert alert-danger">
          {% for error in form.non_field_errors %}
            {{ error }}
          {% endfor %}
        </div>
        {% endif %}

        <div class="input-group mb-3">
          <input type="text" name="username" class="form-control {% if form.username.errors %}is-invalid{% endif %}" placeholder="Tên đăng nhập" required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
          {% if form.username.errors %}
          <div class="invalid-feedback">
            {% for error in form.username.errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
        <div class="input-group mb-3">
          <input type="password" name="password" class="form-control {% if form.password.errors %}is-invalid{% endif %}" placeholder="Mật khẩu" required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>
          {% if form.password.errors %}
          <div class="invalid-feedback">
            {% for error in form.password.errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
        <div class="row">
          <div class="col-8">
            <div class="icheck-primary">
              <input type="checkbox" id="remember" name="remember">
              <label for="remember">
                Ghi nhớ đăng nhập
              </label>
            </div>
          </div>
          <!-- /.col -->
          <div class="col-4">
            <button type="submit" class="btn btn-primary btn-block">Đăng nhập</button>
          </div>
          <!-- /.col -->
        </div>
      </form>

      <p class="mb-1">
        <a href="{% url 'users:password_reset' %}">Quên mật khẩu</a>
      </p>
    </div>
    <!-- /.login-card-body -->
  </div>
</div>
<!-- /.login-box -->

<!-- jQuery -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/dist/js/adminlte.min.js"></script>

</body>
</html>
