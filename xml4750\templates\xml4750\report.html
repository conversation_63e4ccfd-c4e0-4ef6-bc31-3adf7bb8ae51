{% extends 'layouts/base.html' %}
{% load static %}
{% block title %}<PERSON><PERSON><PERSON> c<PERSON>o tổng hợp{% endblock %}

{% block page_title %}B<PERSON><PERSON> c<PERSON>o tổng hợp{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
{% endblock %}

{% block content %}
    <!-- Thực hiện việc hiển thị báo cáo tổng hợp ở đây gồm
        Xuất dữ liệu ra file excel gồm các checkbox: <PERSON><PERSON><PERSON> c<PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 3360, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 102, <PERSON><PERSON><PERSON> <PERSON><PERSON> 19, <PERSON><PERSON><PERSON> 20, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 21
        Dropdown Chọn đối tượng: Tất cả, BHXH VN, BHXH BQP
        Dropdown chọn khoảng thời gian lấy: Giai đoạn, ngày, tháng, quý, năm
        Chọn khoảng thời gian: Từ ngày, đến ngày nếu Dropdown là giai đoạn, ngày nếu Dropdown là ngày, tháng nếu Dropdown là tháng, quý nếu Dropdown là quý, năm nếu Dropdown là năm
        Button: Xuất dữ liệu ra excel, Xem trước khi in
    -->
<div class="card card-primary card-outline shadow-sm">
    <div class="card-header">
        <h5 class="card-title m-0"><i class="fas fa-file-alt"></i> Tạo Báo Cáo</h5>
    </div>
    <div class="card-body">
        <form method="post" id="reportForm">
            {% csrf_token %}

            <!-- Chọn loại báo cáo -->
            <div class="form-group clearfix mb-4 d-flex justify-content-center">
                <label class="form-label fw-bold">Chọn loại báo cáo:</label>
                <div class="icheck-primary icheck-inline pr-4 pl-4">
                    <input type="radio" name="report_type" value="bao_cao_3360" id="baoCao3360">
                    <label for="baoCao3360">Báo cáo 3360</label>
                </div>
                <div class="icheck-primary icheck-inline pr-4">
                    <input type="radio" name="report_type" value="bao_cao_102" id="baoCao102">
                    <label for="baoCao102">Báo cáo 102</label>
                </div>
                <div class="icheck-primary d-inline pr-4">
                    <input type="radio" name="report_type" value="bao_cao_19" id="baoCao19">
                    <label for="baoCao19">Báo cáo 19</label>
                </div>
                <div class="icheck-primary d-inline pr-4">
                    <input type="radio" name="report_type" value="bao_cao_20" id="baoCao20">
                    <label for="baoCao20">Báo cáo 20</label>
                </div>
                <div class="icheck-primary d-inline pr-4">
                    <input type="radio" name="report_type" value="bao_cao_21" id="baoCao21">
                    <label for="baoCao21">Báo cáo 21</label>
                </div>
            </div>

            <!-- Thông tin lọc -->
            <div class="row justify-content-center">
                <div class="col-md-2 form-group">
                    <label class="form-label fw-bold">Đối tượng:</label>
                    <select class="form-control w-50" id="doi_tuong" name="doi_tuong">
                        <option value="all">Tất cả</option>
                        <option value="bhxh_vn">BHXH VN</option>
                        <option value="bhxh_bqp">BHXH BQP</option>
                    </select>
                </div>

                <div class="col-md-3 form-group">
                    <label class="form-label fw-bold">Kiểu thời gian:</label>
                    <select class="form-control w-50" id="thoi_gian_type" name="thoi_gian_type" onchange="handleTimeTypeChange()">
                        <option value="0">Giai đoạn</option>
                        <option value="1">Ngày</option>
                        <option value="2" selected>Tháng</option>
                        <option value="3">Quý</option>
                        <option value="4">Năm</option>
                    </select>
                </div>

                <div class="col-md-4 d-inline">
                    <!-- Giai đoạn -->
                    <div class="row g-2 d-none" id="dateRange">
                        <div class="col d-inline">
                            <label class="form-label">Từ ngày</label>
                            <input type="date" class="form-control" id="ngay_bat_dau" name="ngay_bat_dau">
                        </div>
                        <div class="col">
                            <label class="form-label">Đến ngày</label>
                            <input type="date" class="form-control" id="ngay_ket_thuc" name="ngay_ket_thuc">
                        </div>
                    </div>

                    <!-- Ngày -->
                    <div class="d-none" id="singleDate">
                        <label class="form-label">Chọn ngày</label>
                        <input type="date" class="form-control" id="single_date" name="single_date">
                    </div>

                    <!-- Tháng -->
                    <div class="d-none row" id="monthPicker">
                        <div class="col-md-6">
                            <label class="form-label">Chọn tháng</label>
                            <select class="form-select select2" id="thang_value" name="thang_value" style="width: 100%;"></select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Chọn năm</label>
                            <select class="form-select select2" id="thang_nam" name="thang_nam" style="width: 100%;"></select>
                        </div>
                    </div>

                    <!-- Quý -->
                    <div class="row d-none" id="quarterPicker">
                        <div class="col-md-6">
                            <label class="form-label">Chọn quý</label>
                            <select class="form-select select2" id="quy_value" name="quy_value">
                                <option value="1">Quý 1</option>
                                <option value="2">Quý 2</option>
                                <option value="3">Quý 3</option>
                                <option value="4">Quý 4</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Chọn năm</label>
                            <input type="number" class="form-control" id="quy_nam" name="quy_nam" min="2000" max="2100" value="2024">
                        </div>
                    </div>

                    <!-- Năm -->
                    <div class="d-none" id="yearPicker">
                        <label class="form-label">Chọn năm</label>
                        <input type="number" class="form-control" id="nam_value" name="nam_value" min="2000" max="2100">
                    </div>
                </div>
            </div>

            <!-- Nút hành động -->
            <div class="text-center pt-5">
                <button type="submit" class="btn btn-success me-2 pr-4" onclick="exportToExcel(event)">
                    <i class="fas fa-file-excel"></i> Xuất Excel
                </button>
                <button type="button" class="btn btn-primary" onclick="previewReport(event)">
                    <i class="fas fa-print"></i> Xem trước khi in
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static "AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js" %}"></script>
<script>
    $(document).ready(function () {
        const today = new Date();

        // Danh sách tháng (1–12)
        const months = Array.from({ length: 12 }, (_, i) => {
            const value = (i + 1).toString().padStart(2, '0');
            return { id: value, text: `Tháng ${value}` };
        });

        // Danh sách năm (2020–2030)
        const years = Array.from({ length: 100 }, (_, i) => {
            const year = 2015 + i;
            return { id: year, text: year.toString() };
        });

        // Thêm option vào select tháng và năm
        $('#thang_value').select2({
            data: months,
            theme: 'bootstrap4',
            placeholder: 'Chọn tháng'
        });

        $('#thang_nam').select2({
            data: years,
            theme: 'bootstrap4',
            placeholder: 'Chọn năm'
        });

        // Format: YYYY-MM-DD
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const todayStr = `${yyyy}-${mm}-${dd}`;

        // Set default for date inputs
        $('#ngay_bat_dau').val(todayStr);
        $('#ngay_ket_thuc').val(todayStr);
        $('#single_date').val(todayStr);

        // Set default for month input
        $('#month').val(`${yyyy}-${mm}`);

        // Set default for year input (and quarter)
        $('#nam_value').val(yyyy);
        $('input[name="nam"]').val(yyyy);

        handleTimeTypeChange(); // đảm bảo đúng layout khi reload
        $('#thoi_gian_type').on('change', handleTimeTypeChange);

        $('.select2').select2({
            theme: 'bootstrap4'  // nếu bạn dùng AdminLTE 3 + Bootstrap 4
        });

        $('#thang_value').val(mm).trigger('change');
        $('#thang_nam').val(yyyy).trigger('change');
    });

    function handleTimeTypeChange() {
        const type = $('#thoi_gian_type').val();

        // Ẩn tất cả
        $('#dateRange').addClass('d-none');
        $('#singleDate').addClass('d-none');
        $('#monthPicker').addClass('d-none');
        $('#quarterPicker').addClass('d-none');
        $('#yearPicker').addClass('d-none');

        // Hiện phần tương ứng
        if (type === '0') {
            $('#dateRange').removeClass('d-none');
        } else if (type === '1') {
            $('#singleDate').removeClass('d-none');
        } else if (type === '2') {
            $('#monthPicker').removeClass('d-none');
        } else if (type === '3') {
            $('#quarterPicker').removeClass('d-none');
        } else if (type === '4') {
            $('#yearPicker').removeClass('d-none');
        }
    }

    function previewReport(event) {
        if (event) event.preventDefault();

        const reportType = $('input[name="report_type"]:checked').val();
        const doiTuong = $('#doi_tuong').val();
        const thoiGianType = $('#thoi_gian_type').val();

        if (!reportType) {
            alert('Vui lòng chọn loại báo cáo');
            return;
        }

        const formData = new FormData();
        formData.append('report_type', reportType);
        formData.append('doi_tuong', doiTuong);
        formData.append('loai_thoi_gian', thoiGianType);

        let ngayBatDau = '';
        let ngayKetThuc = '';

        if (thoiGianType === '0' || thoiGianType === '1') {
            const tuNgay = $('#ngay_bat_dau').val();
            const denNgay = $('#ngay_ket_thuc').val();

            if (!tuNgay || (!denNgay && thoiGianType === 'giai_doan')) {
                alert('Vui lòng chọn đầy đủ ngày');
                return;
            }

            if (thoiGianType === '1') {
                // Nếu chỉ chọn 1 ngày
                ngayBatDau = tuNgay + ' 00:00:00';
                ngayKetThuc = tuNgay + ' 23:59:59';
            } else {
                ngayBatDau = tuNgay + ' 00:00:00';
                ngayKetThuc = denNgay + ' 23:59:59';
            }

        } else if (thoiGianType === '2') {
            const thang = parseInt($('#thang_value').val());
            const nam = parseInt($('#thang_nam').val());

            if (!thang || !nam) {
                alert('Vui lòng chọn tháng và năm');
                return;
            }

            const firstDay = new Date(nam, thang - 1, 1);
            const lastDay = new Date(nam, thang, 0);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);

        } else if (thoiGianType === '3') {
            const quy = parseInt($('#quy_value').val());
            const nam = parseInt($('#quy_nam').val());

            if (!quy || !nam) {
                alert('Vui lòng chọn quý và năm');
                return;
            }

            const quyBatDau = (quy - 1) * 3;
            const firstDay = new Date(nam, quyBatDau, 1);
            const lastDay = new Date(nam, quyBatDau + 3, 0);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);

        } else if (thoiGianType === '4') {
            const nam = parseInt($('#nam_value').val());
            if (!nam) {
                alert('Vui lòng nhập năm');
                return;
            }

            const firstDay = new Date(nam, 0, 1);
            const lastDay = new Date(nam, 11, 31);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);
        }

        formData.append('ngay_bat_dau', ngayBatDau);
        formData.append('ngay_ket_thuc', ngayKetThuc);

        fetch('{% url "xml4750:preview_report" %}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => { throw new Error(text) });
            }
            return response.text();  // <-- dùng text thay vì blob
        })
        .then(html => {
            // Mở tab mới và hiển thị nội dung
            const previewWindow = window.open('', '_blank');
            previewWindow.document.open();
            previewWindow.document.write(html);
            previewWindow.document.close();
        })
        .catch(err => {
            alert("Lỗi khi xem trước báo cáo: " + err.message);
        });
    }

    // Thực hiện việc xuất dữ liệu ra Excel
    function exportToExcel(event) {
        if (event) event.preventDefault();

        const reportType = $('input[name="report_type"]:checked').val();
        const doiTuong = $('#doi_tuong').val();
        const thoiGianType = $('#thoi_gian_type').val();

        if (!reportType) {
            alert('Vui lòng chọn loại báo cáo');
            return;
        }

        const formData = new FormData();
        formData.append('report_type', reportType);
        formData.append('doi_tuong', doiTuong);
        formData.append('loai_thoi_gian', thoiGianType);

        let ngayBatDau = '';
        let ngayKetThuc = '';

        if (thoiGianType === '0' || thoiGianType === '1') {
            const tuNgay = $('#ngay_bat_dau').val();
            const denNgay = $('#ngay_ket_thuc').val();

            if (!tuNgay || (!denNgay && thoiGianType === 'giai_doan')) {
                alert('Vui lòng chọn đầy đủ ngày');
                return;
            }

            if (thoiGianType === '1') {
                // Nếu chỉ chọn 1 ngày
                ngayBatDau = tuNgay + ' 00:00:00';
                ngayKetThuc = tuNgay + ' 23:59:59';
            } else {
                ngayBatDau = tuNgay + ' 00:00:00';
                ngayKetThuc = denNgay + ' 23:59:59';
            }

        } else if (thoiGianType === '2') {
            const thang = parseInt($('#thang_value').val());
            const nam = parseInt($('#thang_nam').val());

            if (!thang || !nam) {
                alert('Vui lòng chọn tháng và năm');
                return;
            }

            const firstDay = new Date(nam, thang - 1, 1);
            const lastDay = new Date(nam, thang, 0);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);

        } else if (thoiGianType === '3') {
            const quy = parseInt($('#quy_value').val());
            const nam = parseInt($('#quy_nam').val());

            if (!quy || !nam) {
                alert('Vui lòng chọn quý và năm');
                return;
            }

            const quyBatDau = (quy - 1) * 3;
            const firstDay = new Date(nam, quyBatDau, 1);
            const lastDay = new Date(nam, quyBatDau + 3, 0);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);

        } else if (thoiGianType === '4') {
            const nam = parseInt($('#nam_value').val());
            if (!nam) {
                alert('Vui lòng nhập năm');
                return;
            }

            const firstDay = new Date(nam, 0, 1);
            const lastDay = new Date(nam, 11, 31);

            ngayBatDau = formatDateTime(firstDay, true);
            ngayKetThuc = formatDateTime(lastDay, false);
        }

        formData.append('ngay_bat_dau', ngayBatDau);
        formData.append('ngay_ket_thuc', ngayKetThuc);

        fetch('{% url "xml4750:export_bao_cao_excel" %}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => { throw new Error(text) });
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            const current_time = formatDateTimeForReport(new Date());
            a.href = url;
            a.download = 'bao_cao_' + reportType + '_'+current_time+'.xlsx';
            a.click();
            window.URL.revokeObjectURL(url);
        })
        .catch(err => {
            alert("Lỗi khi xuất Excel: " + err.message);
        });
    }

    // Hàm hỗ trợ định dạng ngày giờ theo kiểu 'yyyyMMddHHmm'
    function formatDateTime(date, isStart) {
        const yyyy = date.getFullYear();
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const dd = String(date.getDate()).padStart(2, '0');
        const time = isStart ? '0000' : '2359';
        return `${yyyy}${mm}${dd}${time}`;
    }

    // Hàm hỗ trợ định dạng ngày giờ theo kiểu yyyyMMddHHmmss để hiển thị trong báo cáo
    function formatDateTimeForReport(date) {
        const yyyy = date.getFullYear();
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const dd = String(date.getDate()).padStart(2, '0');
        const hh = String(date.getHours()).padStart(2, '0');
        const min = String(date.getMinutes()).padStart(2, '0');
        const ss = String(date.getSeconds()).padStart(2, '0');
        return `${yyyy}${mm}${dd}${hh}${min}${ss}`;
    }

    document.addEventListener("DOMContentLoaded", handleTimeTypeChange);
</script>
{% endblock %}