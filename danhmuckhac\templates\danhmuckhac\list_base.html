{% extends 'layouts/base.html' %}
{% load static %}

{% block extra_css %}
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<link href="{% static "css/danh_muc.css" %}" rel="stylesheet">
<link href="{% static "AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css" %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">{% block card_title %}Danh sách{% endblock %}</h3>
        <div class="card-tools">
          <div class="input-group input-group-sm" style="width: 280px;">
            <input type="text" id="searchInput" class="form-control float-right" 
                   placeholder="Tìm kiếm... (hỗ trợ không dấu)"
                   autocomplete="off">
            <div class="input-group-append">
              <button type="button" class="btn btn-outline-secondary" id="clearSearchBtn" 
                      title="Xóa tìm kiếm" style="display: none;">
                <i class="fas fa-times"></i>
              </button>
              <button type="button" class="btn btn-default" id="searchButton" title="Tìm kiếm">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="mb-3 extra_buttons">
          <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createModal">
            <i class="fas fa-plus mr-1"></i> Thêm mới
          </button>
          <button type="button" class="btn btn-success" id="exportBtn">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
          </button>
          <button type="button" class="btn btn-info" data-toggle="modal" data-target="#importModal">
            <i class="fas fa-file-import mr-1"></i> Import Excel
          </button>
          {% block extra_buttons %}{% endblock %}
        </div>

        <!-- Tabulator Table -->
        <div id="{% block table_id %}dataTable{% endblock %}"></div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
  <script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
  <script src="{% static "js/danh_muc.js" %}"></script>
  <script src="{% static "js/danh_muc_configs.js" %}"></script>
  <script src="{% static "AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js" %}"></script>

  <script>
    $(function () {
      // Get config based on type
      const configType = '{% block config_type %}default{% endblock %}';
      let config;
      
      switch(configType) {
        case 'tinh':
          config = TINH_CONFIG;
          break;
        case 'quan_huyen':
          config = QUAN_HUYEN_CONFIG;
          break;
        case 'phuong_xa':
          config = PHUONG_XA_CONFIG;
          break;
        case 'noi_dkbd':
          config = NOI_DKBD_CONFIG;
          break;
        case 'pvcm':
          config = PHAM_VI_CHUYEN_MON_CONFIG;
          break;
        default:
          console.error('Unknown config type:', configType);
          return;
      }
      
      // Fix URLs with placeholders
      config.editUrl = config.editUrl.replace('{id}', '0');
      config.deleteUrl = config.deleteUrl.replace('{id}', '0');
      config.toggleUrl = config.toggleUrl.replace('{id}', '0');
      
      // Initialize manager
      window.manager = new DanhMucManager(config);
      
      // Custom JavaScript for specific types
      {% block custom_js %}{% endblock %}
    });
  </script>
{% endblock %}

{% block extra_modal %}
<!-- Create Modal -->
<div class="modal fade" id="createModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createModalLabel">
          <i class="fas fa-plus mr-2"></i>{% block create_modal_title %}Thêm mới{% endblock %}
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="createForm">
        {% csrf_token %}
        <div class="modal-body">
          {% block create_form_fields %}
          <!-- Form fields will be defined in specific templates -->
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            Form fields chưa được định nghĩa. Vui lòng định nghĩa block "create_form_fields" trong template con.
          </div>
          {% endblock %}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times mr-1"></i> Đóng
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-1"></i> Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editModalLabel">
          <i class="fas fa-edit mr-2"></i>{% block edit_modal_title %}Chỉnh sửa{% endblock %}
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="editForm">
        {% csrf_token %}
        <div class="modal-body">
          {% block edit_form_fields %}
          <!-- Form fields will be defined in specific templates -->
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            Form fields chưa được định nghĩa. Vui lòng định nghĩa block "edit_form_fields" trong template con.
          </div>
          {% endblock %}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times mr-1"></i> Đóng
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-1"></i> Lưu thay đổi
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="importModalLabel">
          <i class="fas fa-file-import mr-2"></i>{% block import_modal_title %}Import từ Excel{% endblock %}
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="importForm" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="modal-body">
          <div class="form-group">
            <label for="import_file">
              <i class="fas fa-file-excel mr-1"></i>
              Chọn file Excel (.xls, .xlsx) hoặc CSV (.csv)
            </label>
            <div class="input-group">
              <div class="custom-file">
                <input type="file" class="custom-file-input" id="import_file" name="file" accept=".xls,.xlsx,.csv" required>
                <label class="custom-file-label" for="import_file">Chọn file hoặc kéo thả vào đây...</label>
              </div>
              <div class="input-group-append">
                <button class="btn btn-outline-secondary" type="button" onclick="$('#import_file').click();">
                  <i class="fas fa-folder-open"></i>
                </button>
              </div>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6><i class="icon fas fa-info"></i> Hướng dẫn:</h6>
            {% block import_instructions %}
            <p class="mb-2">File Excel cần có các cột theo đúng định dạng:</p>
            <ul class="mb-2">
              <li><strong>Cột mã</strong> (bắt buộc)</li>
              <li><strong>Cột tên</strong> (bắt buộc)</li>
              <li><strong>Cột hiệu lực</strong> (tùy chọn)</li>
            </ul>
            {% endblock %}
            <div class="mt-3">
              <button type="button" class="btn btn-sm btn-outline-info" onclick="downloadTemplate()">
                <i class="fas fa-download mr-1"></i>Tải file mẫu
              </button>
            </div>
          </div>
          
          <div id="import-errors" class="alert alert-danger" style="display: none;">
            <h6><i class="icon fas fa-ban"></i> Lỗi import:</h6>
            <ul id="error-list" class="mb-0"></ul>
          </div>
          
          <div id="import-preview" class="mt-3" style="display: none;">
            <h6><i class="fas fa-eye mr-1"></i>Xem trước dữ liệu:</h6>
            <div class="table-responsive">
              <table class="table table-sm table-bordered" id="preview-table">
                <thead class="thead-light">
                  <tr>
                    <th>STT</th>
                    {% block preview_headers %}
                    <th>Mã</th>
                    <th>Tên</th>
                    <th>Hiệu lực</th>
                    {% endblock %}
                    <th>Trạng thái</th>
                  </tr>
                </thead>
                <tbody id="preview-body">
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times mr-1"></i> Đóng
          </button>
          <button type="button" class="btn btn-info" id="previewBtn" style="display: none;">
            <i class="fas fa-eye mr-1"></i> Xem trước
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-upload mr-1"></i> Import
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" role="dialog" aria-labelledby="helpModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="helpModalLabel">
          <i class="fas fa-question-circle mr-2"></i>Hướng dẫn sử dụng
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <h6><i class="fas fa-keyboard mr-1"></i>Phím tắt:</h6>
            <ul class="list-unstyled">
              <li><kbd>Ctrl</kbd> + <kbd>E</kbd> - Xuất Excel</li>
              <li><kbd>Ctrl</kbd> + <kbd>I</kbd> - Import</li>
              <li><kbd>Ctrl</kbd> + <kbd>F</kbd> - Tìm kiếm</li>
              <li><kbd>Ctrl</kbd> + <kbd>R</kbd> - Làm mới</li>
              <li><kbd>ESC</kbd> - Đóng modal</li>
              <li><kbd>F5</kbd> - Làm mới trang</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h6><i class="fas fa-mouse-pointer mr-1"></i>Thao tác chuột:</h6>
            <ul class="list-unstyled">
              <li>Chuột phải trên dòng - Menu ngữ cảnh</li>
              <li>Kéo thả file vào modal Import</li>
              <li>Click đúp để chỉnh sửa nhanh</li>
              <li>Checkbox để chọn nhiều dòng</li>
            </ul>
          </div>
        </div>
        
        <hr>
        
        <h6><i class="fas fa-lightbulb mr-1"></i>Mẹo sử dụng:</h6>
        <ul>
          <li>Sử dụng tìm kiếm để lọc dữ liệu nhanh chóng</li>
          <li>Chọn nhiều dòng để thực hiện thao tác hàng loạt</li>
          <li>Sử dụng menu chuột phải để truy cập nhanh các chức năng</li>
          <li>Kéo thả file Excel trực tiếp vào modal Import</li>
          <li>Sử dụng phím tắt để tăng tốc độ làm việc</li>
        </ul>
        
        <hr>
        
        <h6><i class="fas fa-exclamation-triangle mr-1"></i>Lưu ý quan trọng:</h6>
        <div class="alert alert-warning">
          {% block help_notes %}
          <ul class="mb-0">
            <li>Mã không được trùng lặp</li>
            <li>Tên không được để trống</li>
            <li>Khi import, dữ liệu trùng mã sẽ được cập nhật</li>
          </ul>
          {% endblock %}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
          <i class="fas fa-times mr-1"></i> Đóng
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add Help Button to main content -->
<div class="position-fixed" style="bottom: 20px; right: 20px; z-index: 1000;">
  <button type="button" class="btn btn-info btn-lg rounded-circle" data-toggle="modal" data-target="#helpModal" title="Trợ giúp">
    <i class="fas fa-question"></i>
  </button>
</div>
{% endblock %}
