{% extends 'layouts/base.html' %}
{% load static %}
{% block title %}<PERSON><PERSON><PERSON> chi<PERSON><PERSON> hồ sơ BHYT{% endblock %}

{% block page_title %}<PERSON><PERSON><PERSON> chi<PERSON><PERSON> hồ sơ BHYT{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title"><PERSON><PERSON><PERSON> chiế<PERSON> hồ sơ BHYT</h3>
                </div>
                <div class="card-body">
                    <!-- Upload Section -->
                    <div class="upload-section mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="month_select">Tháng:</label>
                                <select id="month_select" class="form-control"></select>
                            </div>
                            <div class="col-md-3">
                                <label for="year_select">Năm:</label>
                                <select id="year_select" class="form-control"></select>
                            </div>
                            <div class="col-md-3">
                                <label for="doi_tuong"><PERSON><PERSON><PERSON>:</label>
                                <select id="doi_tuong" class="form-control">
                                    <option value="bhxh_vn">BHXH Việt Nam</option>
                                    <option value="bhxh_bqp">BHXH BQP</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="bhxh_file">File Excel BHXH:</label>
                                <input type="file" id="bhxh_file" class="form-control" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button id="btn_compare" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Đối chiếu
                                </button>
                                <button id="btn_export_excel" class="btn btn-success" disabled>
                                    <i class="fas fa-file-excel"></i> Xuất Excel
                                </button>
                            </div>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div id="progress" class="progress mt-3" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div id="results-section" style="display: none;">
                        <!-- Summary Statistics -->
                        <div class="row mb-3">
                            <div class="col-md-2">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-file-medical"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">XML1</span>
                                        <span class="info-box-number" id="total-xml1">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-file-excel"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Excel</span>
                                        <span class="info-box-number" id="total-excel">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Khớp</span>
                                        <span class="info-box-number" id="total-khop">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tabs -->
                        <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="tab-thieu" data-toggle="tab" href="#thieu" role="tab">
                                    Thiếu <span class="badge badge-danger">0</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="tab-thua" data-toggle="tab" href="#thua" role="tab">
                                    Thừa <span class="badge badge-warning">0</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="tab-lech" data-toggle="tab" href="#lech" role="tab">
                                    Lệch tiền <span class="badge badge-info">0</span>
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="resultTabContent">
                            <!-- Tab Thiếu -->
                            <div class="tab-pane fade show active" id="thieu" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="table-thieu">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Mã LK</th>
                                                <th>Họ tên</th>
                                                <th>Mã thẻ BHYT</th>
                                                <th>Ngày sinh</th>
                                                <th>Ngày vào</th>
                                                <th>Ngày ra</th>
                                                <th class="text-right">Tổng chi BH</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Tab Thừa -->
                            <div class="tab-pane fade" id="thua" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="table-thua">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Mã LK</th>
                                                <th>Họ tên</th>
                                                <th>Mã thẻ BHYT</th>
                                                <th>Ngày sinh</th>
                                                <th>Ngày vào</th>
                                                <th>Ngày ra</th>
                                                <th class="text-right">Tổng chi BH</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Tab Lệch -->
                            <div class="tab-pane fade" id="lech" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="table-lech">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Mã LK</th>
                                                <th>Họ tên</th>
                                                <th class="text-right">XML1 Tổng chi</th>
                                                <th class="text-right">Excel Tổng chi</th>
                                                <th class="text-right">Chênh lệch</th>
                                                <th class="text-right">BN TT (XML1/Excel)</th>
                                                <th class="text-right">BH TT (XML1/Excel)</th>
                                                <th class="text-right">BN CCT (XML1/Excel)</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<style>
    .card-primary {
        border-top: 3px solid #007bff;
    }
    .nav-tabs .nav-link {
        color: #007bff;
        border: none;
        border-bottom: 2px solid transparent;
    }
    .nav-tabs .nav-link.active {
        background-color: #007bff;
        color: white;
        border-radius: 0;
    }
    .table-responsive {
        min-height: 200px;
    }
    .upload-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
    }
    .text-danger {
        color: #dc3545 !important;
    }
    .text-success {
        color: #28a745 !important;
    }
    .badge {
        font-size: 0.8em;
    }
    .info-box {
        display: block;
        min-height: 70px;
        background: #fff;
        width: 100%;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        border-radius: 2px;
        margin-bottom: 15px;
    }
    .info-box-icon {
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
        display: block;
        float: left;
        height: 70px;
        width: 70px;
        text-align: center;
        font-size: 45px;
        line-height: 70px;
        background: rgba(0,0,0,0.2);
    }
    .info-box-content {
        padding: 5px 10px;
        margin-left: 70px;
    }
    .info-box-text {
        text-transform: uppercase;
        font-weight: bold;
        font-size: 13px;
    }
    .info-box-number {
        display: block;
        font-weight: bold;
        font-size: 18px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static "AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js" %}"></script>
<script src="{% static "AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js" %}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    $(document).ready(function() {
        const today = new Date();
        
        // Danh sách tháng (1–12)
        const months = Array.from({ length: 12 }, (_, i) => {
            const value = (i + 1).toString().padStart(2, '0');
            return { id: value, text: `Tháng ${value}` };
        });

        // Danh sách năm (2015–2030)
        const years = Array.from({ length: 100 }, (_, i) => {
            const year = 2015 + i;
            return { id: year, text: year.toString() };
        });

        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const todayStr = `${yyyy}-${mm}-${dd}`;

        $('#month_select').select2({
            data: months,
            theme: 'bootstrap4',
            placeholder: "Chọn tháng",
        });

        $('#year_select').select2({
            data: years,
            theme: 'bootstrap4',
            placeholder: "Chọn năm",
        });

        $('#month_select').val(mm).trigger('change');
        $('#year_select').val(yyyy).trigger('change');

        // Initialize tab navigation
        $('#resultTabs a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // Function to format currency
        function formatCurrency(amount) {
            if (amount === null || amount === undefined || amount === '') return '0';
            return new Intl.NumberFormat('vi-VN').format(parseFloat(amount));
        }

        // Function to update summary statistics
        function updateSummary(summary) {
            $('#total-xml1').text(summary.total_xml1 || 0);
            $('#total-excel').text(summary.total_excel || 0);
            $('#total-khop').text(summary.total_khop || 0);
        }

        $('#btn_compare').on('click', function() {
            const fileInput = $('#bhxh_file')[0];
            const doiTuong = $('#doi_tuong').val();
            if (fileInput.files.length === 0) {
                Swal.fire('Lỗi', 'Vui lòng chọn file Excel!', 'error');
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                const data = e.target.result;
                const workbook = XLSX.read(data, { type: 'binary' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                // Assuming the first row is headers, process the data
                const headers = jsonData[0];
                const excelData = jsonData.slice(1).map(row => {
                    let obj = {};
                    headers.forEach((header, index) => {
                        obj[header] = row[index];
                    });
                    return obj;
                });

                const formData = new FormData();
                formData.append('excel_data', JSON.stringify(excelData));
                formData.append('month', $('#month_select').val());
                formData.append('year', $('#year_select').val());
                formData.append('doi_tuong', doiTuong);

                $('#progress').show();
                $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0);

                $.ajax({
                    url: '{% url "xml4750:api_doi_chieu_ho_so" %}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function() {
                        var xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener('progress', function(evt) {
                            if (evt.lengthComputable) {
                                var percentComplete = (evt.loaded / evt.total) * 100;
                                $('.progress-bar').css('width', percentComplete + '%').attr('aria-valuenow', percentComplete);
                            }
                        }, false);
                        return xhr;
                    },
                    success: function(response) {
                        $('#progress').hide();
                        $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0);
                        
                        if (response.success) {
                            const mismatches = response.mismatches || [];
                            const summary = response.summary || {};
                            
                            // Update summary statistics
                            updateSummary(summary);
                            
                            // Clear all tables
                            $('#table-thieu tbody').empty();
                            $('#table-thua tbody').empty();
                            $('#table-lech tbody').empty();

                            // Update tab badges
                            let thieuCount = 0, thuaCount = 0, lechCount = 0;

                            mismatches.forEach(mismatch => {
                                // THIẾU: Có trong XML1 nhưng không có trong Excel
                                if (mismatch.type === 'thieu') {
                                    thieuCount++;
                                    $('#table-thieu tbody').append(`
                                        <tr>
                                            <td>${mismatch.maLK}</td>
                                            <td>${mismatch.hoTen}</td>
                                            <td>${mismatch.maTheBHYT || 'N/A'}</td>
                                            <td>${mismatch.ngaySinh || 'N/A'}</td>
                                            <td>${mismatch.ngayVao || 'N/A'}</td>
                                            <td>${mismatch.ngayRa || 'N/A'}</td>
                                            <td class="text-right">${formatCurrency(mismatch.xml_tTongChiBH)}</td>
                                        </tr>
                                    `);
                                } 
                                // THỪA: Có trong Excel nhưng không có trong XML1
                                else if (mismatch.type === 'thua') {
                                    thuaCount++;
                                    $('#table-thua tbody').append(`
                                        <tr>
                                            <td>${mismatch.maLK}</td>
                                            <td>${mismatch.hoTen}</td>
                                            <td>${mismatch.maTheBHYT || 'N/A'}</td>
                                            <td>${mismatch.ngaySinh || 'N/A'}</td>
                                            <td>${mismatch.ngayVao || 'N/A'}</td>
                                            <td>${mismatch.ngayRa || 'N/A'}</td>
                                            <td class="text-right">${formatCurrency(mismatch.excel_tongChiBH)}</td>
                                        </tr>
                                    `);
                                } 
                                // LỆCH: Có trong cả XML1 và Excel nhưng có sự khác biệt
                                else if (mismatch.type === 'lech') {
                                    lechCount++;
                                    const xmlTotal = parseFloat(mismatch.xml_tTongChiBH) || 0;
                                    const excelTotal = parseFloat(mismatch.excel_tongChiBH) || 0;
                                    const difference = xmlTotal - excelTotal;
                                    
                                    const xmlBNTT = parseFloat(mismatch.xml_tBNTT) || 0;
                                    const excelBNTT = parseFloat(mismatch.excel_tBNTT) || 0;
                                    
                                    const xmlBHTT = parseFloat(mismatch.xml_tBHTT) || 0;
                                    const excelBHTT = parseFloat(mismatch.excel_tBHTT) || 0;
                                    
                                    const xmlBNCCT = parseFloat(mismatch.xml_tBNCCT) || 0;
                                    const excelBNCCT = parseFloat(mismatch.excel_tBNCCT) || 0;

                                    $('#table-lech tbody').append(`
                                        <tr>
                                            <td>${mismatch.maLK}</td>
                                            <td>${mismatch.hoTen}</td>
                                            <td class="text-right">${formatCurrency(xmlTotal)}</td>
                                            <td class="text-right">${formatCurrency(excelTotal)}</td>
                                            <td class="text-right ${difference > 0 ? 'text-success' : difference < 0 ? 'text-danger' : ''}">${formatCurrency(difference)}</td>
                                            <td class="text-right">${formatCurrency(xmlBNTT)} / ${formatCurrency(excelBNTT)}</td>
                                            <td class="text-right">${formatCurrency(xmlBHTT)} / ${formatCurrency(excelBHTT)}</td>
                                            <td class="text-right">${formatCurrency(xmlBNCCT)} / ${formatCurrency(excelBNCCT)}</td>
                                        </tr>
                                    `);
                                }
                            });

                            // Update tab labels with counts
                            $('#tab-thieu').html(`Thiếu <span class="badge badge-danger">${thieuCount}</span>`);
                            $('#tab-thua').html(`Thừa <span class="badge badge-warning">${thuaCount}</span>`);
                            $('#tab-lech').html(`Lệch tiền <span class="badge badge-info">${lechCount}</span>`);

                            // Show message if no data
                            if (thieuCount === 0) {
                                $('#table-thieu tbody').append('<tr><td colspan="7" class="text-center text-muted">Không có hồ sơ thiếu</td></tr>');
                            }
                            if (thuaCount === 0) {
                                $('#table-thua tbody').append('<tr><td colspan="7" class="text-center text-muted">Không có hồ sơ thừa</td></tr>');
                            }
                            if (lechCount === 0) {
                                $('#table-lech tbody').append('<tr><td colspan="8" class="text-center text-muted">Không có hồ sơ lệch tiền</td></tr>');
                            }

                            // Show results section
                            $('#results-section').show();
                            $('#btn_export_excel').prop('disabled', false);
                            
                            const totalIssues = thieuCount + thuaCount + lechCount;
                            if (totalIssues === 0) {
                                Swal.fire('Thành công', 'Đối chiếu hoàn tất! Không có hồ sơ nào có vấn đề.', 'success');
                            } else {
                                Swal.fire('Hoàn tất', `Đối chiếu hoàn tất! Tìm thấy ${totalIssues} hồ sơ có vấn đề.`, 'info');
                            }
                        } else {
                            Swal.fire('Lỗi', response.message || 'Có lỗi xảy ra khi đối chiếu!', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#progress').hide();
                        $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0);
                        Swal.fire('Lỗi', 'Có lỗi xảy ra khi gửi dữ liệu: ' + error, 'error');
                    }
                });
            };

            reader.readAsBinaryString(file);
        });

        // Export to Excel functionality
        $('#btn_export_excel').on('click', function() {
            // Create workbook
            const wb = XLSX.utils.book_new();
            
            // Export Thiếu sheet
            const thieuData = [];
            $('#table-thieu tbody tr').each(function() {
                if (!$(this).find('td').hasClass('text-muted')) {
                    const row = [];
                    $(this).find('td').each(function() {
                        row.push($(this).text().trim());
                    });
                    thieuData.push(row);
                }
            });
            if (thieuData.length > 0) {
                const thieuHeaders = ['Mã LK', 'Họ tên', 'Mã thẻ BHYT', 'Ngày sinh', 'Ngày vào', 'Ngày ra', 'Tổng chi BH'];
                const thieuWS = XLSX.utils.aoa_to_sheet([thieuHeaders, ...thieuData]);
                XLSX.utils.book_append_sheet(wb, thieuWS, 'Hồ sơ thiếu');
            }
            
            // Export Thừa sheet
            const thuaData = [];
            $('#table-thua tbody tr').each(function() {
                if (!$(this).find('td').hasClass('text-muted')) {
                    const row = [];
                    $(this).find('td').each(function() {
                        row.push($(this).text().trim());
                    });
                    thuaData.push(row);
                }
            });
            if (thuaData.length > 0) {
                const thuaHeaders = ['Mã LK', 'Họ tên', 'Mã thẻ BHYT', 'Ngày sinh', 'Ngày vào', 'Ngày ra', 'Tổng chi BH'];
                const thuaWS = XLSX.utils.aoa_to_sheet([thuaHeaders, ...thuaData]);
                XLSX.utils.book_append_sheet(wb, thuaWS, 'Hồ sơ thừa');
            }
            
            // Export Lệch sheet
            const lechData = [];
            $('#table-lech tbody tr').each(function() {
                if (!$(this).find('td').hasClass('text-muted')) {
                    const row = [];
                    $(this).find('td').each(function() {
                        row.push($(this).text().trim());
                    });
                    lechData.push(row);
                }
            });
            if (lechData.length > 0) {
                const lechHeaders = ['Mã LK', 'Họ tên', 'XML1 Tổng chi', 'Excel Tổng chi', 'Chênh lệch', 'BN TT (XML1/Excel)', 'BH TT (XML1/Excel)', 'BN CCT (XML1/Excel)'];
                const lechWS = XLSX.utils.aoa_to_sheet([lechHeaders, ...lechData]);
                XLSX.utils.book_append_sheet(wb, lechWS, 'Hồ sơ lệch tiền');
            }
            
            // Save file
            const fileName = `Doi_chieu_BHYT_${$('#month_select').val()}_${$('#year_select').val()}_${new Date().getTime()}.xlsx`;
            XLSX.writeFile(wb, fileName);
        });
    });
</script>
{% endblock %}