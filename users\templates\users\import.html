{% extends 'layouts/base.html' %}

{% block title %}Nhậ<PERSON> danh sách người dùng | LAN Insight Guardian{% endblock %}

{% block page_title %}Nhậ<PERSON> danh sách người dùng{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:list' %}">Quản lý người dùng</a></li>
<li class="breadcrumb-item active">Nh<PERSON><PERSON> danh sách</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-6">
    <div class="card card-primary">
      <div class="card-header">
        <h3 class="card-title">Nhập danh sách người dùng từ file Excel</h3>
      </div>
      <!-- /.card-header -->
      <!-- form start -->
      <form role="form" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="card-body">
          <!-- Django messages will be handled by SweetAlert2 -->
          
          <div class="form-group">
            <label for="id_file">{{ form.file.label }}</label>
            {{ form.file }}
            <small class="form-text text-muted">{{ form.file.help_text }}</small>
            {% if form.file.errors %}
            <div class="text-danger">
              {% for error in form.file.errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}
          </div>
          
          <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Hướng dẫn</h5>
            <p>File Excel cần có các cột sau:</p>
            <ul>
              <li><strong>Tên đăng nhập</strong> (bắt buộc): Tên đăng nhập của người dùng</li>
              <li><strong>Email</strong> (bắt buộc): Địa chỉ email của người dùng</li>
              <li><strong>Mật khẩu</strong> (bắt buộc): Mật khẩu của người dùng</li>
              <li><strong>Họ</strong> (bắt buộc): Họ của người dùng</li>
              <li><strong>Tên</strong> (bắt buộc): Tên của người dùng</li>
              <li><strong>Nhân viên</strong> (tùy chọn): Có/Không - Người dùng có quyền truy cập trang quản trị</li>
              <li><strong>Quản trị viên</strong> (tùy chọn): Có/Không - Người dùng có tất cả quyền</li>
              <li><strong>Hoạt động</strong> (tùy chọn): Có/Không - Trạng thái hoạt động của người dùng</li>
              <li><strong>Vai trò</strong> (tùy chọn): Tên vai trò của người dùng</li>
            </ul>
            <p>Bạn có thể <a href="{% url 'users:export' %}" class="btn btn-sm btn-info">Tải xuống mẫu</a> để xem định dạng file.</p>
          </div>
        </div>
        <!-- /.card-body -->

        <div class="card-footer">
          <button type="submit" class="btn btn-primary" id="save-button">
            <i class="fas fa-upload"></i> Nhập dữ liệu
          </button>
          <a href="{% url 'users:list' %}" class="btn btn-default">
            <i class="fas fa-times"></i> Hủy
          </a>
        </div>
      </form>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    // Xử lý nút nhập dữ liệu
    $('#save-button').on('click', function(e) {
      e.preventDefault();
      
      // Kiểm tra file
      var fileInput = $('#id_file');
      if (!fileInput.val()) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng chọn file Excel để nhập dữ liệu',
        });
        return;
      }
      
      // Kiểm tra định dạng file
      var fileName = fileInput.val();
      if (!fileName.endsWith('.xlsx')) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Chỉ chấp nhận file Excel (.xlsx)',
        });
        return;
      }
      
      // Hiển thị thông báo xác nhận
      Swal.fire({
        title: 'Xác nhận nhập dữ liệu',
        text: 'Bạn có chắc chắn muốn nhập dữ liệu từ file này?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Nhập dữ liệu',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Hiển thị loading
          Swal.fire({
            title: 'Đang xử lý...',
            html: 'Vui lòng đợi trong giây lát',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });
          
          // Submit form
          $(this).closest('form').submit();
        }
      });
    });
  });
</script>
{% endblock %}
