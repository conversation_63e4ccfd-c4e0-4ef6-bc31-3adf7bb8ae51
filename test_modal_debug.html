<!DOCTYPE html>
<html>
<head>
    <title>Test Modal Debug</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Modal Debug</h1>
    
    <button onclick="testLocalStorage()">Test localStorage</button>
    <button onclick="testCategoryData()">Test categoryData</button>
    
    <div id="results"></div>
    
    <script>
        function testLocalStorage() {
            const CACHE_KEY = 'cachedCategoryData';
            const CACHE_EXPIRE_KEY = 'cachedCategoryExpire';
            
            const cachedData = localStorage.getItem(CACHE_KEY);
            const cachedExpire = localStorage.getItem(CACHE_EXPIRE_KEY);
            const now = Date.now();
            
            console.log('Cache data:', cachedData);
            console.log('Cache expire:', cachedExpire);
            console.log('Now:', now);
            console.log('Is valid:', cachedData && cachedExpire && now < parseInt(cachedExpire));
            
            if (cachedData) {
                try {
                    const parsed = JSON.parse(cachedData);
                    console.log('Parsed data:', parsed);
                    
                    document.getElementById('results').innerHTML = 
                        '<h3>localStorage Data:</h3>' +
                        '<pre>' + JSON.stringify(parsed, null, 2) + '</pre>';
                } catch (e) {
                    console.error('Parse error:', e);
                    document.getElementById('results').innerHTML = 'Parse error: ' + e.message;
                }
            } else {
                document.getElementById('results').innerHTML = 'No cached data found';
            }
        }
        
        function testCategoryData() {
            if (typeof categoryData !== 'undefined') {
                console.log('categoryData:', categoryData);
                document.getElementById('results').innerHTML = 
                    '<h3>categoryData:</h3>' +
                    '<pre>' + JSON.stringify(categoryData, null, 2) + '</pre>';
            } else {
                document.getElementById('results').innerHTML = 'categoryData is undefined';
            }
        }
    </script>
</body>
</html>
