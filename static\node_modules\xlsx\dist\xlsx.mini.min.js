/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var XLSX={};function make_xlsx_lib(e){e.version="0.18.5";var r=1200,t=1252;var a=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4];var n={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969};var i=function(e){if(a.indexOf(e)==-1)return;t=n[0]=e};function s(){i(1252)}var l=function(e){r=e;i(e)};function o(){l(1200);s()}function c(e){var r=[];for(var t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function f(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t)+(e.charCodeAt(2*t+1)<<8));return r.join("")}function u(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var h=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);if(r==255&&t==254)return f(e.slice(2));if(r==254&&t==255)return u(e.slice(2));if(r==65279)return e.slice(1);return e};var p=function xc(e){return String.fromCharCode(e)};var d=function Sc(e){return String.fromCharCode(e)};var m=null;var v=true;var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function b(e){var r="";var t=0,a=0,n=0,i=0,s=0,l=0,o=0;for(var c=0;c<e.length;){t=e.charCodeAt(c++);i=t>>2;a=e.charCodeAt(c++);s=(t&3)<<4|a>>4;n=e.charCodeAt(c++);l=(a&15)<<2|n>>6;o=n&63;if(isNaN(a)){l=o=64}else if(isNaN(n)){o=64}r+=g.charAt(i)+g.charAt(s)+g.charAt(l)+g.charAt(o)}return r}function w(e){var r="";var t=0,a=0,n=0,i=0,s=0,l=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;){i=g.indexOf(e.charAt(c++));s=g.indexOf(e.charAt(c++));t=i<<2|s>>4;r+=String.fromCharCode(t);l=g.indexOf(e.charAt(c++));a=(s&15)<<4|l>>2;if(l!==64){r+=String.fromCharCode(a)}o=g.indexOf(e.charAt(c++));n=(l&3)<<6|o;if(o!==64){r+=String.fromCharCode(n)}}return r}var y=function(){return typeof Buffer!=="undefined"&&typeof undefined!=="undefined"&&typeof{}!=="undefined"&&!!{}.node}();var k=function(){if(typeof Buffer!=="undefined"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(r){e=true}return e?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function x(e){if(y)return Buffer.alloc?Buffer.alloc(e):new Buffer(e);return typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function S(e){if(y)return Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e);return typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var C=function Cc(e){if(y)return k(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)&255})};function _(e){if(typeof ArrayBuffer==="undefined")return C(e);var r=new ArrayBuffer(e.length),t=new Uint8Array(r);for(var a=0;a!=e.length;++a)t[a]=e.charCodeAt(a)&255;return r}function A(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");var r=[];for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function T(e){if(typeof Uint8Array==="undefined")throw new Error("Unsupported");return new Uint8Array(e)}function E(e){if(typeof ArrayBuffer=="undefined")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return E(new Uint8Array(e));var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=e[t];return r}var F=y?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:k(e)}))}:function(e){if(typeof Uint8Array!=="undefined"){var r=0,t=0;for(r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t);var n=0;for(r=0,t=0;r<e.length;t+=n,++r){n=e[r].length;if(e[r]instanceof Uint8Array)a.set(e[r],t);else if(typeof e[r]=="string"){throw"wtf"}else a.set(new Uint8Array(e[r]),t)}return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))};function D(e){var r=[],t=0,a=e.length+250;var n=x(e.length+255);for(var i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)n[t++]=s;else if(s<2048){n[t++]=192|s>>6&31;n[t++]=128|s&63}else if(s>=55296&&s<57344){s=(s&1023)+64;var l=e.charCodeAt(++i)&1023;n[t++]=240|s>>8&7;n[t++]=128|s>>2&63;n[t++]=128|l>>6&15|(s&3)<<4;n[t++]=128|l&63}else{n[t++]=224|s>>12&15;n[t++]=128|s>>6&63;n[t++]=128|s&63}if(t>a){r.push(n.slice(0,t));t=0;n=x(65535);a=65530}}r.push(n.slice(0,t));return F(r)}var O=/\u0000/g,M=/[\u0001-\u0006]/g;function N(e){var r="",t=e.length-1;while(t>=0)r+=e.charAt(t--);return r}function P(e,r){var t=""+e;return t.length>=r?t:br("0",r-t.length)+t}function I(e,r){var t=""+e;return t.length>=r?t:br(" ",r-t.length)+t}function R(e,r){var t=""+e;return t.length>=r?t:t+br(" ",r-t.length)}function L(e,r){var t=""+Math.round(e);return t.length>=r?t:br("0",r-t.length)+t}function B(e,r){var t=""+e;return t.length>=r?t:br("0",r-t.length)+t}var z=Math.pow(2,32);function U(e,r){if(e>z||e<-z)return L(e,r);var t=Math.round(e);return B(t,r)}function W(e,r){r=r||0;return e.length>=7+r&&(e.charCodeAt(r)|32)===103&&(e.charCodeAt(r+1)|32)===101&&(e.charCodeAt(r+2)|32)===110&&(e.charCodeAt(r+3)|32)===101&&(e.charCodeAt(r+4)|32)===114&&(e.charCodeAt(r+5)|32)===97&&(e.charCodeAt(r+6)|32)===108}var j=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]];var H=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function V(e){if(!e)e={};e[0]="General";e[1]="0";e[2]="0.00";e[3]="#,##0";e[4]="#,##0.00";e[9]="0%";e[10]="0.00%";e[11]="0.00E+00";e[12]="# ?/?";e[13]="# ??/??";e[14]="m/d/yy";e[15]="d-mmm-yy";e[16]="d-mmm";e[17]="mmm-yy";e[18]="h:mm AM/PM";e[19]="h:mm:ss AM/PM";e[20]="h:mm";e[21]="h:mm:ss";e[22]="m/d/yy h:mm";e[37]="#,##0 ;(#,##0)";e[38]="#,##0 ;[Red](#,##0)";e[39]="#,##0.00;(#,##0.00)";e[40]="#,##0.00;[Red](#,##0.00)";e[45]="mm:ss";e[46]="[h]:mm:ss";e[47]="mmss.0";e[48]="##0.0E+0";e[49]="@";e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "';return e}var X={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'};var G={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0};var Y={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function J(e,r,t){var a=e<0?-1:1;var n=e*a;var i=0,s=1,l=0;var o=1,c=0,f=0;var u=Math.floor(n);while(c<r){u=Math.floor(n);l=u*s+i;f=u*c+o;if(n-u<5e-8)break;n=1/(n-u);i=s;s=l;o=c;c=f}if(f>r){if(c>r){f=o;l=i}else{f=c;l=s}}if(!t)return[0,a*l,f];var h=Math.floor(a*l/f);return[h,a*l-h*f,f]}function K(e,r,t){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0;var s=[];var l={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(l.u)<1e-6)l.u=0;if(r&&r.date1904)a+=1462;if(l.u>.9999){l.u=0;if(++n==86400){l.T=n=0;++a;++l.D}}if(a===60){s=t?[1317,10,29]:[1900,2,29];i=3}else if(a===0){s=t?[1317,8,29]:[1900,1,0];i=6}else{if(a>60)--a;var o=new Date(1900,0,1);o.setDate(o.getDate()+a-1);s=[o.getFullYear(),o.getMonth()+1,o.getDate()];i=o.getDay();if(a<60)i=(i+6)%7;if(t)i=le(o,s)}l.y=s[0];l.m=s[1];l.d=s[2];l.S=n%60;n=Math.floor(n/60);l.M=n%60;n=Math.floor(n/60);l.H=n;l.q=i;return l}var q=new Date(1899,11,31,0,0,0);var Z=q.getTime();var Q=new Date(1900,2,1,0,0,0);function ee(e,r){var t=e.getTime();if(r)t-=1461*24*60*60*1e3;else if(e>=Q)t+=24*60*60*1e3;return(t-(Z+(e.getTimezoneOffset()-q.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function re(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function te(e){if(e.indexOf("E")==-1)return e;return e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function ae(e){var r=e<0?12:11;var t=re(e.toFixed(12));if(t.length<=r)return t;t=e.toPrecision(10);if(t.length<=r)return t;return e.toExponential(5)}function ne(e){var r=re(e.toFixed(11));return r.length>(e<0?12:11)||r==="0"||r==="-0"?e.toPrecision(6):r}function ie(e){var r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),t;if(r>=-4&&r<=-1)t=e.toPrecision(10+r);else if(Math.abs(r)<=9)t=ae(e);else if(r===10)t=e.toFixed(10).substr(0,12);else t=ne(e);return re(te(t.toUpperCase()))}function se(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):ie(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Be(14,ee(e,r&&r.date1904),r);}throw new Error("unsupported value in General format: "+e)}function le(e,r){r[0]-=581;var t=e.getDay();if(e<60)t=(t+6)%7;return t}function oe(e,r,t,a){var n="",i=0,s=0,l=t.y,o,c=0;switch(e){case 98:l=t.y+543;case 121:switch(r.length){case 1:;case 2:o=l%100;c=2;break;default:o=l%1e4;c=4;break;}break;case 109:switch(r.length){case 1:;case 2:o=t.m;c=r.length;break;case 3:return H[t.m-1][1];case 5:return H[t.m-1][0];default:return H[t.m-1][2];}break;case 100:switch(r.length){case 1:;case 2:o=t.d;c=r.length;break;case 3:return j[t.q][0];default:return j[t.q][1];}break;case 104:switch(r.length){case 1:;case 2:o=1+(t.H+11)%12;c=r.length;break;default:throw"bad hour format: "+r;}break;case 72:switch(r.length){case 1:;case 2:o=t.H;c=r.length;break;default:throw"bad hour format: "+r;}break;case 77:switch(r.length){case 1:;case 2:o=t.M;c=r.length;break;default:throw"bad minute format: "+r;}break;case 115:if(r!="s"&&r!="ss"&&r!=".0"&&r!=".00"&&r!=".000")throw"bad second format: "+r;if(t.u===0&&(r=="s"||r=="ss"))return P(t.S,r.length);if(a>=2)s=a===3?1e3:100;else s=a===1?10:1;i=Math.round(s*(t.S+t.u));if(i>=60*s)i=0;if(r==="s")return i===0?"0":""+i/s;n=P(i,2+a);if(r==="ss")return n.substr(0,2);return"."+n.substr(2,r.length-1);case 90:switch(r){case"[h]":;case"[hh]":o=t.D*24+t.H;break;case"[m]":;case"[mm]":o=(t.D*24+t.H)*60+t.M;break;case"[s]":;case"[ss]":o=((t.D*24+t.H)*60+t.M)*60+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r;}c=r.length===3?1:2;break;case 101:o=l;c=1;break;}var f=c>0?P(o,c):"";return f}function ce(e){var r=3;if(e.length<=r)return e;var t=e.length%r,a=e.substr(0,t);for(;t!=e.length;t+=r)a+=(a.length>0?",":"")+e.substr(t,r);return a}var fe=/%/g;function ue(e,r,t){var a=r.replace(fe,""),n=r.length-a.length;return De(e,a,t*Math.pow(10,2*n))+br("%",n)}function he(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return De(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function pe(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+pe(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(t.indexOf("e")===-1){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);while(t.substr(0,2)==="0."){t=t.charAt(0)+t.substr(2,n)+"."+t.substr(2+n);t=t.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.")}t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}var de=/# (\?+)( ?)\/( ?)(\d+)/;function me(e,r,t){var a=parseInt(e[4],10),n=Math.round(r*a),i=Math.floor(n/a);var s=n-i*a,l=a;return t+(i===0?"":""+i)+" "+(s===0?br(" ",e[1].length+1+e[4].length):I(s,e[1].length)+e[2]+"/"+e[3]+P(l,e[4].length))}function ve(e,r,t){return t+(r===0?"":""+r)+br(" ",e[1].length+2+e[4].length)}var ge=/^#*0*\.([0#]+)/;var be=/\).*[0#]/;var we=/\(###\) ###\\?-####/;function ye(e){var r="",t;for(var a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t);}return r}function ke(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function xe(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);if(r<(""+Math.round(t*a)).length)return 0;return Math.round(t*a)}function Se(e,r){if(r<(""+Math.round((e-Math.floor(e))*Math.pow(10,r))).length){return 1}return 0}function Ce(e){if(e<2147483647&&e>-2147483648)return""+(e>=0?e|0:e-1|0);return""+Math.floor(e)}function _e(e,r,t){if(e.charCodeAt(0)===40&&!r.match(be)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(t>=0)return _e("n",a,t);return"("+_e("n",a,-t)+")"}if(r.charCodeAt(r.length-1)===44)return he(e,r,t);if(r.indexOf("%")!==-1)return ue(e,r,t);if(r.indexOf("E")!==-1)return pe(r,t);if(r.charCodeAt(0)===36)return"$"+_e(e,r.substr(r.charAt(1)==" "?2:1),t);var n;var i,s,l,o=Math.abs(t),c=t<0?"-":"";if(r.match(/^00+$/))return c+U(o,r.length);if(r.match(/^[#?]+$/)){n=U(t,0);if(n==="0")n="";return n.length>r.length?n:ye(r.substr(0,r.length-n.length))+n}if(i=r.match(de))return me(i,o,c);if(r.match(/^#+0+$/))return c+U(o,r.length-r.indexOf("0"));if(i=r.match(ge)){n=ke(t,i[1].length).replace(/^([^\.]+)$/,"$1."+ye(i[1])).replace(/\.$/,"."+ye(i[1])).replace(/\.(\d*)$/,function(e,r){return"."+r+br("0",ye(i[1]).length-r.length)});return r.indexOf("0.")!==-1?n:n.replace(/^0\./,".")}r=r.replace(/^#+([0.])/,"$1");if(i=r.match(/^(0*)\.(#*)$/)){return c+ke(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".")}if(i=r.match(/^#{1,3},##0(\.?)$/))return c+ce(U(o,0));if(i=r.match(/^#,##0\.([#0]*0)$/)){return t<0?"-"+_e(e,r,-t):ce(""+(Math.floor(t)+Se(t,i[1].length)))+"."+P(xe(t,i[1].length),i[1].length)}if(i=r.match(/^#,#*,#0/))return _e(e,r.replace(/^#,#*,/,""),t);if(i=r.match(/^([0#]+)(\\?-([0#]+))+$/)){n=N(_e(e,r.replace(/[\\-]/g,""),t));s=0;return N(N(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return s<n.length?n.charAt(s++):e==="0"?"0":""}))}if(r.match(we)){n=_e(e,"##########",t);return"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6)}var f="";if(i=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(i[4].length,7);l=J(o,Math.pow(10,s)-1,false);n=""+c;f=De("n",i[1],l[1]);if(f.charAt(f.length-1)==" ")f=f.substr(0,f.length-1)+"0";n+=f+i[2]+"/"+i[3];f=R(l[2],s);if(f.length<i[4].length)f=ye(i[4].substr(i[4].length-f.length))+f;n+=f;return n}if(i=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(Math.max(i[1].length,i[4].length),7);l=J(o,Math.pow(10,s)-1,true);return c+(l[0]||(l[1]?"":"0"))+" "+(l[1]?I(l[1],s)+i[2]+"/"+i[3]+R(l[2],s):br(" ",2*s+1+i[2].length+i[3].length))}if(i=r.match(/^[#0?]+$/)){n=U(t,0);if(r.length<=n.length)return n;return ye(r.substr(0,r.length-n.length))+n}if(i=r.match(/^([#0?]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1");s=n.indexOf(".");var u=r.indexOf(".")-s,h=r.length-n.length-u;return ye(r.substr(0,u)+n+r.substr(r.length-h))}if(i=r.match(/^00,000\.([#0]*0)$/)){s=xe(t,i[1].length);return t<0?"-"+_e(e,r,-t):ce(Ce(t)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e})+"."+P(s,i[1].length)}switch(r){case"###,##0.00":return _e(e,"#,##0.00",t);case"###,###":;case"##,###":;case"#,###":var p=ce(U(o,0));return p!=="0"?c+p:"";case"###,###.00":return _e(e,"###,##0.00",t).replace(/^0\./,".");case"#,###.00":return _e(e,"#,##0.00",t).replace(/^0\./,".");default:;}throw new Error("unsupported format |"+r+"|")}function Ae(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return De(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function Te(e,r,t){var a=r.replace(fe,""),n=r.length-a.length;return De(e,a,t*Math.pow(10,2*n))+br("%",n)}function Ee(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+Ee(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(!t.match(/[Ee]/)){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}function Fe(e,r,t){if(e.charCodeAt(0)===40&&!r.match(be)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(t>=0)return Fe("n",a,t);return"("+Fe("n",a,-t)+")"}if(r.charCodeAt(r.length-1)===44)return Ae(e,r,t);if(r.indexOf("%")!==-1)return Te(e,r,t);if(r.indexOf("E")!==-1)return Ee(r,t);if(r.charCodeAt(0)===36)return"$"+Fe(e,r.substr(r.charAt(1)==" "?2:1),t);var n;var i,s,l,o=Math.abs(t),c=t<0?"-":"";if(r.match(/^00+$/))return c+P(o,r.length);if(r.match(/^[#?]+$/)){n=""+t;if(t===0)n="";return n.length>r.length?n:ye(r.substr(0,r.length-n.length))+n}if(i=r.match(de))return ve(i,o,c);if(r.match(/^#+0+$/))return c+P(o,r.length-r.indexOf("0"));if(i=r.match(ge)){n=(""+t).replace(/^([^\.]+)$/,"$1."+ye(i[1])).replace(/\.$/,"."+ye(i[1]));n=n.replace(/\.(\d*)$/,function(e,r){return"."+r+br("0",ye(i[1]).length-r.length)});return r.indexOf("0.")!==-1?n:n.replace(/^0\./,".")}r=r.replace(/^#+([0.])/,"$1");if(i=r.match(/^(0*)\.(#*)$/)){return c+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".")}if(i=r.match(/^#{1,3},##0(\.?)$/))return c+ce(""+o);if(i=r.match(/^#,##0\.([#0]*0)$/)){return t<0?"-"+Fe(e,r,-t):ce(""+t)+"."+br("0",i[1].length)}if(i=r.match(/^#,#*,#0/))return Fe(e,r.replace(/^#,#*,/,""),t);if(i=r.match(/^([0#]+)(\\?-([0#]+))+$/)){n=N(Fe(e,r.replace(/[\\-]/g,""),t));s=0;return N(N(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return s<n.length?n.charAt(s++):e==="0"?"0":""}))}if(r.match(we)){n=Fe(e,"##########",t);return"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6)}var f="";if(i=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(i[4].length,7);l=J(o,Math.pow(10,s)-1,false);n=""+c;f=De("n",i[1],l[1]);if(f.charAt(f.length-1)==" ")f=f.substr(0,f.length-1)+"0";n+=f+i[2]+"/"+i[3];f=R(l[2],s);if(f.length<i[4].length)f=ye(i[4].substr(i[4].length-f.length))+f;n+=f;return n}if(i=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(Math.max(i[1].length,i[4].length),7);l=J(o,Math.pow(10,s)-1,true);return c+(l[0]||(l[1]?"":"0"))+" "+(l[1]?I(l[1],s)+i[2]+"/"+i[3]+R(l[2],s):br(" ",2*s+1+i[2].length+i[3].length))}if(i=r.match(/^[#0?]+$/)){n=""+t;if(r.length<=n.length)return n;return ye(r.substr(0,r.length-n.length))+n}if(i=r.match(/^([#0]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1");s=n.indexOf(".");var u=r.indexOf(".")-s,h=r.length-n.length-u;return ye(r.substr(0,u)+n+r.substr(r.length-h))}if(i=r.match(/^00,000\.([#0]*0)$/)){return t<0?"-"+Fe(e,r,-t):ce(""+t).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e})+"."+P(0,i[1].length)}switch(r){case"###,###":;case"##,###":;case"#,###":var p=ce(""+o);return p!=="0"?c+p:"";default:if(r.match(/\.[0#?]*$/))return Fe(e,r.slice(0,r.lastIndexOf(".")),t)+ye(r.slice(r.lastIndexOf(".")));}throw new Error("unsupported format |"+r+"|")}function De(e,r,t){return(t|0)===t?Fe(e,r,t):_e(e,r,t)}function Oe(e){var r=[];var t=false;for(var a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:;case 42:;case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n);n=a+1;}r[r.length]=e.substr(n);if(t===true)throw new Error("Format |"+e+"| unterminated string ");return r}var Me=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Ne(e){var r=0,t="",a="";while(r<e.length){switch(t=e.charAt(r)){case"G":if(W(e,r))r+=6;r++;break;case'"':for(;e.charCodeAt(++r)!==34&&r<e.length;){}++r;break;case"\\":r+=2;break;case"_":r+=2;break;case"@":++r;break;case"B":;case"b":if(e.charAt(r+1)==="1"||e.charAt(r+1)==="2")return true;case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":;case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":return true;case"A":;case"a":;case"上":if(e.substr(r,3).toUpperCase()==="A/P")return true;if(e.substr(r,5).toUpperCase()==="AM/PM")return true;if(e.substr(r,5).toUpperCase()==="上午/下午")return true;++r;break;case"[":a=t;while(e.charAt(r++)!=="]"&&r<e.length)a+=e.charAt(r);if(a.match(Me))return true;break;case".":;case"0":;case"#":while(r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||t=="\\"&&e.charAt(r+1)=="-"&&"0#".indexOf(e.charAt(r+2))>-1)){}break;case"?":while(e.charAt(++r)===t){}break;case"*":++r;if(e.charAt(r)==" "||e.charAt(r)=="*")++r;break;case"(":;case")":++r;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":while(r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1){}break;case" ":++r;break;default:++r;break;}}return false}function Pe(e,r,t,a){var n=[],i="",s=0,l="",o="t",c,f,u;var h="H";while(s<e.length){switch(l=e.charAt(s)){case"G":if(!W(e,s))throw new Error("unrecognized character "+l+" in "+e);n[n.length]={t:"G",v:"General"};s+=7;break;case'"':for(i="";(u=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(u);n[n.length]={t:"t",v:i};++s;break;case"\\":var p=e.charAt(++s),d=p==="("||p===")"?p:"t";n[n.length]={t:d,v:p};++s;break;case"_":n[n.length]={t:"t",v:" "};s+=2;break;case"@":n[n.length]={t:"T",v:r};++s;break;case"B":;case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(c==null){c=K(r,t,e.charAt(s+1)==="2");if(c==null)return""}n[n.length]={t:"X",v:e.substr(s,2)};o=l;s+=2;break};case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":l=l.toLowerCase();case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":if(r<0)return"";if(c==null){c=K(r,t);if(c==null)return""}i=l;while(++s<e.length&&e.charAt(s).toLowerCase()===l)i+=l;if(l==="m"&&o.toLowerCase()==="h")l="M";if(l==="h")l=h;n[n.length]={t:l,v:i};o=l;break;case"A":;case"a":;case"上":var m={t:l,v:l};if(c==null)c=K(r,t);if(e.substr(s,3).toUpperCase()==="A/P"){if(c!=null)m.v=c.H>=12?"P":"A";m.t="T";h="h";s+=3}else if(e.substr(s,5).toUpperCase()==="AM/PM"){if(c!=null)m.v=c.H>=12?"PM":"AM";m.t="T";s+=5;h="h"}else if(e.substr(s,5).toUpperCase()==="上午/下午"){if(c!=null)m.v=c.H>=12?"下午":"上午";m.t="T";s+=5;h="h"}else{m.t="t";++s}if(c==null&&m.t==="T")return"";n[n.length]=m;o=l;break;case"[":i=l;while(e.charAt(s++)!=="]"&&s<e.length)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Me)){if(c==null){c=K(r,t);if(c==null)return""}n[n.length]={t:"Z",v:i.toLowerCase()};o=i.charAt(1)}else if(i.indexOf("$")>-1){i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$";if(!Ne(e))n[n.length]={t:"t",v:i}}break;case".":if(c!=null){i=l;while(++s<e.length&&(l=e.charAt(s))==="0")i+=l;n[n.length]={t:"s",v:i};break};case"0":;case"#":i=l;while(++s<e.length&&"0#?.,E+-%".indexOf(l=e.charAt(s))>-1)i+=l;n[n.length]={t:"n",v:i};break;case"?":i=l;while(e.charAt(++s)===l)i+=l;n[n.length]={t:l,v:i};o=l;break;case"*":++s;if(e.charAt(s)==" "||e.charAt(s)=="*")++s;break;case"(":;case")":n[n.length]={t:a===1?"t":l,v:l};++s;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":i=l;while(s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:l,v:l};++s;break;case"$":n[n.length]={t:"t",v:"$"};++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(l)===-1)throw new Error("unrecognized character "+l+" in "+e);n[n.length]={t:"t",v:l};++s;break;}}var v=0,g=0,b;for(s=n.length-1,o="t";s>=0;--s){switch(n[s].t){case"h":;case"H":n[s].t=h;o="h";if(v<1)v=1;break;case"s":if(b=n[s].v.match(/\.0+$/))g=Math.max(g,b[0].length-1);if(v<3)v=3;case"d":;case"y":;case"M":;case"e":o=n[s].t;break;case"m":if(o==="s"){n[s].t="M";if(v<2)v=2}break;case"X":break;case"Z":if(v<1&&n[s].v.match(/[Hh]/))v=1;if(v<2&&n[s].v.match(/[Mm]/))v=2;if(v<3&&n[s].v.match(/[Ss]/))v=3;}}switch(v){case 0:break;case 1:if(c.u>=.5){c.u=0;++c.S}if(c.S>=60){c.S=0;++c.M}if(c.M>=60){c.M=0;++c.H}break;case 2:if(c.u>=.5){c.u=0;++c.S}if(c.S>=60){c.S=0;++c.M}break;}var w="",y;for(s=0;s<n.length;++s){switch(n[s].t){case"t":;case"T":;case" ":;case"D":break;case"X":n[s].v="";n[s].t=";";break;case"d":;case"m":;case"y":;case"h":;case"H":;case"M":;case"s":;case"e":;case"b":;case"Z":n[s].v=oe(n[s].t.charCodeAt(0),n[s].v,c,g);n[s].t="t";break;case"n":;case"?":y=s+1;while(n[y]!=null&&((l=n[y].t)==="?"||l==="D"||(l===" "||l==="t")&&n[y+1]!=null&&(n[y+1].t==="?"||n[y+1].t==="t"&&n[y+1].v==="/")||n[s].t==="("&&(l===" "||l==="n"||l===")")||l==="t"&&(n[y].v==="/"||n[y].v===" "&&n[y+1]!=null&&n[y+1].t=="?"))){n[s].v+=n[y].v;n[y]={v:"",t:";"};++y}w+=n[s].v;s=y-1;break;case"G":n[s].t="t";n[s].v=se(r,t);break;}}var k="",x,S;if(w.length>0){if(w.charCodeAt(0)==40){x=r<0&&w.charCodeAt(0)===45?-r:r;S=De("n",w,x)}else{x=r<0&&a>1?-r:r;S=De("n",w,x);if(x<0&&n[0]&&n[0].t=="t"){S=S.substr(1);n[0].v="-"+n[0].v}}y=S.length-1;var C=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){C=s;break}var _=n.length;if(C===n.length&&S.indexOf("E")===-1){for(s=n.length-1;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;if(y>=n[s].v.length-1){y-=n[s].v.length;n[s].v=S.substr(y+1,n[s].v.length)}else if(y<0)n[s].v="";else{n[s].v=S.substr(0,y+1);y=-1}n[s].t="t";_=s}if(y>=0&&_<n.length)n[_].v=S.substr(0,y+1)+n[_].v}else if(C!==n.length&&S.indexOf("E")===-1){y=S.indexOf(".")-1;for(s=C;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;f=n[s].v.indexOf(".")>-1&&s===C?n[s].v.indexOf(".")-1:n[s].v.length-1;k=n[s].v.substr(f+1);for(;f>=0;--f){if(y>=0&&(n[s].v.charAt(f)==="0"||n[s].v.charAt(f)==="#"))k=S.charAt(y--)+k}n[s].v=k;n[s].t="t";_=s}if(y>=0&&_<n.length)n[_].v=S.substr(0,y+1)+n[_].v;y=S.indexOf(".")+1;for(s=C;s<n.length;++s){if(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==C)continue;f=n[s].v.indexOf(".")>-1&&s===C?n[s].v.indexOf(".")+1:0;k=n[s].v.substr(0,f);for(;f<n[s].v.length;++f){if(y<S.length)k+=S.charAt(y++)}n[s].v=k;n[s].t="t";_=s}}}for(s=0;s<n.length;++s)if(n[s]!=null&&"n?".indexOf(n[s].t)>-1){x=a>1&&r<0&&s>0&&n[s-1].v==="-"?-r:r;n[s].v=De(n[s].t,n[s].v,x);n[s].t="t"}var A="";for(s=0;s!==n.length;++s)if(n[s]!=null)A+=n[s].v;return A}var Ie=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Re(e,r){if(r==null)return false;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return true;break;case">":if(e>t)return true;break;case"<":if(e<t)return true;break;case"<>":if(e!=t)return true;break;case">=":if(e>=t)return true;break;case"<=":if(e<=t)return true;break;}return false}function Le(e,r){var t=Oe(e);var a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1)--a;if(t.length>4)throw new Error("cannot find right format for |"+t.join("|")+"|");if(typeof r!=="number")return[4,t.length===4||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"];break;case 4:break;}var i=r>0?t[0]:r<0?t[1]:t[2];if(t[0].indexOf("[")===-1&&t[1].indexOf("[")===-1)return[a,i];if(t[0].match(/\[[=<>]/)!=null||t[1].match(/\[[=<>]/)!=null){var s=t[0].match(Ie);var l=t[1].match(Ie);return Re(r,s)?[a,t[0]]:Re(r,l)?[a,t[1]]:[a,t[s!=null&&l!=null?2:1]]}return[a,i]}function Be(e,r,t){if(t==null)t={};var a="";switch(typeof e){case"string":if(e=="m/d/yy"&&t.dateNF)a=t.dateNF;else a=e;break;case"number":if(e==14&&t.dateNF)a=t.dateNF;else a=(t.table!=null?t.table:X)[e];if(a==null)a=t.table&&t.table[G[e]]||X[G[e]];if(a==null)a=Y[e]||"General";break;}if(W(a,0))return se(r,t);if(r instanceof Date)r=ee(r,t.date1904);var n=Le(a,r);if(W(n[1]))return se(r,t);if(r===true)r="TRUE";else if(r===false)r="FALSE";else if(r===""||r==null)return"";return Pe(n[1],r,t,n[0])}function $e(e,r){if(typeof r!="number"){r=+r||-1;for(var t=0;t<392;++t){if(X[t]==undefined){if(r<0)r=t;continue}if(X[t]==e){r=t;break}}if(r<0)r=391}X[r]=e;return r}function ze(e){for(var r=0;r!=392;++r)if(e[r]!==undefined)$e(e[r],r)}function Ue(){X=V()}var We={format:Be,load:$e,_table:X,load_table:ze,parse_date_code:K,is_date:Ne,get_table:function _c(){return We._table=X}};var je={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"};var He=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ve(e){var r=typeof e=="number"?X[e]:e;r=r.replace(He,"(\\d+)");return new RegExp("^"+r+"$")}function Xe(e,r,t){var a=-1,n=-1,i=-1,s=-1,l=-1,o=-1;(r.match(He)||[]).forEach(function(e,r){var c=parseInt(t[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=c;break;case"d":i=c;break;case"h":s=c;break;case"s":o=c;break;case"m":if(s>=0)l=c;else n=c;break;}});if(o>=0&&l==-1&&n>=0){l=n;n=-1}var c=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);if(c.length==7)c="0"+c;if(c.length==8)c="20"+c;var f=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);if(s==-1&&l==-1&&o==-1)return c;if(a==-1&&n==-1&&i==-1)return f;return c+"T"+f}var Ge=function(){var e={};e.version="1.2.0";function r(){var e=0,r=new Array(256);for(var t=0;t!=256;++t){e=t;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;r[t]=e}return typeof Int32Array!=="undefined"?new Int32Array(r):r}var t=r();function a(e){var r=0,t=0,a=0,n=typeof Int32Array!=="undefined"?new Int32Array(4096):new Array(4096);for(a=0;a!=256;++a)n[a]=e[a];for(a=0;a!=256;++a){t=e[a];for(r=256+a;r<4096;r+=256)t=n[r]=t>>>8^e[t&255]}var i=[];for(a=1;a!=16;++a)i[a-1]=typeof Int32Array!=="undefined"?n.subarray(a*256,a*256+256):n.slice(a*256,a*256+256);return i}var n=a(t);var i=n[0],s=n[1],l=n[2],o=n[3],c=n[4];var f=n[5],u=n[6],h=n[7],p=n[8],d=n[9];var m=n[10],v=n[11],g=n[12],b=n[13],w=n[14];function y(e,r){var a=r^-1;for(var n=0,i=e.length;n<i;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a}function k(e,r){var a=r^-1,n=e.length-15,y=0;for(;y<n;)a=w[e[y++]^a&255]^b[e[y++]^a>>8&255]^g[e[y++]^a>>16&255]^v[e[y++]^a>>>24]^m[e[y++]]^d[e[y++]]^p[e[y++]]^h[e[y++]]^u[e[y++]]^f[e[y++]]^c[e[y++]]^o[e[y++]]^l[e[y++]]^s[e[y++]]^i[e[y++]]^t[e[y++]];n+=15;while(y<n)a=a>>>8^t[(a^e[y++])&255];return~a}function x(e,r){var a=r^-1;for(var n=0,i=e.length,s=0,l=0;n<i;){
s=e.charCodeAt(n++);if(s<128){a=a>>>8^t[(a^s)&255]}else if(s<2048){a=a>>>8^t[(a^(192|s>>6&31))&255];a=a>>>8^t[(a^(128|s&63))&255]}else if(s>=55296&&s<57344){s=(s&1023)+64;l=e.charCodeAt(n++)&1023;a=a>>>8^t[(a^(240|s>>8&7))&255];a=a>>>8^t[(a^(128|s>>2&63))&255];a=a>>>8^t[(a^(128|l>>6&15|(s&3)<<4))&255];a=a>>>8^t[(a^(128|l&63))&255]}else{a=a>>>8^t[(a^(224|s>>12&15))&255];a=a>>>8^t[(a^(128|s>>6&63))&255];a=a>>>8^t[(a^(128|s&63))&255]}}return~a}e.table=t;e.bstr=y;e.buf=k;e.str=x;return e}();var Ye=function Ac(){var e={};e.version="1.2.1";function r(e,r){var t=e.split("/"),a=r.split("/");for(var n=0,i=0,s=Math.min(t.length,a.length);n<s;++n){if(i=t[n].length-a[n].length)return i;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}function t(e){if(e.charAt(e.length-1)=="/")return e.slice(0,-1).indexOf("/")===-1?e:t(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(0,r+1)}function a(e){if(e.charAt(e.length-1)=="/")return a(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(r+1)}function n(e,r){if(typeof r==="string")r=new Date(r);var t=r.getHours();t=t<<6|r.getMinutes();t=t<<5|r.getSeconds()>>>1;e._W(2,t);var a=r.getFullYear()-1980;a=a<<4|r.getMonth()+1;a=a<<5|r.getDate();e._W(2,a)}function i(e){var r=e._R(2)&65535;var t=e._R(2)&65535;var a=new Date;var n=t&31;t>>>=5;var i=t&15;t>>>=4;a.setMilliseconds(0);a.setFullYear(t+1980);a.setMonth(i-1);a.setDate(n);var s=r&31;r>>>=5;var l=r&63;r>>>=6;a.setHours(r);a.setMinutes(l);a.setSeconds(s<<1);return a}function s(e){ca(e,0);var r={};var t=0;while(e.l<=e.length-4){var a=e._R(2);var n=e._R(2),i=e.l+n;var s={};switch(a){case 21589:{t=e._R(1);if(t&1)s.mtime=e._R(4);if(n>5){if(t&2)s.atime=e._R(4);if(t&4)s.ctime=e._R(4)}if(s.mtime)s.mt=new Date(s.mtime*1e3)}break;}e.l=i;r[a]=s}return r}var l;function o(){return l||(l=undefined)}function c(e,r){if(e[0]==80&&e[1]==75)return Oe(e,r);if((e[0]|32)==109&&(e[1]|32)==105)return ze(e,r);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t=3;var a=512;var n=0;var i=0;var s=0;var l=0;var o=0;var c=[];var d=e.slice(0,512);ca(d,0);var v=f(d);t=v[0];switch(t){case 3:a=512;break;case 4:a=4096;break;case 0:if(v[1]==0)return Oe(e,r);default:throw new Error("Major Version: Expected 3 or 4 saw "+t);}if(a!==512){d=e.slice(0,a);ca(d,28)}var b=e.slice(0,a);u(d,t);var w=d._R(4,"i");if(t===3&&w!==0)throw new Error("# Directory Sectors: Expected 0 saw "+w);d.l+=4;s=d._R(4,"i");d.l+=4;d.chk("00100000","Mini Stream Cutoff Size: ");l=d._R(4,"i");n=d._R(4,"i");o=d._R(4,"i");i=d._R(4,"i");for(var y=-1,k=0;k<109;++k){y=d._R(4,"i");if(y<0)break;c[k]=y}var x=h(e,a);m(o,i,x,a,c);var S=g(x,s,c,a);S[s].name="!Directory";if(n>0&&l!==B)S[l].name="!MiniFAT";S[c[0]].name="!FAT";S.fat_addrs=c;S.ssz=a;var C={},A=[],T=[],E=[];_(s,S,x,A,n,C,T,l);p(T,E,A);A.shift();var F={FileIndex:T,FullPaths:E};if(r&&r.raw)F.raw={header:b,sectors:x};return F}function f(e){if(e[e.l]==80&&e[e.l+1]==75)return[0,0];e.chk($,"Header Signature: ");e.l+=16;var r=e._R(2,"u");return[e._R(2,"u"),r]}function u(e,r){var t=9;e.l+=2;switch(t=e._R(2)){case 9:if(r!=3)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(r!=4)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t);}e.chk("0600","Mini Sector Shift: ");e.chk("000000000000","Reserved: ")}function h(e,r){var t=Math.ceil(e.length/r)-1;var a=[];for(var n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);a[t-1]=e.slice(t*r);return a}function p(e,r,t){var a=0,n=0,i=0,s=0,l=0,o=t.length;var c=[],f=[];for(;a<o;++a){c[a]=f[a]=a;r[a]=t[a]}for(;l<f.length;++l){a=f[l];n=e[a].L;i=e[a].R;s=e[a].C;if(c[a]===a){if(n!==-1&&c[n]!==n)c[a]=c[n];if(i!==-1&&c[i]!==i)c[a]=c[i]}if(s!==-1)c[s]=a;if(n!==-1&&a!=c[a]){c[n]=c[a];if(f.lastIndexOf(n)<l)f.push(n)}if(i!==-1&&a!=c[a]){c[i]=c[a];if(f.lastIndexOf(i)<l)f.push(i)}}for(a=1;a<o;++a)if(c[a]===a){if(i!==-1&&c[i]!==i)c[a]=c[i];else if(n!==-1&&c[n]!==n)c[a]=c[n]}for(a=1;a<o;++a){if(e[a].type===0)continue;l=a;if(l!=c[l])do{l=c[l];r[a]=r[l]+"/"+r[a]}while(l!==0&&-1!==c[l]&&l!=c[l]);c[a]=-1}r[0]+="/";for(a=1;a<o;++a){if(e[a].type!==2)r[a]+="/"}}function d(e,r,t){var a=e.start,n=e.size;var i=[];var s=a;while(t&&n>0&&s>=0){i.push(r.slice(s*L,s*L+L));n-=L;s=ra(t,s*4)}if(i.length===0)return ua(0);return F(i).slice(0,e.size)}function m(e,r,t,a,n){var i=B;if(e===B){if(r!==0)throw new Error("DIFAT chain shorter than expected")}else if(e!==-1){var s=t[e],l=(a>>>2)-1;if(!s)return;for(var o=0;o<l;++o){if((i=ra(s,o*4))===B)break;n.push(i)}m(ra(s,a-4),r-1,t,a,n)}}function v(e,r,t,a,n){var i=[],s=[];if(!n)n=[];var l=a-1,o=0,c=0;for(o=r;o>=0;){n[o]=true;i[i.length]=o;s.push(e[o]);var f=t[Math.floor(o*4/a)];c=o*4&l;if(a<4+c)throw new Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=ra(e[f],c)}return{nodes:i,data:Dt([s])}}function g(e,r,t,a){var n=e.length,i=[];var s=[],l=[],o=[];var c=a-1,f=0,u=0,h=0,p=0;for(f=0;f<n;++f){l=[];h=f+r;if(h>=n)h-=n;if(s[h])continue;o=[];var d=[];for(u=h;u>=0;){d[u]=true;s[u]=true;l[l.length]=u;o.push(e[u]);var m=t[Math.floor(u*4/a)];p=u*4&c;if(a<4+p)throw new Error("FAT boundary crossed: "+u+" 4 "+a);if(!e[m])break;u=ra(e[m],p);if(d[u])break}i[h]={nodes:l,data:Dt([o])}}return i}function _(e,r,t,a,n,i,s,l){var o=0,c=a.length?2:0;var f=r[e].data;var u=0,h=0,p;for(;u<f.length;u+=128){var m=f.slice(u,u+128);ca(m,64);h=m._R(2);p=Mt(m,0,h-c);a.push(p);var g={name:p,type:m._R(1),color:m._R(1),L:m._R(4,"i"),R:m._R(4,"i"),C:m._R(4,"i"),clsid:m._R(16),state:m._R(4,"i"),start:0,size:0};var b=m._R(2)+m._R(2)+m._R(2)+m._R(2);if(b!==0)g.ct=A(m,m.l-8);var w=m._R(2)+m._R(2)+m._R(2)+m._R(2);if(w!==0)g.mt=A(m,m.l-8);g.start=m._R(4,"i");g.size=m._R(4,"i");if(g.size<0&&g.start<0){g.size=g.type=0;g.start=B;g.name=""}if(g.type===5){o=g.start;if(n>0&&o!==B)r[o].name="!StreamData"}else if(g.size>=4096){g.storage="fat";if(r[g.start]===undefined)r[g.start]=v(t,g.start,r.fat_addrs,r.ssz);r[g.start].name=g.name;g.content=r[g.start].data.slice(0,g.size)}else{g.storage="minifat";if(g.size<0)g.size=0;else if(o!==B&&g.start!==B&&r[o]){g.content=d(g,r[o].data,(r[l]||{}).data)}}if(g.content)ca(g.content,0);i[p]=g;s.push(g)}}function A(e,r){return new Date((ea(e,r+4)/1e7*Math.pow(2,32)+ea(e,r)/1e7-11644473600)*1e3)}function T(e,r){o();return c(l.readFileSync(e),r)}function E(e,r){var t=r&&r.type;if(!t){if(y&&Buffer.isBuffer(e))t="buffer"}switch(t||"base64"){case"file":return T(e,r);case"base64":return c(C(w(e)),r);case"binary":return c(C(e),r);}return c(e,r)}function D(e,r){var t=r||{},a=t.root||"Root Entry";if(!e.FullPaths)e.FullPaths=[];if(!e.FileIndex)e.FileIndex=[];if(e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");if(e.FullPaths.length===0){e.FullPaths[0]=a+"/";e.FileIndex[0]={name:a,type:5}}if(t.CLSID)e.FileIndex[0].clsid=t.CLSID;N(e)}function N(e){var r="Sh33tJ5";if(Ye.find(e,"/"+r))return;var t=ua(4);t[0]=55;t[1]=t[3]=50;t[2]=54;e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69});e.FullPaths.push(e.FullPaths[0]+r);P(e)}function P(e,n){D(e);var i=false,s=false;for(var l=e.FullPaths.length-1;l>=0;--l){var o=e.FileIndex[l];switch(o.type){case 0:if(s)i=true;else{e.FileIndex.pop();e.FullPaths.pop()}break;case 1:;case 2:;case 5:s=true;if(isNaN(o.R*o.L*o.C))i=true;if(o.R>-1&&o.L>-1&&o.R==o.L)i=true;break;default:i=true;break;}}if(!i&&!n)return;var c=new Date(1987,1,19),f=0;var u=Object.create?Object.create(null):{};var h=[];for(l=0;l<e.FullPaths.length;++l){u[e.FullPaths[l]]=true;if(e.FileIndex[l].type===0)continue;h.push([e.FullPaths[l],e.FileIndex[l]])}for(l=0;l<h.length;++l){var p=t(h[l][0]);s=u[p];if(!s){h.push([p,{name:a(p).replace("/",""),type:1,clsid:U,ct:c,mt:c,content:null}]);u[p]=true}}h.sort(function(e,t){return r(e[0],t[0])});e.FullPaths=[];e.FileIndex=[];for(l=0;l<h.length;++l){e.FullPaths[l]=h[l][0];e.FileIndex[l]=h[l][1]}for(l=0;l<h.length;++l){var d=e.FileIndex[l];var m=e.FullPaths[l];d.name=a(m).replace("/","");d.L=d.R=d.C=-(d.color=1);d.size=d.content?d.content.length:0;d.start=0;d.clsid=d.clsid||U;if(l===0){d.C=h.length>1?1:-1;d.size=0;d.type=5}else if(m.slice(-1)=="/"){for(f=l+1;f<h.length;++f)if(t(e.FullPaths[f])==m)break;d.C=f>=h.length?-1:f;for(f=l+1;f<h.length;++f)if(t(e.FullPaths[f])==t(m))break;d.R=f>=h.length?-1:f;d.type=1}else{if(t(e.FullPaths[l+1]||"")==t(m))d.R=l+1;d.type=2}}}function I(e,r){var t=r||{};if(t.fileType=="mad")return Ue(e,t);P(e);switch(t.fileType){case"zip":return Ne(e,t);}var a=function(e){var r=0,t=0;for(var a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(!n.content)continue;var i=n.content.length;if(i>0){if(i<4096)r+=i+63>>6;else t+=i+511>>9}}var s=e.FullPaths.length+3>>2;var l=r+7>>3;var o=r+127>>7;var c=l+t+s+o;var f=c+127>>7;var u=f<=109?0:Math.ceil((f-109)/127);while(c+f+u+127>>7>f)u=++f<=109?0:Math.ceil((f-109)/127);var h=[1,u,f,o,s,t,r,0];e.FileIndex[0].size=r<<6;h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3);return h}(e);var n=ua(a[7]<<9);var i=0,s=0;{for(i=0;i<8;++i)n._W(1,z[i]);for(i=0;i<8;++i)n._W(2,0);n._W(2,62);n._W(2,3);n._W(2,65534);n._W(2,9);n._W(2,6);for(i=0;i<3;++i)n._W(2,0);n._W(4,0);n._W(4,a[2]);n._W(4,a[0]+a[1]+a[2]+a[3]-1);n._W(4,0);n._W(4,1<<12);n._W(4,a[3]?a[0]+a[1]+a[2]-1:B);n._W(4,a[3]);n._W(-4,a[1]?a[0]-1:B);n._W(4,a[1]);for(i=0;i<109;++i)n._W(-4,i<a[2]?a[1]+i:-1)}if(a[1]){for(s=0;s<a[1];++s){for(;i<236+s*127;++i)n._W(-4,i<a[2]?a[1]+i:-1);n._W(-4,s===a[1]-1?B:s+1)}}var l=function(e){for(s+=e;i<s-1;++i)n._W(-4,i+1);if(e){++i;n._W(-4,B)}};s=i=0;for(s+=a[1];i<s;++i)n._W(-4,W.DIFSECT);for(s+=a[2];i<s;++i)n._W(-4,W.FATSECT);l(a[3]);l(a[4]);var o=0,c=0;var f=e.FileIndex[0];for(;o<e.FileIndex.length;++o){f=e.FileIndex[o];if(!f.content)continue;c=f.content.length;if(c<4096)continue;f.start=s;l(c+511>>9)}l(a[6]+7>>3);while(n.l&511)n._W(-4,W.ENDOFCHAIN);s=i=0;for(o=0;o<e.FileIndex.length;++o){f=e.FileIndex[o];if(!f.content)continue;c=f.content.length;if(!c||c>=4096)continue;f.start=s;l(c+63>>6)}while(n.l&511)n._W(-4,W.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(!u||u.length===0){for(o=0;o<17;++o)n._W(4,0);for(o=0;o<3;++o)n._W(4,-1);for(o=0;o<12;++o)n._W(4,0);continue}f=e.FileIndex[i];if(i===0)f.start=f.size?f.start-1:B;var h=i===0&&t.root||f.name;c=2*(h.length+1);n._W(64,h,"utf16le");n._W(2,c);n._W(1,f.type);n._W(1,f.color);n._W(-4,f.L);n._W(-4,f.R);n._W(-4,f.C);if(!f.clsid)for(o=0;o<4;++o)n._W(4,0);else n._W(16,f.clsid,"hex");n._W(4,f.state||0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,f.start);n._W(4,f.size);n._W(4,0)}for(i=1;i<e.FileIndex.length;++i){f=e.FileIndex[i];if(f.size>=4096){n.l=f.start+1<<9;if(y&&Buffer.isBuffer(f.content)){f.content.copy(n,n.l,0,f.size);n.l+=f.size+511&-512}else{for(o=0;o<f.size;++o)n._W(1,f.content[o]);for(;o&511;++o)n._W(1,0)}}}for(i=1;i<e.FileIndex.length;++i){f=e.FileIndex[i];if(f.size>0&&f.size<4096){if(y&&Buffer.isBuffer(f.content)){f.content.copy(n,n.l,0,f.size);n.l+=f.size+63&-64}else{for(o=0;o<f.size;++o)n._W(1,f.content[o]);for(;o&63;++o)n._W(1,0)}}}if(y){n.l=n.length}else{while(n.l<n.length)n._W(1,0)}return n}function R(e,r){var t=e.FullPaths.map(function(e){return e.toUpperCase()});var a=t.map(function(e){var r=e.split("/");return r[r.length-(e.slice(-1)=="/"?2:1)]});var n=false;if(r.charCodeAt(0)===47){n=true;r=t[0].slice(0,-1)+r}else n=r.indexOf("/")!==-1;var i=r.toUpperCase();var s=n===true?t.indexOf(i):a.indexOf(i);if(s!==-1)return e.FileIndex[s];var l=!i.match(M);i=i.replace(O,"");if(l)i=i.replace(M,"!");for(s=0;s<t.length;++s){if((l?t[s].replace(M,"!"):t[s]).replace(O,"")==i)return e.FileIndex[s];if((l?a[s].replace(M,"!"):a[s]).replace(O,"")==i)return e.FileIndex[s]}return null}var L=64;var B=-2;var $="d0cf11e0a1b11ae1";var z=[208,207,17,224,161,177,26,225];var U="00000000000000000000000000000000";var W={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:B,FREESECT:-1,HEADER_SIGNATURE:$,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:U,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function j(e,r,t){o();var a=I(e,t);l.writeFileSync(r,a)}function H(e){var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function V(e,r){var t=I(e,r);switch(r&&r.type||"buffer"){case"file":o();l.writeFileSync(r.filename,t);return t;case"binary":return typeof t=="string"?t:H(t);case"base64":return b(typeof t=="string"?t:H(t));case"buffer":if(y)return Buffer.isBuffer(t)?t:k(t);case"array":return typeof t=="string"?C(t):t;}return t}var X;function G(e){try{var r=e.InflateRaw;var t=new r;t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag);if(t.bytesRead)X=e;else throw new Error("zlib does not expose bytesRead")}catch(a){console.error("cannot use native zlib: "+(a.message||a))}}function Y(e,r){if(!X)return Fe(e,r);var t=X.InflateRaw;var a=new t;var n=a._processChunk(e.slice(e.l),a._finishFlushFlag);e.l+=a.bytesRead;return n}function J(e){return X?X.deflateRawSync(e):ke(e)}var K=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];var q=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258];var Z=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Q(e){var r=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(r>>16|r>>8|r)&255}var ee=typeof Uint8Array!=="undefined";var re=ee?new Uint8Array(1<<8):[];for(var te=0;te<1<<8;++te)re[te]=Q(te);function ae(e,r){var t=re[e&255];if(r<=8)return t>>>8-r;t=t<<8|re[e>>8&255];if(r<=16)return t>>>16-r;t=t<<8|re[e>>16&255];return t>>>24-r}function ne(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}function ie(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function se(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=4?0:e[a+1]<<8))>>>t&15}function le(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function oe(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function ce(e,r,t){var a=r&7,n=r>>>3,i=(1<<t)-1;var s=e[n]>>>a;if(t<8-a)return s&i;s|=e[n+1]<<8-a;if(t<16-a)return s&i;s|=e[n+2]<<16-a;if(t<24-a)return s&i;s|=e[n+3]<<24-a;return s&i}function fe(e,r,t){var a=r&7,n=r>>>3;if(a<=5)e[n]|=(t&7)<<a;else{e[n]|=t<<a&255;e[n+1]=(t&7)>>8-a}return r+3}function ue(e,r,t){var a=r&7,n=r>>>3;t=(t&1)<<a;e[n]|=t;return r+1}function he(e,r,t){var a=r&7,n=r>>>3;t<<=a;e[n]|=t&255;t>>>=8;e[n+1]=t;return r+8}function pe(e,r,t){var a=r&7,n=r>>>3;t<<=a;e[n]|=t&255;t>>>=8;e[n+1]=t&255;e[n+2]=t>>>8;return r+16}function de(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(y){var i=S(a);if(e.copy)e.copy(i);else for(;n<e.length;++n)i[n]=e[n];return i}else if(ee){var s=new Uint8Array(a);if(s.set)s.set(e);else for(;n<t;++n)s[n]=e[n];return s}e.length=a;return e}function me(e){var r=new Array(e);for(var t=0;t<e;++t)r[t]=0;return r}function ve(e,r,t){var a=1,n=0,i=0,s=0,l=0,o=e.length;var c=ee?new Uint16Array(32):me(32);for(i=0;i<32;++i)c[i]=0;for(i=o;i<t;++i)e[i]=0;o=e.length;var f=ee?new Uint16Array(o):me(o);for(i=0;i<o;++i){c[n=e[i]]++;if(a<n)a=n;f[i]=0}c[0]=0;for(i=1;i<=a;++i)c[i+16]=l=l+c[i-1]<<1;for(i=0;i<o;++i){l=e[i];if(l!=0)f[i]=c[l+16]++}var u=0;for(i=0;i<o;++i){u=e[i];if(u!=0){l=ae(f[i],a)>>a-u;for(s=(1<<a+4-u)-1;s>=0;--s)r[l|s<<u]=u&15|i<<4}}return a}var ge=ee?new Uint16Array(512):me(512);var be=ee?new Uint16Array(32):me(32);if(!ee){for(var we=0;we<512;++we)ge[we]=0;for(we=0;we<32;++we)be[we]=0}(function(){var e=[];var r=0;for(;r<32;r++)e.push(5);ve(e,be,32);var t=[];r=0;for(;r<=143;r++)t.push(8);for(;r<=255;r++)t.push(9);for(;r<=279;r++)t.push(7);for(;r<=287;r++)t.push(8);ve(t,ge,288)})();var ye=function Je(){var e=ee?new Uint8Array(32768):[];var r=0,t=0;for(;r<Z.length-1;++r){for(;t<Z[r+1];++t)e[t]=r}for(;t<32768;++t)e[t]=29;var a=ee?new Uint8Array(259):[];for(r=0,t=0;r<q.length-1;++r){for(;t<q[r+1];++t)a[t]=r}function n(e,r){var t=0;while(t<e.length){var a=Math.min(65535,e.length-t);var n=t+a==e.length;r._W(1,+n);r._W(2,a);r._W(2,~a&65535);while(a-- >0)r[r.l++]=e[t++]}return r.l}function i(r,t){var n=0;var i=0;var s=ee?new Uint16Array(32768):[];while(i<r.length){var l=Math.min(65535,r.length-i);if(l<10){n=fe(t,n,+!!(i+l==r.length));if(n&7)n+=8-(n&7);t.l=n/8|0;t._W(2,l);t._W(2,~l&65535);while(l-- >0)t[t.l++]=r[i++];n=t.l*8;continue}n=fe(t,n,+!!(i+l==r.length)+2);var o=0;while(l-- >0){var c=r[i];o=(o<<5^c)&32767;var f=-1,u=0;if(f=s[o]){f|=i&~32767;if(f>i)f-=32768;if(f<i)while(r[f+u]==r[i+u]&&u<250)++u}if(u>2){c=a[u];if(c<=22)n=he(t,n,re[c+1]>>1)-1;else{he(t,n,3);n+=5;he(t,n,re[c-23]>>5);n+=3}var h=c<8?0:c-4>>2;if(h>0){pe(t,n,u-q[c]);n+=h}c=e[i-f];n=he(t,n,re[c]>>3);n-=3;var p=c<4?0:c-2>>1;if(p>0){pe(t,n,i-f-Z[c]);n+=p}for(var d=0;d<u;++d){s[o]=i&32767;o=(o<<5^r[i])&32767;++i}l-=u-1}else{if(c<=143)c=c+48;else n=ue(t,n,1);n=he(t,n,re[c]);s[o]=i&32767;++i}}n=he(t,n,0)-1}t.l=(n+7)/8|0;return t.l}return function s(e,r){if(e.length<8)return n(e,r);return i(e,r)}}();function ke(e){var r=ua(50+Math.floor(e.length*1.1));var t=ye(e,r);return r.slice(0,t)}var xe=ee?new Uint16Array(32768):me(32768);var Se=ee?new Uint16Array(32768):me(32768);var Ce=ee?new Uint16Array(128):me(128);var _e=1,Ae=1;function Te(e,r){var t=le(e,r)+257;r+=5;var a=le(e,r)+1;r+=5;var n=se(e,r)+4;r+=4;var i=0;var s=ee?new Uint8Array(19):me(19);var l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var o=1;var c=ee?new Uint8Array(8):me(8);var f=ee?new Uint8Array(8):me(8);var u=s.length;for(var h=0;h<n;++h){s[K[h]]=i=ie(e,r);if(o<i)o=i;c[i]++;r+=3}var p=0;c[0]=0;for(h=1;h<=o;++h)f[h]=p=p+c[h-1]<<1;for(h=0;h<u;++h)if((p=s[h])!=0)l[h]=f[p]++;var d=0;for(h=0;h<u;++h){d=s[h];if(d!=0){p=re[l[h]]>>8-d;for(var m=(1<<7-d)-1;m>=0;--m)Ce[p|m<<d]=d&7|h<<3}}var v=[];o=1;for(;v.length<t+a;){p=Ce[oe(e,r)];r+=p&7;switch(p>>>=3){case 16:i=3+ne(e,r);r+=2;p=v[v.length-1];while(i-- >0)v.push(p);break;case 17:i=3+ie(e,r);r+=3;while(i-- >0)v.push(0);break;case 18:i=11+oe(e,r);r+=7;while(i-- >0)v.push(0);break;default:v.push(p);if(o<p)o=p;break;}}var g=v.slice(0,t),b=v.slice(t);for(h=t;h<286;++h)g[h]=0;for(h=a;h<30;++h)b[h]=0;_e=ve(g,xe,286);Ae=ve(b,Se,30);return r}function Ee(e,r){if(e[0]==3&&!(e[1]&3)){return[x(r),2]}var t=0;var a=0;var n=S(r?r:1<<18);var i=0;var s=n.length>>>0;var l=0,o=0;while((a&1)==0){a=ie(e,t);t+=3;if(a>>>1==0){if(t&7)t+=8-(t&7);var c=e[t>>>3]|e[(t>>>3)+1]<<8;t+=32;if(c>0){if(!r&&s<i+c){n=de(n,i+c);s=n.length}while(c-- >0){n[i++]=e[t>>>3];t+=8}}continue}else if(a>>1==1){l=9;o=5}else{t=Te(e,t);l=_e;o=Ae}for(;;){if(!r&&s<i+32767){n=de(n,i+32767);s=n.length}var f=ce(e,t,l);var u=a>>>1==1?ge[f]:xe[f];t+=u&15;u>>>=4;if((u>>>8&255)===0)n[i++]=u;else if(u==256)break;else{u-=257;var h=u<8?0:u-4>>2;if(h>5)h=0;var p=i+q[u];if(h>0){p+=ce(e,t,h);t+=h}f=ce(e,t,o);u=a>>>1==1?be[f]:Se[f];t+=u&15;u>>>=4;var d=u<4?0:u-2>>1;var m=Z[u];if(d>0){m+=ce(e,t,d);t+=d}if(!r&&s<p){n=de(n,p+100);s=n.length}while(i<p){n[i]=n[i-m];++i}}}}if(r)return[n,t+7>>>3];return[n.slice(0,i),t+7>>>3]}function Fe(e,r){var t=e.slice(e.l||0);var a=Ee(t,r);e.l+=a[1];return a[0]}function De(e,r){if(e){if(typeof console!=="undefined")console.error(r)}else throw new Error(r)}function Oe(e,r){var t=e;ca(t,0);var a=[],n=[];var i={FileIndex:a,FullPaths:n};D(i,{root:r.root});var l=t.length-4;while((t[l]!=80||t[l+1]!=75||t[l+2]!=5||t[l+3]!=6)&&l>=0)--l;t.l=l+4;t.l+=4;var o=t._R(2);t.l+=6;var c=t._R(4);t.l=c;for(l=0;l<o;++l){t.l+=20;var f=t._R(4);var u=t._R(4);var h=t._R(2);var p=t._R(2);var d=t._R(2);t.l+=8;var m=t._R(4);var v=s(t.slice(t.l+h,t.l+h+p));t.l+=h+p+d;var g=t.l;t.l=m+4;Me(t,f,u,i,v);t.l=g}return i}function Me(e,r,t,a,n){e.l+=2;var l=e._R(2);var o=e._R(2);var c=i(e);if(l&8257)throw new Error("Unsupported ZIP encryption");var f=e._R(4);var u=e._R(4);var h=e._R(4);var p=e._R(2);var d=e._R(2);var m="";for(var v=0;v<p;++v)m+=String.fromCharCode(e[e.l++]);if(d){var g=s(e.slice(e.l,e.l+d));if((g[21589]||{}).mt)c=g[21589].mt;if(((n||{})[21589]||{}).mt)c=n[21589].mt}e.l+=d;var b=e.slice(e.l,e.l+u);switch(o){case 8:b=Y(e,h);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o);}var w=false;if(l&8){f=e._R(4);if(f==134695760){f=e._R(4);w=true}u=e._R(4);h=e._R(4)}if(u!=r)De(w,"Bad compressed size: "+r+" != "+u);if(h!=t)De(w,"Bad uncompressed size: "+t+" != "+h);je(a,m,b,{unsafe:true,mt:c})}function Ne(e,r){var t=r||{};var a=[],i=[];var s=ua(1);var l=t.compression?8:0,o=0;var c=false;if(c)o|=8;var f=0,u=0;var h=0,p=0;var d=e.FullPaths[0],m=d,v=e.FileIndex[0];var g=[];var b=0;for(f=1;f<e.FullPaths.length;++f){m=e.FullPaths[f].slice(d.length);v=e.FileIndex[f];if(!v.size||!v.content||m=="Sh33tJ5")continue;var w=h;var y=ua(m.length);for(u=0;u<m.length;++u)y._W(1,m.charCodeAt(u)&127);y=y.slice(0,y.l);g[p]=Ge.buf(v.content,0);var k=v.content;if(l==8)k=J(k);s=ua(30);s._W(4,67324752);s._W(2,20);s._W(2,o);s._W(2,l);if(v.mt)n(s,v.mt);else s._W(4,0);s._W(-4,o&8?0:g[p]);s._W(4,o&8?0:k.length);s._W(4,o&8?0:v.content.length);s._W(2,y.length);s._W(2,0);h+=s.length;a.push(s);h+=y.length;a.push(y);h+=k.length;a.push(k);if(o&8){s=ua(12);s._W(-4,g[p]);s._W(4,k.length);s._W(4,v.content.length);h+=s.l;a.push(s)}s=ua(46);s._W(4,33639248);s._W(2,0);s._W(2,20);s._W(2,o);s._W(2,l);s._W(4,0);s._W(-4,g[p]);s._W(4,k.length);s._W(4,v.content.length);s._W(2,y.length);s._W(2,0);s._W(2,0);s._W(2,0);s._W(2,0);s._W(4,0);s._W(4,w);b+=s.l;i.push(s);b+=y.length;i.push(y);++p}s=ua(22);s._W(4,101010256);s._W(2,0);s._W(2,0);s._W(2,p);s._W(2,p);s._W(4,b);s._W(4,h);s._W(2,0);return F([F(a),F(i),s])}var Pe={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Ie(e,r){if(e.ctype)return e.ctype;var t=e.name||"",a=t.match(/\.([^\.]+)$/);if(a&&Pe[a[1]])return Pe[a[1]];if(r){a=(t=r).match(/[\.\\]([^\.\\])+$/);if(a&&Pe[a[1]])return Pe[a[1]]}return"application/octet-stream"}function Re(e){var r=b(e);var t=[];for(var a=0;a<r.length;a+=76)t.push(r.slice(a,a+76));return t.join("\r\n")+"\r\n"}function Le(e){var r=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var r=e.charCodeAt(0).toString(16).toUpperCase();return"="+(r.length==1?"0"+r:r)});r=r.replace(/ $/gm,"=20").replace(/\t$/gm,"=09");if(r.charAt(0)=="\n")r="=0D"+r.slice(1);r=r.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A");var t=[],a=r.split("\r\n");for(var n=0;n<a.length;++n){var i=a[n];if(i.length==0){t.push("");continue}for(var s=0;s<i.length;){var l=76;var o=i.slice(s,s+l);if(o.charAt(l-1)=="=")l--;else if(o.charAt(l-2)=="=")l-=2;else if(o.charAt(l-3)=="=")l-=3;o=i.slice(s,s+l);s+=l;if(s<i.length)o+="=";t.push(o)}}return t.join("\r\n")}function Be(e){var r=[];for(var t=0;t<e.length;++t){var a=e[t];while(t<=e.length&&a.charAt(a.length-1)=="=")a=a.slice(0,a.length-1)+e[++t];r.push(a)}for(var n=0;n<r.length;++n)r[n]=r[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return C(r.join("\r\n"))}function $e(e,r,t){var a="",n="",i="",s;var l=0;for(;l<10;++l){var o=r[l];if(!o||o.match(/^\s*$/))break;var c=o.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":a=c[2].trim();break;case"content-type":i=c[2].trim();break;case"content-transfer-encoding":n=c[2].trim();break;}}++l;switch(n.toLowerCase()){case"base64":s=C(w(r.slice(l).join("")));break;case"quoted-printable":s=Be(r.slice(l));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+n);}var f=je(e,a.slice(t.length),s,{unsafe:true});if(i)f.ctype=i}function ze(e,r){if(H(e.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var t=r&&r.root||"";var a=(y&&Buffer.isBuffer(e)?e.toString("binary"):H(e)).split("\r\n");var n=0,i="";for(n=0;n<a.length;++n){i=a[n];if(!/^Content-Location:/i.test(i))continue;i=i.slice(i.indexOf("file"));if(!t)t=i.slice(0,i.lastIndexOf("/")+1);if(i.slice(0,t.length)==t)continue;while(t.length>0){t=t.slice(0,t.length-1);t=t.slice(0,t.lastIndexOf("/")+1);if(i.slice(0,t.length)==t)break}}var s=(a[1]||"").match(/boundary="(.*?)"/);if(!s)throw new Error("MAD cannot find boundary");var l="--"+(s[1]||"");var o=[],c=[];var f={FileIndex:o,FullPaths:c};D(f);var u,h=0;for(n=0;n<a.length;++n){var p=a[n];if(p!==l&&p!==l+"--")continue;if(h++)$e(f,a.slice(u,n),t);u=n}return f}function Ue(e,r){var t=r||{};var a=t.boundary||"SheetJS";a="------="+a;var n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+a.slice(2)+'"',"","",""];var i=e.FullPaths[0],s=i,l=e.FileIndex[0];for(var o=1;o<e.FullPaths.length;++o){s=e.FullPaths[o].slice(i.length);l=e.FileIndex[o];if(!l.size||!l.content||s=="Sh33tJ5")continue;s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});var c=l.content;var f=y&&Buffer.isBuffer(c)?c.toString("binary"):H(c);var u=0,h=Math.min(1024,f.length),p=0;for(var d=0;d<=h;++d)if((p=f.charCodeAt(d))>=32&&p<128)++u;var m=u>=h*4/5;n.push(a);n.push("Content-Location: "+(t.root||"file:///C:/SheetJS/")+s);n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64"));n.push("Content-Type: "+Ie(l,s));n.push("");n.push(m?Le(f):Re(f))}n.push(a+"--\r\n");return n.join("\r\n")}function We(e){var r={};D(r,e);return r}function je(e,r,t,n){var i=n&&n.unsafe;if(!i)D(e);var s=!i&&Ye.find(e,r);if(!s){var l=e.FullPaths[0];if(r.slice(0,l.length)==l)l=r;else{if(l.slice(-1)!="/")l+="/";l=(l+r).replace("//","/")}s={name:a(r),type:2};e.FileIndex.push(s);e.FullPaths.push(l);if(!i)Ye.utils.cfb_gc(e)}s.content=t;s.size=t?t.length:0;if(n){if(n.CLSID)s.clsid=n.CLSID;if(n.mt)s.mt=n.mt;if(n.ct)s.ct=n.ct}return s}function He(e,r){D(e);var t=Ye.find(e,r);if(t)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t){e.FileIndex.splice(a,1);e.FullPaths.splice(a,1);return true}return false}function Ve(e,r,t){D(e);var n=Ye.find(e,r);if(n)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n){e.FileIndex[i].name=a(t);e.FullPaths[i]=t;return true}return false}function Xe(e){P(e,true)}e.find=R;e.read=E;e.parse=c;e.write=V;e.writeFile=j;e.utils={cfb_new:We,cfb_add:je,cfb_del:He,cfb_mov:Ve,cfb_gc:Xe,ReadShift:aa,CheckField:oa,prep_blob:ca,bconcat:F,use_zlib:G,_deflateRaw:ke,_inflateRaw:Fe,consts:W};return e}();var Je;if(typeof require!=="undefined")try{Je=undefined}catch(Ke){}function qe(e){if(typeof e==="string")return _(e);if(Array.isArray(e))return T(e);return e}function Ze(e,r,t){if(typeof Je!=="undefined"&&Je.writeFileSync)return t?Je.writeFileSync(e,r,t):Je.writeFileSync(e,r);if(typeof Deno!=="undefined"){if(t&&typeof r=="string")switch(t){case"utf8":r=new TextEncoder(t).encode(r);break;case"binary":r=_(r);break;default:throw new Error("Unsupported encoding "+t);}return Deno.writeFileSync(e,r)}var a=t=="utf8"?ct(r):r;if(typeof IE_SaveFile!=="undefined")return IE_SaveFile(a,e);if(typeof Blob!=="undefined"){var n=new Blob([qe(a)],{type:"application/octet-stream"});if(typeof navigator!=="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs!=="undefined")return saveAs(n,e);if(typeof URL!=="undefined"&&typeof document!=="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome==="object"&&typeof(chrome.downloads||{}).download=="function"){if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return chrome.downloads.download({url:i,filename:e,saveAs:true})}var s=document.createElement("a");if(s.download!=null){s.download=e;s.href=i;document.body.appendChild(s);s.click();document.body.removeChild(s);if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return i}}}if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var l=File(e);l.open("w");l.encoding="binary";if(Array.isArray(r))r=A(r);l.write(r);l.close();return r}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function Qe(e){if(typeof Je!=="undefined")return Je.readFileSync(e);if(typeof Deno!=="undefined")return Deno.readFileSync(e);if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var r=File(e);r.open("r");r.encoding="binary";var t=r.read();r.close();return t}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function er(e){var r=Object.keys(e),t=[];for(var a=0;a<r.length;++a)if(Object.prototype.hasOwnProperty.call(e,r[a]))t.push(r[a]);return t}function rr(e,r){var t=[],a=er(e);for(var n=0;n!==a.length;++n)if(t[e[a[n]][r]]==null)t[e[a[n]][r]]=a[n];return t}function tr(e){var r=[],t=er(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}function ar(e){var r=[],t=er(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=parseInt(t[a],10);return r}function nr(e){var r=[],t=er(e);for(var a=0;a!==t.length;++a){if(r[e[t[a]]]==null)r[e[t[a]]]=[];r[e[t[a]]].push(t[a])}return r}var ir=new Date(1899,11,30,0,0,0);function sr(e,r){var t=e.getTime();if(r)t-=1462*24*60*60*1e3;var a=ir.getTime()+(e.getTimezoneOffset()-ir.getTimezoneOffset())*6e4;return(t-a)/(24*60*60*1e3)}var lr=new Date;var or=ir.getTime()+(lr.getTimezoneOffset()-ir.getTimezoneOffset())*6e4;var cr=lr.getTimezoneOffset();function fr(e){var r=new Date;r.setTime(e*24*60*60*1e3+or);if(r.getTimezoneOffset()!==cr){r.setTime(r.getTime()+(r.getTimezoneOffset()-cr)*6e4)}return r}function ur(e){var r=0,t=0,a=false;var n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i){if(!n[i])continue;t=1;if(i>3)a=true;switch(n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":t*=24;case"H":t*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");else t*=60;case"S":break;}r+=t*parseInt(n[i],10)}return r}var hr=new Date("2017-02-19T19:06:09.000Z");var pr=isNaN(hr.getFullYear())?new Date("2/19/17"):hr;var dr=pr.getFullYear()==2017;function mr(e,r){var t=new Date(e);if(dr){if(r>0)t.setTime(t.getTime()+t.getTimezoneOffset()*60*1e3);else if(r<0)t.setTime(t.getTime()-t.getTimezoneOffset()*60*1e3);return t}if(e instanceof Date)return e;if(pr.getFullYear()==1917&&!isNaN(t.getFullYear())){var a=t.getFullYear();if(e.indexOf(""+a)>-1)return t;t.setFullYear(t.getFullYear()+100);return t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"];var i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);if(e.indexOf("Z")>-1)i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3);return i}function vr(e,r){if(y&&Buffer.isBuffer(e)){if(r){if(e[0]==255&&e[1]==254)return ct(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return ct(u(e.slice(2).toString("binary")));
}return e.toString("binary")}if(typeof TextDecoder!=="undefined")try{if(r){if(e[0]==255&&e[1]==254)return ct(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return ct(new TextDecoder("utf-16be").decode(e.slice(2)))}var t={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};if(Array.isArray(e))e=new Uint8Array(e);return new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return t[e]||e})}catch(a){}var n=[];for(var i=0;i!=e.length;++i)n.push(String.fromCharCode(e[i]));return n.join("")}function gr(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))r[t]=gr(e[t]);return r}function br(e,r){var t="";while(t.length<r)t+=e;return t}function wr(e){var r=Number(e);if(!isNaN(r))return isFinite(r)?r:NaN;if(!/\d/.test(e))return r;var t=1;var a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){t*=100;return""});if(!isNaN(r=Number(a)))return r/t;a=a.replace(/[(](.*)[)]/,function(e,r){t=-t;return r});if(!isNaN(r=Number(a)))return r/t;return r}var yr=["january","february","march","april","may","june","july","august","september","october","november","december"];function kr(e){var r=new Date(e),t=new Date(NaN);var a=r.getYear(),n=r.getMonth(),i=r.getDate();if(isNaN(i))return t;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"");if(s.length>3&&yr.indexOf(s)==-1)return t}else if(s.match(/[a-z]/))return t;if(a<0||a>8099)return t;if((n>0||i>1)&&a!=101)return r;if(e.match(/[^-0-9:,\/\\]/))return t;return r}var xr=function(){var e="abacaba".split(/(:?b)/i).length==5;return function r(t,a,n){if(e||typeof a=="string")return t.split(a);var i=t.split(a),s=[i[0]];for(var l=1;l<i.length;++l){s.push(n);s.push(i[l])}return s}}();function Sr(e){if(!e)return null;if(e.content&&e.type)return vr(e.content,true);if(e.data)return h(e.data);if(e.asNodeBuffer&&y)return h(e.asNodeBuffer().toString("binary"));if(e.asBinary)return h(e.asBinary());if(e._data&&e._data.getContent)return h(vr(Array.prototype.slice.call(e._data.getContent(),0)));return null}function Cr(e){if(!e)return null;if(e.data)return c(e.data);if(e.asNodeBuffer&&y)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();if(typeof r=="string")return c(r);return Array.prototype.slice.call(r)}if(e.content&&e.type)return e.content;return null}function _r(e){return e&&e.name.slice(-4)===".bin"?Cr(e):Sr(e)}function Ar(e,r){var t=e.FullPaths||er(e.files);var a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/");for(var i=0;i<t.length;++i){var s=t[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[t[i]]:e.FileIndex[i]}return null}function Tr(e,r){var t=Ar(e,r);if(t==null)throw new Error("Cannot find file "+r+" in zip");return t}function Er(e,r,t){if(!t)return _r(Tr(e,r));if(!r)return null;try{return Er(e,r)}catch(a){return null}}function Fr(e,r,t){if(!t)return Sr(Tr(e,r));if(!r)return null;try{return Fr(e,r)}catch(a){return null}}function Dr(e,r,t){if(!t)return Cr(Tr(e,r));if(!r)return null;try{return Dr(e,r)}catch(a){return null}}function Or(e){var r=e.FullPaths||er(e.files),t=[];for(var a=0;a<r.length;++a)if(r[a].slice(-1)!="/")t.push(r[a].replace(/^Root Entry[\/]/,""));return t.sort()}function Mr(e,r,t){if(e.FullPaths){if(typeof t=="string"){var a;if(y)a=k(t);else a=D(t);return Ye.utils.cfb_add(e,r,a)}Ye.utils.cfb_add(e,r,t)}else e.file(r,t)}function Nr(){return Ye.utils.cfb_new()}function Pr(e,r){switch(r.type){case"base64":return Ye.read(e,{type:"base64"});case"binary":return Ye.read(e,{type:"binary"});case"buffer":;case"array":return Ye.read(e,{type:"buffer"});}throw new Error("Unrecognized type "+r.type)}function Ir(e,r){if(e.charAt(0)=="/")return e.slice(1);var t=r.split("/");if(r.slice(-1)!="/")t.pop();var a=e.split("/");while(a.length!==0){var n=a.shift();if(n==="..")t.pop();else if(n!==".")t.push(n)}return t.join("/")}var Rr='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var Lr=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g;var Br=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,$r=/<[^>]*>/g;var zr=Rr.match(Br)?Br:$r;var Ur=/<\w*:/,Wr=/<(\/?)\w+:/;function jr(e,r,t){var a={};var n=0,i=0;for(;n!==e.length;++n)if((i=e.charCodeAt(n))===32||i===10||i===13)break;if(!r)a[0]=e.slice(0,n);if(n===e.length)return a;var s=e.match(Lr),l=0,o="",c=0,f="",u="",h=1;if(s)for(c=0;c!=s.length;++c){u=s[c];for(i=0;i!=u.length;++i)if(u.charCodeAt(i)===61)break;f=u.slice(0,i).trim();while(u.charCodeAt(i+1)==32)++i;h=(n=u.charCodeAt(i+1))==34||n==39?1:0;o=u.slice(i+1+h,u.length-h);for(l=0;l!=f.length;++l)if(f.charCodeAt(l)===58)break;if(l===f.length){if(f.indexOf("_")>0)f=f.slice(0,f.indexOf("_"));a[f]=o;if(!t)a[f.toLowerCase()]=o}else{var p=(l===5&&f.slice(0,5)==="xmlns"?"xmlns":"")+f.slice(l+1);if(a[p]&&f.slice(l-3,l)=="ext")continue;a[p]=o;if(!t)a[p.toLowerCase()]=o}}return a}function Hr(e){return e.replace(Wr,"<$1")}var Vr={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"};var Xr=tr(Vr);var Gr=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,r=/_x([\da-fA-F]{4})_/gi;return function t(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(e,r){return Vr[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e}).replace(r,function(e,r){return String.fromCharCode(parseInt(r,16))});var s=n.indexOf("]]>");return t(n.slice(0,i))+n.slice(i+9,s)+t(n.slice(s+3))}}();var Yr=/[&<>'"]/g,Jr=/[\u0000-\u0008\u000b-\u001f]/g;function Kr(e){var r=e+"";return r.replace(Yr,function(e){return Xr[e]}).replace(Jr,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function qr(e){return Kr(e).replace(/ /g,"_x0020_")}var Zr=/[\u0000-\u001f]/g;function Qr(e){var r=e+"";return r.replace(Yr,function(e){return Xr[e]}).replace(/\n/g,"<br/>").replace(Zr,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function et(e){var r=e+"";return r.replace(Yr,function(e){return Xr[e]}).replace(Zr,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var rt=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function t(a){return a.replace(e,r)}}();function tt(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function at(e){switch(e){case 1:;case true:;case"1":;case"true":;case"TRUE":return true;default:return false;}}function nt(e){var r="",t=0,a=0,n=0,i=0,s=0,l=0;while(t<e.length){a=e.charCodeAt(t++);if(a<128){r+=String.fromCharCode(a);continue}n=e.charCodeAt(t++);if(a>191&&a<224){s=(a&31)<<6;s|=n&63;r+=String.fromCharCode(s);continue}i=e.charCodeAt(t++);if(a<240){r+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(t++);l=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536;r+=String.fromCharCode(55296+(l>>>10&1023));r+=String.fromCharCode(56320+(l&1023))}return r}function it(e){var r=x(2*e.length),t,a,n=1,i=0,s=0,l;for(a=0;a<e.length;a+=n){n=1;if((l=e.charCodeAt(a))<128)t=l;else if(l<224){t=(l&31)*64+(e.charCodeAt(a+1)&63);n=2}else if(l<240){t=(l&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63);n=3}else{n=4;t=(l&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63);t-=65536;s=55296+(t>>>10&1023);t=56320+(t&1023)}if(s!==0){r[i++]=s&255;r[i++]=s>>>8;s=0}r[i++]=t%256;r[i++]=t>>>8}return r.slice(0,i).toString("ucs2")}function st(e){return k(e,"binary").toString("utf8")}var lt="foo bar bazâð£";var ot=y&&(st(lt)==nt(lt)&&st||it(lt)==nt(lt)&&it)||nt;var ct=y?function(e){return k(e,"utf8").toString("binary")}:function(e){var r=[],t=0,a=0,n=0;while(t<e.length){a=e.charCodeAt(t++);switch(true){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6)));r.push(String.fromCharCode(128+(a&63)));break;case a>=55296&&a<57344:a-=55296;n=e.charCodeAt(t++)-56320+(a<<10);r.push(String.fromCharCode(240+(n>>18&7)));r.push(String.fromCharCode(144+(n>>12&63)));r.push(String.fromCharCode(128+(n>>6&63)));r.push(String.fromCharCode(128+(n&63)));break;default:r.push(String.fromCharCode(224+(a>>12)));r.push(String.fromCharCode(128+(a>>6&63)));r.push(String.fromCharCode(128+(a&63)));}}return r.join("")};var ft=function(){var e={};return function r(t,a){var n=t+"|"+(a||"");if(e[n])return e[n];return e[n]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",a||"")}}();var ut=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]});return function r(t){var a=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,"");for(var n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}();var ht=function(){var e={};return function r(t){if(e[t]!==undefined)return e[t];return e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}();var pt=/<\/?(?:vt:)?variant>/g,dt=/<(?:vt:)([^>]*)>([\s\S]*)</;function mt(e,r){var t=jr(e);var a=e.match(ht(t.baseType))||[];var n=[];if(a.length!=t.size){if(r.WTF)throw new Error("unexpected vector length "+a.length+" != "+t.size);return n}a.forEach(function(e){var r=e.replace(pt,"").match(dt);if(r)n.push({v:ot(r[2]),t:r[1]})});return n}var vt=/(^\s|\s$|\n)/;function gt(e,r){return"<"+e+(r.match(vt)?' xml:space="preserve"':"")+">"+r+"</"+e+">"}function bt(e){return er(e).map(function(r){return" "+r+'="'+e[r]+'"'}).join("")}function wt(e,r,t){return"<"+e+(t!=null?bt(t):"")+(r!=null?(r.match(vt)?' xml:space="preserve"':"")+">"+r+"</"+e:"/")+">"}function yt(e,r){try{return e.toISOString().replace(/\.\d*/,"")}catch(t){if(r)throw t}return""}function kt(e,r){switch(typeof e){case"string":var t=wt("vt:lpwstr",Kr(e));if(r)t=t.replace(/&quot;/g,"_x0022_");return t;case"number":return wt((e|0)==e?"vt:i4":"vt:r8",Kr(String(e)));case"boolean":return wt("vt:bool",e?"true":"false");}if(e instanceof Date)return wt("vt:filetime",yt(e));throw new Error("Unable to serialize "+e)}function xt(e){if(y&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e==="string")return e;if(typeof Uint8Array!=="undefined"&&e instanceof Uint8Array)return ot(A(E(e)));throw new Error("Bad input format: expected Buffer or string")}var St=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm;var Ct={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"};var _t=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];var At={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Tt(e,r){var t=1-2*(e[r+7]>>>7);var a=((e[r+7]&127)<<4)+(e[r+6]>>>4&15);var n=e[r+6]&15;for(var i=5;i>=0;--i)n=n*256+e[r+i];if(a==2047)return n==0?t*Infinity:NaN;if(a==0)a=-1022;else{a-=1023;n+=Math.pow(2,52)}return t*Math.pow(2,a-52)*n}function Et(e,r,t){var a=(r<0||1/r==-Infinity?1:0)<<7,n=0,i=0;var s=a?-r:r;if(!isFinite(s)){n=2047;i=isNaN(r)?26985:0}else if(s==0)n=i=0;else{n=Math.floor(Math.log(s)/Math.LN2);i=s*Math.pow(2,52-n);if(n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))){n=-1022}else{i-=Math.pow(2,52);n+=1023}}for(var l=0;l<=5;++l,i/=256)e[t+l]=i&255;e[t+6]=(n&15)<<4|i&15;e[t+7]=n>>4|a}var Ft=function(e){var r=[],t=10240;for(var a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=t)r.push.apply(r,e[0][a].slice(n,n+t));return r};var Dt=y?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:k(e)})):Ft(e)}:Ft;var Ot=function(e,r,t){var a=[];for(var n=r;n<t;n+=2)a.push(String.fromCharCode(Zt(e,n)));return a.join("").replace(O,"")};var Mt=y?function(e,r,t){if(!Buffer.isBuffer(e))return Ot(e,r,t);return e.toString("utf16le",r,t).replace(O,"")}:Ot;var Nt=function(e,r,t){var a=[];for(var n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")};var Pt=y?function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):Nt(e,r,t)}:Nt;var It=function(e,r,t){var a=[];for(var n=r;n<t;n++)a.push(String.fromCharCode(qt(e,n)));return a.join("")};var Rt=y?function Tc(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):It(e,r,t)}:It;var Lt=function(e,r){var t=ea(e,r);return t>0?Rt(e,r+4,r+4+t-1):""};var Bt=Lt;var $t=function(e,r){var t=ea(e,r);return t>0?Rt(e,r+4,r+4+t-1):""};var zt=$t;var Ut=function(e,r){var t=2*ea(e,r);return t>0?Rt(e,r+4,r+4+t-1):""};var Wt=Ut;var jt=function Ec(e,r){var t=ea(e,r);return t>0?Mt(e,r+4,r+4+t):""};var Ht=jt;var Vt=function(e,r){var t=ea(e,r);return t>0?Rt(e,r+4,r+4+t):""};var Xt=Vt;var Gt=function(e,r){return Tt(e,r)};var Yt=Gt;var Jt=function Fc(e){return Array.isArray(e)||typeof Uint8Array!=="undefined"&&e instanceof Uint8Array};if(y){Bt=function Dc(e,r){if(!Buffer.isBuffer(e))return Lt(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};zt=function Oc(e,r){if(!Buffer.isBuffer(e))return $t(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};Wt=function Mc(e,r){if(!Buffer.isBuffer(e))return Ut(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)};Ht=function Nc(e,r){if(!Buffer.isBuffer(e))return jt(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)};Xt=function Pc(e,r){if(!Buffer.isBuffer(e))return Vt(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)};Yt=function Ic(e,r){if(Buffer.isBuffer(e))return e.readDoubleLE(r);return Gt(e,r)};Jt=function Rc(e){return Buffer.isBuffer(e)||Array.isArray(e)||typeof Uint8Array!=="undefined"&&e instanceof Uint8Array}}function Kt(){Mt=function(e,r,t){return $cptable.utils.decode(1200,e.slice(r,t)).replace(O,"")};Rt=function(e,r,t){return $cptable.utils.decode(65001,e.slice(r,t))};Bt=function(e,r){var a=ea(e,r);return a>0?$cptable.utils.decode(t,e.slice(r+4,r+4+a-1)):""};zt=function(e,t){var a=ea(e,t);return a>0?$cptable.utils.decode(r,e.slice(t+4,t+4+a-1)):""};Wt=function(e,r){var t=2*ea(e,r);return t>0?$cptable.utils.decode(1200,e.slice(r+4,r+4+t-1)):""};Ht=function(e,r){var t=ea(e,r);return t>0?$cptable.utils.decode(1200,e.slice(r+4,r+4+t)):""};Xt=function(e,r){var t=ea(e,r);return t>0?$cptable.utils.decode(65001,e.slice(r+4,r+4+t)):""}}if(typeof $cptable!=="undefined")Kt();var qt=function(e,r){return e[r]};var Zt=function(e,r){return e[r+1]*(1<<8)+e[r]};var Qt=function(e,r){var t=e[r+1]*(1<<8)+e[r];return t<32768?t:(65535-t+1)*-1};var ea=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]};var ra=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]};var ta=function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]};function aa(e,t){var a="",n,i,s=[],l,o,c,f;switch(t){case"dbcs":f=this.l;if(y&&Buffer.isBuffer(this))a=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c){a+=String.fromCharCode(Zt(this,f));f+=2}e*=2;break;case"utf8":a=Rt(this,this.l,this.l+e);break;case"utf16le":e*=2;a=Mt(this,this.l,this.l+e);break;case"wstr":if(typeof $cptable!=="undefined")a=$cptable.utils.decode(r,this.slice(this.l,this.l+2*e));else return aa.call(this,e,"dbcs");e=2*e;break;case"lpstr-ansi":a=Bt(this,this.l);e=4+ea(this,this.l);break;case"lpstr-cp":a=zt(this,this.l);e=4+ea(this,this.l);break;case"lpwstr":a=Wt(this,this.l);e=4+2*ea(this,this.l);break;case"lpp4":e=4+ea(this,this.l);a=Ht(this,this.l);if(e&2)e+=2;break;case"8lpp4":e=4+ea(this,this.l);a=Xt(this,this.l);if(e&3)e+=4-(e&3);break;case"cstr":e=0;a="";while((l=qt(this,this.l+e++))!==0)s.push(p(l));a=s.join("");break;case"_wstr":e=0;a="";while((l=Zt(this,this.l+e))!==0){s.push(p(l));e+=2}e+=2;a=s.join("");break;case"dbcs-cont":a="";f=this.l;for(c=0;c<e;++c){if(this.lens&&this.lens.indexOf(f)!==-1){l=qt(this,f);this.l=f+1;o=aa.call(this,e-c,l?"dbcs-cont":"sbcs-cont");return s.join("")+o}s.push(p(Zt(this,f)));f+=2}a=s.join("");e*=2;break;case"cpstr":if(typeof $cptable!=="undefined"){a=$cptable.utils.decode(r,this.slice(this.l,this.l+e));break};case"sbcs-cont":a="";f=this.l;for(c=0;c!=e;++c){if(this.lens&&this.lens.indexOf(f)!==-1){l=qt(this,f);this.l=f+1;o=aa.call(this,e-c,l?"dbcs-cont":"sbcs-cont");return s.join("")+o}s.push(p(qt(this,f)));f+=1}a=s.join("");break;default:switch(e){case 1:n=qt(this,this.l);this.l++;return n;case 2:n=(t==="i"?Qt:Zt)(this,this.l);this.l+=2;return n;case 4:;case-4:if(t==="i"||(this[this.l+3]&128)===0){n=(e>0?ra:ta)(this,this.l);this.l+=4;return n}else{i=ea(this,this.l);this.l+=4}return i;case 8:;case-8:if(t==="f"){if(e==8)i=Yt(this,this.l);else i=Yt([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0);this.l+=8;return i}else e=8;case 16:a=Pt(this,this.l,e);break;};}this.l+=e;return a}var na=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255;e[t+2]=r>>>16&255;e[t+3]=r>>>24&255};var ia=function(e,r,t){e[t]=r&255;e[t+1]=r>>8&255;e[t+2]=r>>16&255;e[t+3]=r>>24&255};var sa=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255};function la(e,r,a){var n=0,i=0;if(a==="dbcs"){for(i=0;i!=r.length;++i)sa(this,r.charCodeAt(i),this.l+2*i);n=2*r.length}else if(a==="sbcs"){if(typeof $cptable!=="undefined"&&t==874){for(i=0;i!=r.length;++i){var s=$cptable.utils.encode(t,r.charAt(i));this[this.l+i]=s[0]}}else{r=r.replace(/[^\x00-\x7F]/g,"_");for(i=0;i!=r.length;++i)this[this.l+i]=r.charCodeAt(i)&255}n=r.length}else if(a==="hex"){for(;i<e;++i){this[this.l++]=parseInt(r.slice(2*i,2*i+2),16)||0}return this}else if(a==="utf16le"){var l=Math.min(this.l+e,this.length);for(i=0;i<Math.min(r.length,e);++i){var o=r.charCodeAt(i);this[this.l++]=o&255;this[this.l++]=o>>8}while(this.l<l)this[this.l++]=0;return this}else switch(e){case 1:n=1;this[this.l]=r&255;break;case 2:n=2;this[this.l]=r&255;r>>>=8;this[this.l+1]=r&255;break;case 3:n=3;this[this.l]=r&255;r>>>=8;this[this.l+1]=r&255;r>>>=8;this[this.l+2]=r&255;break;case 4:n=4;na(this,r,this.l);break;case 8:n=8;if(a==="f"){Et(this,r,this.l);break};case 16:break;case-4:n=4;ia(this,r,this.l);break;}this.l+=n;return this}function oa(e,r){var t=Pt(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function ca(e,r){e.l=r;e._R=aa;e.chk=oa;e._W=la}function fa(e,r){e.l+=r}function ua(e){var r=x(e);ca(r,0);return r}function ha(e,r,t){if(!e)return;var a,n,i;ca(e,e.l||0);var s=e.length,l=0,o=0;while(e.l<s){l=e._R(1);if(l&128)l=(l&127)+((e._R(1)&127)<<7);var c=XLSBRecordEnum[l]||XLSBRecordEnum[65535];a=e._R(1);i=a&127;for(n=1;n<4&&a&128;++n)i+=((a=e._R(1))&127)<<7*n;o=e.l+i;var f=c.f&&c.f(e,i,t);e.l=o;if(r(f,c,l))return}}function pa(){var e=[],r=y?256:2048;var t=function o(e){var r=ua(e);ca(r,0);return r};var a=t(r);var n=function c(){if(!a)return;if(a.length>a.l){a=a.slice(0,a.l);a.l=a.length}if(a.length>0)e.push(a);a=null};var i=function f(e){if(a&&e<a.length-a.l)return a;n();return a=t(Math.max(e+1,r))};var s=function u(){n();return F(e)};var l=function h(e){n();a=e;if(a.l==null)a.l=a.length;i(r)};return{next:i,push:l,end:s,_bufs:e}}function da(e,r,t,a){var n=+r,i;if(isNaN(n))return;if(!a)a=XLSBRecordEnum[n].p||(t||[]).length||0;i=1+(n>=128?1:0)+1;if(a>=128)++i;if(a>=16384)++i;if(a>=2097152)++i;var s=e.next(i);if(n<=127)s._W(1,n);else{s._W(1,(n&127)+128);s._W(1,n>>7)}for(var l=0;l!=4;++l){if(a>=128){s._W(1,(a&127)+128);a>>=7}else{s._W(1,a);break}}if(a>0&&Jt(t))e.push(t)}function ma(e,r,t){var a=gr(e);if(r.s){if(a.cRel)a.c+=r.s.c;if(a.rRel)a.r+=r.s.r}else{if(a.cRel)a.c+=r.c;if(a.rRel)a.r+=r.r}if(!t||t.biff<12){while(a.c>=256)a.c-=256;while(a.r>=65536)a.r-=65536}return a}function va(e,r,t){var a=gr(e);a.s=ma(a.s,r.s,t);a.e=ma(a.e,r.s,t);return a}function ga(e,r){if(e.cRel&&e.c<0){e=gr(e);while(e.c<0)e.c+=r>8?16384:256}if(e.rRel&&e.r<0){e=gr(e);while(e.r<0)e.r+=r>8?1048576:r>5?65536:16384}var t=Fa(e);if(!e.cRel&&e.cRel!=null)t=_a(t);if(!e.rRel&&e.rRel!=null)t=ka(t);return t}function ba(e,r){if(e.s.r==0&&!e.s.rRel){if(e.e.r==(r.biff>=12?1048575:r.biff>=8?65536:16384)&&!e.e.rRel){return(e.s.cRel?"":"$")+Ca(e.s.c)+":"+(e.e.cRel?"":"$")+Ca(e.e.c)}}if(e.s.c==0&&!e.s.cRel){if(e.e.c==(r.biff>=12?16383:255)&&!e.e.cRel){return(e.s.rRel?"":"$")+ya(e.s.r)+":"+(e.e.rRel?"":"$")+ya(e.e.r)}}return ga(e.s,r.biff)+":"+ga(e.e,r.biff)}function wa(e){return parseInt(xa(e),10)-1}function ya(e){return""+(e+1)}function ka(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function xa(e){return e.replace(/\$(\d+)$/,"$1")}function Sa(e){var r=Aa(e),t=0,a=0;for(;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function Ca(e){if(e<0)throw new Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function _a(e){return e.replace(/^([A-Z])/,"$$$1")}function Aa(e){return e.replace(/^\$([A-Z])/,"$1")}function Ta(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Ea(e){var r=0,t=0;for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);if(n>=48&&n<=57)r=10*r+(n-48);else if(n>=65&&n<=90)t=26*t+(n-64)}return{c:t-1,r:r-1}}function Fa(e){var r=e.c+1;var t="";for(;r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function Da(e){var r=e.indexOf(":");if(r==-1)return{s:Ea(e),e:Ea(e)};return{s:Ea(e.slice(0,r)),e:Ea(e.slice(r+1))}}function Oa(e,r){if(typeof r==="undefined"||typeof r==="number"){return Oa(e.s,e.e)}if(typeof e!=="string")e=Fa(e);if(typeof r!=="string")r=Fa(r);return e==r?e:e+":"+r}function Ma(e){var r={s:{c:0,r:0},e:{c:0,r:0}};var t=0,a=0,n=0;var i=e.length;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.s.c=--t;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.s.r=--t;if(a===i||n!=10){r.e.c=r.s.c;r.e.r=r.s.r;return r}++a;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.e.c=--t;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.e.r=--t;return r}function Na(e,r){var t=e.t=="d"&&r instanceof Date;if(e.z!=null)try{return e.w=Be(e.z,t?sr(r):r)}catch(a){}try{return e.w=Be((e.XF||{}).numFmtId||(t?14:0),t?sr(r):r)}catch(a){return""+r}}function Pa(e,r,t){if(e==null||e.t==null||e.t=="z")return"";if(e.w!==undefined)return e.w;if(e.t=="d"&&!e.z&&t&&t.dateNF)e.z=t.dateNF;if(e.t=="e")return ln[e.v]||e.v;if(r==undefined)return Na(e,e.v);return Na(e,r)}function Ia(e,r){var t=r&&r.sheet?r.sheet:"Sheet1";var a={};a[t]=e;return{SheetNames:[t],Sheets:a}}function Ra(e,r,t){var a=t||{};var n=e?Array.isArray(e):a.dense;if(m!=null&&n==null)n=m;var i=e||(n?[]:{});var s=0,l=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var o=typeof a.origin=="string"?Ea(a.origin):a.origin;s=o.r;l=o.c}if(!i["!ref"])i["!ref"]="A1:A1"}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var f=Ma(i["!ref"]);c.s.c=f.s.c;c.s.r=f.s.r;c.e.c=Math.max(c.e.c,f.e.c);c.e.r=Math.max(c.e.r,f.e.r);if(s==-1)c.e.r=s=f.e.r+1}for(var u=0;u!=r.length;++u){if(!r[u])continue;if(!Array.isArray(r[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=r[u].length;++h){if(typeof r[u][h]==="undefined")continue;var p={v:r[u][h]};var d=s+u,v=l+h;if(c.s.r>d)c.s.r=d;if(c.s.c>v)c.s.c=v;if(c.e.r<d)c.e.r=d;if(c.e.c<v)c.e.c=v;if(r[u][h]&&typeof r[u][h]==="object"&&!Array.isArray(r[u][h])&&!(r[u][h]instanceof Date))p=r[u][h];else{if(Array.isArray(p.v)){p.f=r[u][h][1];p.v=p.v[0]}if(p.v===null){if(p.f)p.t="n";else if(a.nullError){p.t="e";p.v=0}else if(!a.sheetStubs)continue;else p.t="z"}else if(typeof p.v==="number")p.t="n";else if(typeof p.v==="boolean")p.t="b";else if(p.v instanceof Date){p.z=a.dateNF||X[14];if(a.cellDates){p.t="d";p.w=Be(p.z,sr(p.v))}else{p.t="n";p.v=sr(p.v);p.w=Be(p.z,p.v)}}else p.t="s"}if(n){if(!i[d])i[d]=[];if(i[d][v]&&i[d][v].z)p.z=i[d][v].z;i[d][v]=p}else{var g=Fa({c:v,r:d});if(i[g]&&i[g].z)p.z=i[g].z;i[g]=p}}}if(c.s.c<1e7)i["!ref"]=Oa(c);return i}function La(e,r){return Ra(null,e,r)}var Ba=2;var $a=3;var za=11;var Ua=12;var Wa=19;var ja=30;var Ha=64;var Va=65;var Xa=71;var Ga=4096;var Ya=4108;var Ja=4126;var Ka=80;var qa=81;var Za=[Ka,qa];var Qa={1:{n:"CodePage",t:Ba},2:{n:"Category",t:Ka},3:{n:"PresentationFormat",t:Ka},4:{n:"ByteCount",t:$a},5:{n:"LineCount",t:$a},6:{n:"ParagraphCount",t:$a},7:{n:"SlideCount",t:$a},8:{n:"NoteCount",t:$a},9:{n:"HiddenCount",t:$a},10:{n:"MultimediaClipCount",t:$a},11:{n:"ScaleCrop",t:za},12:{n:"HeadingPairs",t:Ya},13:{n:"TitlesOfParts",t:Ja},14:{n:"Manager",t:Ka},15:{n:"Company",t:Ka},16:{n:"LinksUpToDate",t:za},17:{n:"CharacterCount",t:$a},19:{n:"SharedDoc",t:za},22:{n:"HyperlinksChanged",t:za},23:{n:"AppVersion",t:$a,p:"version"},24:{n:"DigSig",t:Va},26:{n:"ContentType",t:Ka},27:{n:"ContentStatus",t:Ka},28:{n:"Language",t:Ka},29:{n:"Version",t:Ka},255:{},2147483648:{n:"Locale",t:Wa},2147483651:{n:"Behavior",t:Wa},1919054434:{}};var en={1:{n:"CodePage",t:Ba},2:{n:"Title",t:Ka},3:{n:"Subject",t:Ka},4:{n:"Author",t:Ka},5:{n:"Keywords",t:Ka},6:{n:"Comments",t:Ka},7:{n:"Template",t:Ka},8:{n:"LastAuthor",t:Ka},9:{n:"RevNumber",t:Ka},10:{n:"EditTime",t:Ha},11:{n:"LastPrinted",t:Ha},12:{n:"CreatedDate",t:Ha},13:{n:"ModifiedDate",t:Ha},14:{n:"PageCount",t:$a},15:{n:"WordCount",t:$a},16:{n:"CharCount",t:$a},17:{n:"Thumbnail",t:Xa},18:{n:"Application",t:Ka},19:{n:"DocSecurity",t:$a},255:{},2147483648:{n:"Locale",t:Wa},2147483651:{n:"Behavior",t:Wa},1919054434:{}};var rn={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"};var tn=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function an(e){return e.map(function(e){return[e>>16&255,e>>8&255,e&255]})}var nn=an([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);var sn=gr(nn);var ln={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"};var on={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255};var cn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO",
"application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};var fn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function un(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function hn(e){var r=un();if(!e||!e.match)return r;var t={};(e.match(zr)||[]).forEach(function(e){var a=jr(e);switch(a[0].replace(Ur,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension]=a.ContentType;break;case"<Override":if(r[cn[a.ContentType]]!==undefined)r[cn[a.ContentType]].push(a.PartName);break;}});if(r.xmlns!==Ct.CT)throw new Error("Unknown Namespace: "+r.xmlns);r.calcchain=r.calcchains.length>0?r.calcchains[0]:"";r.sst=r.strs.length>0?r.strs[0]:"";r.style=r.styles.length>0?r.styles[0]:"";r.defaults=t;delete r.calcchains;return r}function pn(e,r){var t=nr(cn);var a=[],n;a[a.length]=Rr;a[a.length]=wt("Types",null,{xmlns:Ct.CT,"xmlns:xsd":Ct.xsd,"xmlns:xsi":Ct.xsi});a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return wt("Default",null,{Extension:e[0],ContentType:e[1]})}));var i=function(t){if(e[t]&&e[t].length>0){n=e[t][0];a[a.length]=wt("Override",null,{PartName:(n[0]=="/"?"":"/")+n,ContentType:fn[t][r.bookType]||fn[t]["xlsx"]})}};var s=function(t){(e[t]||[]).forEach(function(e){a[a.length]=wt("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:fn[t][r.bookType]||fn[t]["xlsx"]})})};var l=function(r){(e[r]||[]).forEach(function(e){a[a.length]=wt("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:t[r][0]})})};i("workbooks");s("sheets");s("charts");l("themes");["strs","styles"].forEach(i);["coreprops","extprops","custprops"].forEach(l);l("vba");l("comments");l("threadedcomments");l("drawings");s("metadata");l("people");if(a.length>2){a[a.length]="</Types>";a[1]=a[1].replace("/>",">")}return a.join("")}var dn={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function mn(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function vn(e,r){var t={"!id":{}};if(!e)return t;if(r.charAt(0)!=="/"){r="/"+r}var a={};(e.match(zr)||[]).forEach(function(e){var n=jr(e);if(n[0]==="<Relationship"){var i={};i.Type=n.Type;i.Target=n.Target;i.Id=n.Id;if(n.TargetMode)i.TargetMode=n.TargetMode;var s=n.TargetMode==="External"?n.Target:Ir(n.Target,r);t[s]=i;a[n.Id]=i}});t["!id"]=a;return t}function gn(e){var r=[Rr,wt("Relationships",null,{xmlns:Ct.RELS})];er(e["!id"]).forEach(function(t){r[r.length]=wt("Relationship",null,e["!id"][t])});if(r.length>2){r[r.length]="</Relationships>";r[1]=r[1].replace("/>",">")}return r.join("")}function bn(e,r,t,a,n,i){if(!n)n={};if(!e["!id"])e["!id"]={};if(!e["!idx"])e["!idx"]=1;if(r<0)for(r=e["!idx"];e["!id"]["rId"+r];++r){}e["!idx"]=r+1;n.Id="rId"+r;n.Type=a;n.Target=t;if(i)n.TargetMode=i;else if([dn.HLINK,dn.XPATH,dn.XMISS].indexOf(n.Type)>-1)n.TargetMode="External";if(e["!id"][n.Id])throw new Error("Cannot rewrite rId "+r);e["!id"][n.Id]=n;e[("/"+n.Target).replace("//","/")]=n;return r}var wn="application/vnd.oasis.opendocument.spreadsheet";function yn(e,r){var t=xt(e);var a;var n;while(a=St.exec(t))switch(a[3]){case"manifest":break;case"file-entry":n=jr(a[0],false);if(n.path=="/"&&n.type!==wn)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":;case"algorithm":;case"start-key-generation":;case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw a;}}function kn(e){var r=[Rr];r.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n');r.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var t=0;t<e.length;++t)r.push('  <manifest:file-entry manifest:full-path="'+e[t][0]+'" manifest:media-type="'+e[t][1]+'"/>\n');r.push("</manifest:manifest>");return r.join("")}function xn(e,r,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(t||"odf")+"#"+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Sn(e,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Cn(e){var r=[Rr];r.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var t=0;t!=e.length;++t){r.push(xn(e[t][0],e[t][1]));r.push(Sn("",e[t][0]))}r.push(xn("","Document","pkg"));r.push("</rdf:RDF>");return r.join("")}function _n(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>Sheet'+"JS "+e.version+"</meta:generator></office:meta></office:document-meta>"}var An=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];var Tn=function(){var e=new Array(An.length);for(var r=0;r<An.length;++r){var t=An[r];var a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function En(e){var r={};e=ot(e);for(var t=0;t<An.length;++t){var a=An[t],n=e.match(Tn[t]);if(n!=null&&n.length>0)r[a[1]]=Gr(n[1]);if(a[2]==="date"&&r[a[1]])r[a[1]]=mr(r[a[1]])}return r}function Fn(e,r,t,a,n){if(n[e]!=null||r==null||r==="")return;n[e]=r;r=Kr(r);a[a.length]=t?wt(e,r,t):gt(e,r)}function Dn(e,r){var t=r||{};var a=[Rr,wt("cp:coreProperties",null,{"xmlns:cp":Ct.CORE_PROPS,"xmlns:dc":Ct.dc,"xmlns:dcterms":Ct.dcterms,"xmlns:dcmitype":Ct.dcmitype,"xmlns:xsi":Ct.xsi})],n={};if(!e&&!t.Props)return a.join("");if(e){if(e.CreatedDate!=null)Fn("dcterms:created",typeof e.CreatedDate==="string"?e.CreatedDate:yt(e.CreatedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n);if(e.ModifiedDate!=null)Fn("dcterms:modified",typeof e.ModifiedDate==="string"?e.ModifiedDate:yt(e.ModifiedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n)}for(var i=0;i!=An.length;++i){var s=An[i];var l=t.Props&&t.Props[s[1]]!=null?t.Props[s[1]]:e?e[s[1]]:null;if(l===true)l="1";else if(l===false)l="0";else if(typeof l=="number")l=String(l);if(l!=null)Fn(s[0],l,null,a,n)}if(a.length>2){a[a.length]="</cp:coreProperties>";a[1]=a[1].replace("/>",">")}return a.join("")}var On=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];var Mn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function Nn(e,r,t,a){var n=[];if(typeof e=="string")n=mt(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(e){return{v:e}}));var s=typeof r=="string"?mt(r,a).map(function(e){return e.v}):r;var l=0,o=0;if(s.length>0)for(var c=0;c!==n.length;c+=2){o=+n[c+1].v;switch(n[c].v){case"Worksheets":;case"工作表":;case"Листы":;case"أوراق العمل":;case"ワークシート":;case"גליונות עבודה":;case"Arbeitsblätter":;case"Çalışma Sayfaları":;case"Feuilles de calcul":;case"Fogli di lavoro":;case"Folhas de cálculo":;case"Planilhas":;case"Regneark":;case"Hojas de cálculo":;case"Werkbladen":t.Worksheets=o;t.SheetNames=s.slice(l,l+o);break;case"Named Ranges":;case"Rangos con nombre":;case"名前付き一覧":;case"Benannte Bereiche":;case"Navngivne områder":t.NamedRanges=o;t.DefinedNames=s.slice(l,l+o);break;case"Charts":;case"Diagramme":t.Chartsheets=o;t.ChartNames=s.slice(l,l+o);break;}l+=o}}function Pn(e,r,t){var a={};if(!r)r={};e=ot(e);On.forEach(function(t){var n=(e.match(ft(t[0]))||[])[1];switch(t[2]){case"string":if(n)r[t[1]]=Gr(n);break;case"bool":r[t[1]]=n==="true";break;case"raw":var i=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));if(i&&i.length>0)a[t[1]]=i[1];break;}});if(a.HeadingPairs&&a.TitlesOfParts)Nn(a.HeadingPairs,a.TitlesOfParts,r,t);return r}function In(e){var r=[],t=wt;if(!e)e={};e.Application="SheetJS";r[r.length]=Rr;r[r.length]=wt("Properties",null,{xmlns:Ct.EXT_PROPS,"xmlns:vt":Ct.vt});On.forEach(function(a){if(e[a[1]]===undefined)return;var n;switch(a[2]){case"string":n=Kr(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break;}if(n!==undefined)r[r.length]=t(a[0],n)});r[r.length]=t("HeadingPairs",t("vt:vector",t("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+t("vt:variant",t("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"}));r[r.length]=t("TitlesOfParts",t("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+Kr(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"}));if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}var Rn=/<[^>]+>[^<]*/g;function Ln(e,r){var t={},a="";var n=e.match(Rn);if(n)for(var i=0;i!=n.length;++i){var s=n[i],l=jr(s);switch(l[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Gr(l.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var o=s.split(">");var c=o[0].slice(4),f=o[1];switch(c){case"lpstr":;case"bstr":;case"lpwstr":t[a]=Gr(f);break;case"bool":t[a]=at(f);break;case"i1":;case"i2":;case"i4":;case"i8":;case"int":;case"uint":t[a]=parseInt(f,10);break;case"r4":;case"r8":;case"decimal":t[a]=parseFloat(f);break;case"filetime":;case"date":t[a]=mr(f);break;case"cy":;case"error":t[a]=Gr(f);break;default:if(c.slice(-1)=="/")break;if(r.WTF&&typeof console!=="undefined")console.warn("Unexpected",s,c,o);}}else if(s.slice(0,2)==="</"){}else if(r.WTF)throw new Error(s);}}return t}function Bn(e){var r=[Rr,wt("Properties",null,{xmlns:Ct.CUST_PROPS,"xmlns:vt":Ct.vt})];if(!e)return r.join("");var t=1;er(e).forEach(function a(n){++t;r[r.length]=wt("property",kt(e[n],true),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:t,name:Kr(n)})});if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}var $n=[2,3,48,49,131,139,140,245];var zn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969};var r=tr({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function a(r,t){var a=[];var n=x(1);switch(t.type){case"base64":n=C(w(r));break;case"binary":n=C(r);break;case"buffer":;case"array":n=r;break;}ca(n,0);var i=n._R(1);var s=!!(i&136);var l=false,o=false;switch(i){case 2:break;case 3:break;case 48:l=true;s=true;break;case 49:l=true;s=true;break;case 131:break;case 139:break;case 140:o=true;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+i.toString(16));}var c=0,f=521;if(i==2)c=n._R(2);n.l+=3;if(i!=2)c=n._R(4);if(c>1048576)c=1e6;if(i!=2)f=n._R(2);var u=n._R(2);var h=t.codepage||1252;if(i!=2){n.l+=16;n._R(1);if(n[n.l]!==0)h=e[n[n.l]];n.l+=1;n.l+=2}if(o)n.l+=36;var p=[],d={};var m=Math.min(n.length,i==2?521:f-10-(l?264:0));var v=o?32:11;while(n.l<m&&n[n.l]!=13){d={};d.name=$cptable.utils.decode(h,n.slice(n.l,n.l+v)).replace(/[\u0000\r\n].*$/g,"");n.l+=v;d.type=String.fromCharCode(n._R(1));if(i!=2&&!o)d.offset=n._R(4);d.len=n._R(1);if(i==2)d.offset=n._R(2);d.dec=n._R(1);if(d.name.length)p.push(d);if(i!=2)n.l+=o?13:14;switch(d.type){case"B":if((!l||d.len!=8)&&t.WTF)console.log("Skipping "+d.name+":"+d.type);break;case"G":;case"P":if(t.WTF)console.log("Skipping "+d.name+":"+d.type);break;case"+":;case"0":;case"@":;case"C":;case"D":;case"F":;case"I":;case"L":;case"M":;case"N":;case"O":;case"T":;case"Y":break;default:throw new Error("Unknown Field Type: "+d.type);}}if(n[n.l]!==13)n.l=f-1;if(n._R(1)!==13)throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=f;var g=0,b=0;a[0]=[];for(b=0;b!=p.length;++b)a[0][b]=p[b].name;while(c-- >0){if(n[n.l]===42){n.l+=u;continue}++n.l;a[++g]=[];b=0;for(b=0;b!=p.length;++b){var y=n.slice(n.l,n.l+p[b].len);n.l+=p[b].len;ca(y,0);var k=$cptable.utils.decode(h,y);switch(p[b].type){case"C":if(k.trim().length)a[g][b]=k.replace(/\s+$/,"");break;case"D":if(k.length===8)a[g][b]=new Date(+k.slice(0,4),+k.slice(4,6)-1,+k.slice(6,8));else a[g][b]=k;break;case"F":a[g][b]=parseFloat(k.trim());break;case"+":;case"I":a[g][b]=o?y._R(-4,"i")^2147483648:y._R(4,"i");break;case"L":switch(k.trim().toUpperCase()){case"Y":;case"T":a[g][b]=true;break;case"N":;case"F":a[g][b]=false;break;case"":;case"?":break;default:throw new Error("DBF Unrecognized L:|"+k+"|");}break;case"M":if(!s)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));a[g][b]="##MEMO##"+(o?parseInt(k.trim(),10):y._R(4));break;case"N":k=k.replace(/\u0000/g,"").trim();if(k&&k!=".")a[g][b]=+k||0;break;case"@":a[g][b]=new Date(y._R(-8,"f")-621356832e5);break;case"T":a[g][b]=new Date((y._R(4)-2440588)*864e5+y._R(4));break;case"Y":a[g][b]=y._R(4,"i")/1e4+y._R(4,"i")/1e4*Math.pow(2,32);break;case"O":a[g][b]=-y._R(-8,"f");break;case"B":if(l&&p[b].len==8){a[g][b]=y._R(8,"f");break};case"G":;case"P":y.l+=p[b].len;break;case"0":if(p[b].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+p[b].type);}}}if(i!=2)if(n.l<n.length&&n[n.l++]!=26)throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));if(t&&t.sheetRows)a=a.slice(0,t.sheetRows);t.DBF=p;return a}function n(e,r){var t=r||{};if(!t.dateNF)t.dateNF="yyyymmdd";var n=La(a(e,t),t);n["!cols"]=t.DBF.map(function(e){return{wch:e.len,DBF:e}});delete t.DBF;return n}function i(e,r){try{return Ia(n(e,r),r)}catch(t){if(r&&r.WTF)throw t}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function o(e,a){var n=a||{};if(+n.codepage>=0)l(+n.codepage);if(n.type=="string")throw new Error("Cannot write DBF to JS string");var i=pa();var o=tc(e,{header:1,raw:true,cellDates:true});var c=o[0],f=o.slice(1),u=e["!cols"]||[];var h=0,p=0,d=0,m=1;for(h=0;h<c.length;++h){if(((u[h]||{}).DBF||{}).name){c[h]=u[h].DBF.name;++d;continue}if(c[h]==null)continue;++d;if(typeof c[h]==="number")c[h]=c[h].toString(10);if(typeof c[h]!=="string")throw new Error("DBF Invalid column name "+c[h]+" |"+typeof c[h]+"|");if(c.indexOf(c[h])!==h)for(p=0;p<1024;++p)if(c.indexOf(c[h]+"_"+p)==-1){c[h]+="_"+p;break}}var v=Ma(e["!ref"]);var g=[];var b=[];var w=[];for(h=0;h<=v.e.c-v.s.c;++h){var y="",k="",x=0;var S=[];for(p=0;p<f.length;++p){if(f[p][h]!=null)S.push(f[p][h])}if(S.length==0||c[h]==null){g[h]="?";continue}for(p=0;p<S.length;++p){switch(typeof S[p]){case"number":k="B";break;case"string":k="C";break;case"boolean":k="L";break;case"object":k=S[p]instanceof Date?"D":"C";break;default:k="C";}x=Math.max(x,String(S[p]).length);y=y&&y!=k?"C":k}if(x>250)x=250;k=((u[h]||{}).DBF||{}).type;if(k=="C"){if(u[h].DBF.len>x)x=u[h].DBF.len}if(y=="B"&&k=="N"){y="N";w[h]=u[h].DBF.dec;x=u[h].DBF.len}b[h]=y=="C"||k=="N"?x:s[y]||0;m+=b[h];g[h]=y}var C=i.next(32);C._W(4,318902576);C._W(4,f.length);C._W(2,296+32*d);C._W(2,m);for(h=0;h<4;++h)C._W(4,0);C._W(4,0|(+r[t]||3)<<8);for(h=0,p=0;h<c.length;++h){if(c[h]==null)continue;var _=i.next(32);var A=(c[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_._W(1,A,"sbcs");_._W(1,g[h]=="?"?"C":g[h],"sbcs");_._W(4,p);_._W(1,b[h]||s[g[h]]||0);_._W(1,w[h]||0);_._W(1,2);_._W(4,0);_._W(1,0);_._W(4,0);_._W(4,0);p+=b[h]||s[g[h]]||0}var T=i.next(264);T._W(4,13);for(h=0;h<65;++h)T._W(4,0);for(h=0;h<f.length;++h){var E=i.next(m);E._W(1,0);for(p=0;p<c.length;++p){if(c[p]==null)continue;switch(g[p]){case"L":E._W(1,f[h][p]==null?63:f[h][p]?84:70);break;case"B":E._W(8,f[h][p]||0,"f");break;case"N":var F="0";if(typeof f[h][p]=="number")F=f[h][p].toFixed(w[p]||0);for(d=0;d<b[p]-F.length;++d)E._W(1,32);E._W(1,F,"sbcs");break;case"D":if(!f[h][p])E._W(8,"00000000","sbcs");else{E._W(4,("0000"+f[h][p].getFullYear()).slice(-4),"sbcs");E._W(2,("00"+(f[h][p].getMonth()+1)).slice(-2),"sbcs");E._W(2,("00"+f[h][p].getDate()).slice(-2),"sbcs")}break;case"C":var D=String(f[h][p]!=null?f[h][p]:"").slice(0,b[p]);E._W(1,D,"sbcs");for(d=0;d<b[p]-D.length;++d)E._W(1,32);break;}}}i.next(1)._W(1,26);return i.end()}return{to_workbook:i,to_sheet:n,from_sheet:o}}();var Un=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223};var r=new RegExp("N("+er(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm");var t=function(r,t){var a=e[t];return typeof a=="number"?d(a):a};var a=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return a==59?e:d(a)};e["|"]=254;function n(e,r){switch(r.type){case"base64":return i(w(e),r);case"binary":return i(e,r);case"buffer":return i(y&&Buffer.isBuffer(e)?e.toString("binary"):A(e),r);case"array":return i(vr(e),r);}throw new Error("Unrecognized type "+r.type)}function i(e,n){var i=e.split(/[\n\r]+/),s=-1,o=-1,c=0,f=0,u=[];var h=[];var p=null;var d={},m=[],v=[],g=[];var b=0,w;if(+n.codepage>=0)l(+n.codepage);for(;c!==i.length;++c){b=0;var y=i[c].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(r,t);var k=y.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")});var x=k[0],S;if(y.length>0)switch(x){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":if(k[1].charAt(0)=="P")h.push(y.slice(3).replace(/;;/g,";"));break;case"C":var C=false,_=false,A=false,T=false,E=-1,F=-1;for(f=1;f<k.length;++f)switch(k[f].charAt(0)){case"A":break;case"X":o=parseInt(k[f].slice(1))-1;_=true;break;case"Y":s=parseInt(k[f].slice(1))-1;if(!_)o=0;for(w=u.length;w<=s;++w)u[w]=[];break;case"K":S=k[f].slice(1);if(S.charAt(0)==='"')S=S.slice(1,S.length-1);else if(S==="TRUE")S=true;else if(S==="FALSE")S=false;else if(!isNaN(wr(S))){S=wr(S);if(p!==null&&Ne(p))S=fr(S)}else if(!isNaN(kr(S).getDate())){S=mr(S)}if(typeof $cptable!=="undefined"&&typeof S=="string"&&(n||{}).type!="string"&&(n||{}).codepage)S=$cptable.utils.decode(n.codepage,S);C=true;break;case"E":T=true;var D=ms(k[f].slice(1),{r:s,c:o});u[s][o]=[u[s][o],D];break;case"S":A=true;u[s][o]=[u[s][o],"S5S"];break;case"G":break;case"R":E=parseInt(k[f].slice(1))-1;break;case"C":F=parseInt(k[f].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y);}if(C){if(u[s][o]&&u[s][o].length==2)u[s][o][0]=S;else u[s][o]=S;p=null}if(A){if(T)throw new Error("SYLK shared formula cannot have own formula");var O=E>-1&&u[E][F];if(!O||!O[1])throw new Error("SYLK shared formula cannot find base");u[s][o][1]=bs(O[1],{r:s-E,c:o-F})}break;case"F":var M=0;for(f=1;f<k.length;++f)switch(k[f].charAt(0)){case"X":o=parseInt(k[f].slice(1))-1;++M;break;case"Y":s=parseInt(k[f].slice(1))-1;for(w=u.length;w<=s;++w)u[w]=[];break;case"M":b=parseInt(k[f].slice(1))/20;break;case"F":break;case"G":break;case"P":p=h[parseInt(k[f].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":g=k[f].slice(1).split(" ");for(w=parseInt(g[0],10);w<=parseInt(g[1],10);++w){b=parseInt(g[2],10);v[w-1]=b===0?{hidden:true}:{wch:b};wi(v[w-1])}break;case"C":o=parseInt(k[f].slice(1))-1;if(!v[o])v[o]={};break;case"R":s=parseInt(k[f].slice(1))-1;if(!m[s])m[s]={};if(b>0){m[s].hpt=b;m[s].hpx=Si(b)}else if(b===0)m[s].hidden=true;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y);}if(M<1)p=null;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y);}}if(m.length>0)d["!rows"]=m;if(v.length>0)d["!cols"]=v;if(n&&n.sheetRows)u=u.slice(0,n.sheetRows);return[u,d]}function s(e,r){var t=n(e,r);var a=t[0],i=t[1];var s=La(a,r);er(i).forEach(function(e){s[e]=i[e]});return s}function o(e,r){return Ia(s(e,r),r)}function c(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0;if(e.f&&!e.F)n+=";E"+gs(e.f,{r:t,c:a});break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"';break;}return n}function f(e,r){r.forEach(function(r,t){var a="F;W"+(t+1)+" "+(t+1)+" ";if(r.hidden)a+="0";else{if(typeof r.width=="number"&&!r.wpx)r.wpx=di(r.width);if(typeof r.wpx=="number"&&!r.wch)r.wch=mi(r.wpx);if(typeof r.wch=="number")a+=Math.round(r.wch)}if(a.charAt(a.length-1)!=" ")e.push(a)})}function u(e,r){r.forEach(function(r,t){var a="F;";if(r.hidden)a+="M0;";else if(r.hpt)a+="M"+20*r.hpt+";";else if(r.hpx)a+="M"+20*xi(r.hpx)+";";if(a.length>2)e.push(a+"R"+(t+1))})}function h(e,r){var t=["ID;PWXL;N;E"],a=[];var n=Ma(e["!ref"]),i;var s=Array.isArray(e);var l="\r\n";t.push("P;PGeneral");t.push("F;P0;DG0G8;M255");if(e["!cols"])f(t,e["!cols"]);if(e["!rows"])u(t,e["!rows"]);t.push("B;Y"+(n.e.r-n.s.r+1)+";X"+(n.e.c-n.s.c+1)+";D"+[n.s.c,n.s.r,n.e.c,n.e.r].join(" "));for(var o=n.s.r;o<=n.e.r;++o){for(var h=n.s.c;h<=n.e.c;++h){var p=Fa({r:o,c:h});i=s?(e[o]||[])[h]:e[p];if(!i||i.v==null&&(!i.f||i.F))continue;a.push(c(i,e,o,h,r))}}return t.join(l)+l+a.join(l)+l+"E"+l}return{to_workbook:o,to_sheet:s,from_sheet:h}}();var Wn=function(){function e(e,t){switch(t.type){case"base64":return r(w(e),t);case"binary":return r(e,t);case"buffer":return r(y&&Buffer.isBuffer(e)?e.toString("binary"):A(e),t);case"array":return r(vr(e),t);}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=e.split("\n"),a=-1,n=-1,i=0,s=[];for(;i!==t.length;++i){if(t[i].trim()==="BOT"){s[++a]=[];n=0;continue}if(a<0)continue;var l=t[i].trim().split(",");var o=l[0],c=l[1];++i;var f=t[i]||"";while((f.match(/["]/g)||[]).length&1&&i<t.length-1)f+="\n"+t[++i];f=f.trim();switch(+o){case-1:if(f==="BOT"){s[++a]=[];n=0;continue}else if(f!=="EOD")throw new Error("Unrecognized DIF special command "+f);break;case 0:if(f==="TRUE")s[a][n]=true;else if(f==="FALSE")s[a][n]=false;else if(!isNaN(wr(c)))s[a][n]=wr(c);else if(!isNaN(kr(c).getDate()))s[a][n]=mr(c);else s[a][n]=c;++n;break;case 1:f=f.slice(1,f.length-1);f=f.replace(/""/g,'"');if(v&&f&&f.match(/^=".*"$/))f=f.slice(2,-1);s[a][n++]=f!==""?f:null;break;}if(f==="EOD")break}if(r&&r.sheetRows)s=s.slice(0,r.sheetRows);return s}function t(r,t){return La(e(r,t),t)}function a(e,r){return Ia(t(e,r),r)}var n=function(){var e=function t(e,r,a,n,i){e.push(r);e.push(a+","+n);e.push('"'+i.replace(/"/g,'""')+'"')};var r=function a(e,r,t,n){e.push(r+","+t);e.push(r==1?'"'+n.replace(/"/g,'""')+'"':n)};return function n(t){var a=[];var n=Ma(t["!ref"]),i;var s=Array.isArray(t);e(a,"TABLE",0,1,"sheetjs");e(a,"VECTORS",0,n.e.r-n.s.r+1,"");e(a,"TUPLES",0,n.e.c-n.s.c+1,"");e(a,"DATA",0,0,"");for(var l=n.s.r;l<=n.e.r;++l){r(a,-1,0,"BOT");for(var o=n.s.c;o<=n.e.c;++o){var c=Fa({r:l,c:o});i=s?(t[l]||[])[o]:t[c];if(!i){r(a,1,0,"");continue}switch(i.t){case"n":var f=v?i.w:i.v;if(!f&&i.v!=null)f=i.v;if(f==null){if(v&&i.f&&!i.F)r(a,1,0,"="+i.f);else r(a,1,0,"")}else r(a,0,f,"V");break;case"b":r(a,0,i.v?1:0,i.v?"TRUE":"FALSE");break;case"s":r(a,1,0,!v||isNaN(i.v)?i.v:'="'+i.v+'"');break;case"d":if(!i.w)i.w=Be(i.z||X[14],sr(mr(i.v)));if(v)r(a,0,i.w,"V");else r(a,1,0,i.w);break;default:r(a,1,0,"");}}}r(a,-1,0,"EOD");var u="\r\n";var h=a.join(u);return h}}();return{to_workbook:a,to_sheet:t,from_sheet:n}}();var jn=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function r(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(r,t){var a=r.split("\n"),n=-1,i=-1,s=0,l=[];for(;s!==a.length;++s){var o=a[s].trim().split(":");if(o[0]!=="cell")continue;var c=Ea(o[1]);if(l.length<=c.r)for(n=l.length;n<=c.r;++n)if(!l[n])l[n]=[];n=c.r;i=c.c;switch(o[2]){case"t":l[n][i]=e(o[3]);break;case"v":l[n][i]=+o[3];break;case"vtf":var f=o[o.length-1];case"vtc":switch(o[3]){case"nl":l[n][i]=+o[4]?true:false;
break;default:l[n][i]=+o[4];break;}if(o[2]=="vtf")l[n][i]=[l[n][i],f];}}if(t&&t.sheetRows)l=l.slice(0,t.sheetRows);return l}function a(e,r){return La(t(e,r),r)}function n(e,r){return Ia(a(e,r),r)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n");var s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n";var l=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n");var o="--SocialCalcSpreadsheetControlSave--";function c(e){if(!e||!e["!ref"])return"";var t=[],a=[],n,i="";var s=Da(e["!ref"]);var l=Array.isArray(e);for(var o=s.s.r;o<=s.e.r;++o){for(var c=s.s.c;c<=s.e.c;++c){i=Fa({r:o,c:c});n=l?(e[o]||[])[c]:e[i];if(!n||n.v==null||n.t==="z")continue;a=["cell",i,"t"];switch(n.t){case"s":;case"str":a.push(r(n.v));break;case"n":if(!n.f){a[2]="v";a[3]=n.v}else{a[2]="vtf";a[3]="n";a[4]=n.v;a[5]=r(n.f)}break;case"b":a[2]="vt"+(n.f?"f":"c");a[3]="nl";a[4]=n.v?"1":"0";a[5]=r(n.f||(n.v?"TRUE":"FALSE"));break;case"d":var f=sr(mr(n.v));a[2]="vtc";a[3]="nd";a[4]=""+f;a[5]=n.w||Be(n.z||X[14],f);break;case"e":continue;}t.push(a.join(":"))}}t.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1");t.push("valueformat:1:text-wiki");return t.join("\n")}function f(e){return[i,s,l,s,c(e),o].join("\n")}return{to_workbook:n,to_sheet:a,from_sheet:f}}();var Hn=function(){function e(e,r,t,a,n){if(n.raw)r[t][a]=e;else if(e===""){}else if(e==="TRUE")r[t][a]=true;else if(e==="FALSE")r[t][a]=false;else if(!isNaN(wr(e)))r[t][a]=wr(e);else if(!isNaN(kr(e).getDate()))r[t][a]=mr(e);else r[t][a]=e}function r(r,t){var a=t||{};var n=[];if(!r||r.length===0)return n;var i=r.split(/[\r\n]/);var s=i.length-1;while(s>=0&&i[s].length===0)--s;var l=10,o=0;var c=0;for(;c<=s;++c){o=i[c].indexOf(" ");if(o==-1)o=i[c].length;else o++;l=Math.max(l,o)}for(c=0;c<=s;++c){n[c]=[];var f=0;e(i[c].slice(0,l).trim(),n,c,f,a);for(f=1;f<=(i[c].length-l)/10+1;++f)e(i[c].slice(l+(f-1)*10,l+f*10).trim(),n,c,f,a)}if(a.sheetRows)n=n.slice(0,a.sheetRows);return n}var t={44:",",9:"\t",59:";",124:"|"};var a={44:3,9:2,59:1,124:0};function n(e){var r={},n=false,i=0,s=0;for(;i<e.length;++i){if((s=e.charCodeAt(i))==34)n=!n;else if(!n&&s in t)r[s]=(r[s]||0)+1}s=[];for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}if(!s.length){r=a;for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}}s.sort(function(e,r){return e[0]-r[0]||a[e[1]]-a[r[1]]});return t[s.pop()[1]]||44}function i(e,r){var t=r||{};var a="";if(m!=null&&t.dense==null)t.dense=m;var i=t.dense?[]:{};var s={s:{c:0,r:0},e:{c:0,r:0}};if(e.slice(0,4)=="sep="){if(e.charCodeAt(5)==13&&e.charCodeAt(6)==10){a=e.charAt(4);e=e.slice(7)}else if(e.charCodeAt(5)==13||e.charCodeAt(5)==10){a=e.charAt(4);e=e.slice(6)}else a=n(e.slice(0,1024))}else if(t&&t.FS)a=t.FS;else a=n(e.slice(0,1024));var l=0,o=0,c=0;var f=0,u=0,h=a.charCodeAt(0),p=false,d=0,v=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g=t.dateNF!=null?Ve(t.dateNF):null;function b(){var r=e.slice(f,u);var a={};if(r.charAt(0)=='"'&&r.charAt(r.length-1)=='"')r=r.slice(1,-1).replace(/""/g,'"');if(r.length===0)a.t="z";else if(t.raw){a.t="s";a.v=r}else if(r.trim().length===0){a.t="s";a.v=r}else if(r.charCodeAt(0)==61){if(r.charCodeAt(1)==34&&r.charCodeAt(r.length-1)==34){a.t="s";a.v=r.slice(2,-1).replace(/""/g,'"')}else if(ys(r)){a.t="n";a.f=r.slice(1)}else{a.t="s";a.v=r}}else if(r=="TRUE"){a.t="b";a.v=true}else if(r=="FALSE"){a.t="b";a.v=false}else if(!isNaN(c=wr(r))){a.t="n";if(t.cellText!==false)a.w=r;a.v=c}else if(!isNaN(kr(r).getDate())||g&&r.match(g)){a.z=t.dateNF||X[14];var n=0;if(g&&r.match(g)){r=Xe(r,t.dateNF,r.match(g)||[]);n=1}if(t.cellDates){a.t="d";a.v=mr(r,n)}else{a.t="n";a.v=sr(mr(r,n))}if(t.cellText!==false)a.w=Be(a.z,a.v instanceof Date?sr(a.v):a.v);if(!t.cellNF)delete a.z}else{a.t="s";a.v=r}if(a.t=="z"){}else if(t.dense){if(!i[l])i[l]=[];i[l][o]=a}else i[Fa({c:o,r:l})]=a;f=u+1;v=e.charCodeAt(f);if(s.e.c<o)s.e.c=o;if(s.e.r<l)s.e.r=l;if(d==h)++o;else{o=0;++l;if(t.sheetRows&&t.sheetRows<=l)return true}}e:for(;u<e.length;++u)switch(d=e.charCodeAt(u)){case 34:if(v===34)p=!p;break;case h:;case 10:;case 13:if(!p&&b())break e;break;default:break;}if(u-f>0)b();i["!ref"]=Oa(s);return i}function s(e,t){if(!(t&&t.PRN))return i(e,t);if(t.FS)return i(e,t);if(e.slice(0,4)=="sep=")return i(e,t);if(e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0)return i(e,t);return La(r(e,t),t)}function l(e,r){var t="",a=r.type=="string"?[0,0,0,0]:Oo(e,r);switch(r.type){case"base64":t=w(e);break;case"binary":t=e;break;case"buffer":if(r.codepage==65001)t=e.toString("utf8");else if(r.codepage&&typeof $cptable!=="undefined")t=$cptable.utils.decode(r.codepage,e);else t=y&&Buffer.isBuffer(e)?e.toString("binary"):A(e);break;case"array":t=vr(e);break;case"string":t=e;break;default:throw new Error("Unrecognized type "+r.type);}if(a[0]==239&&a[1]==187&&a[2]==191)t=ot(t.slice(3));else if(r.type!="string"&&r.type!="buffer"&&r.codepage==65001)t=ot(t);else if(r.type=="binary"&&typeof $cptable!=="undefined"&&r.codepage)t=$cptable.utils.decode(r.codepage,$cptable.utils.encode(28591,t));if(t.slice(0,19)=="socialcalc:version:")return jn.to_sheet(r.type=="string"?t:ot(t),r);return s(t,r)}function o(e,r){return Ia(l(e,r),r)}function c(e){var r=[];var t=Ma(e["!ref"]),a;var n=Array.isArray(e);for(var i=t.s.r;i<=t.e.r;++i){var s=[];for(var l=t.s.c;l<=t.e.c;++l){var o=Fa({r:i,c:l});a=n?(e[i]||[])[l]:e[o];if(!a||a.v==null){s.push("          ");continue}var c=(a.w||(Pa(a),a.w)||"").slice(0,10);while(c.length<10)c+=" ";s.push(c+(l===0?" ":""))}r.push(s.join(""))}return r.join("\n")}return{to_workbook:o,to_sheet:l,from_sheet:c}}();function Vn(e,r){var t=r||{},a=!!t.WTF;t.WTF=true;try{var n=Un.to_workbook(e,t);t.WTF=a;return n}catch(i){t.WTF=a;if(!i.message.match(/SYLK bad record ID/)&&a)throw i;return Hn.to_workbook(e,r)}}function Xn(e){var r={},t=e.match(zr),a=0;var i=false;if(t)for(;a!=t.length;++a){var s=jr(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":;case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;r.cp=n[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":;case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":;case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting";break;};case"<u>":;case"<u/>":r.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":;case"<b/>":r.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":;case"<i/>":r.i=1;break;case"</i>":break;case"<color":if(s.rgb)r.color=s.rgb.slice(2,8);break;case"<color>":;case"<color/>":;case"</color>":break;case"<family":r.family=s.val;break;case"<family>":;case"<family/>":;case"</family>":break;case"<vertAlign":r.valign=s.val;break;case"<vertAlign>":;case"<vertAlign/>":;case"</vertAlign>":break;case"<scheme":break;case"<scheme>":;case"<scheme/>":;case"</scheme>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(s[0].charCodeAt(1)!==47&&!i)throw new Error("Unrecognized rich format "+s[0]);}}return r}var Gn=function(){var e=ft("t"),r=ft("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:Gr(a[1])};var i=t.match(r);if(i)n.s=Xn(i[1]);return n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function i(e){return e.replace(a,"").split(n).map(t).filter(function(e){return e.v})}}();var Yn=function Lc(){var e=/(\r\n|\n)/g;function r(e,r,t){var a=[];if(e.u)a.push("text-decoration: underline;");if(e.uval)a.push("text-underline-style:"+e.uval+";");if(e.sz)a.push("font-size:"+e.sz+"pt;");if(e.outline)a.push("text-effect: outline;");if(e.shadow)a.push("text-shadow: auto;");r.push('<span style="'+a.join("")+'">');if(e.b){r.push("<b>");t.push("</b>")}if(e.i){r.push("<i>");t.push("</i>")}if(e.strike){r.push("<s>");t.push("</s>")}var n=e.valign||"";if(n=="superscript"||n=="super")n="sup";else if(n=="subscript")n="sub";if(n!=""){r.push("<"+n+">");t.push("</"+n+">")}t.push("</span>");return e}function t(t){var a=[[],t.v,[]];if(!t.v)return"";if(t.s)r(t.s,a[0],a[2]);return a[0].join("")+a[1].replace(e,"<br/>")+a[2].join("")}return function a(e){return e.map(t).join("")}}();var Jn=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Kn=/<(?:\w+:)?r>/;var qn=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Zn(e,r){var t=r?r.cellHTML:true;var a={};if(!e)return{t:""};if(e.match(/^\s*<(?:\w+:)?t[^>]*>/)){a.t=Gr(ot(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||""));a.r=ot(e);if(t)a.h=Qr(a.t)}else if(e.match(Kn)){a.r=ot(e);a.t=Gr(ot((e.replace(qn,"").match(Jn)||[]).join("").replace(zr,"")));if(t)a.h=Yn(Gn(a.r))}return a}var Qn=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/;var ei=/<(?:\w+:)?(?:si|sstItem)>/g;var ri=/<\/(?:\w+:)?(?:si|sstItem)>/;function ti(e,r){var t=[],a="";if(!e)return t;var n=e.match(Qn);if(n){a=n[2].replace(ei,"").split(ri);for(var i=0;i!=a.length;++i){var s=Zn(a[i].trim(),r);if(s!=null)t[t.length]=s}n=jr(n[1]);t.Count=n.count;t.Unique=n.uniqueCount}return t}var ai=/^\s|\s$|[\t\n\r]/;function ni(e,r){if(!r.bookSST)return"";var t=[Rr];t[t.length]=wt("sst",null,{xmlns:_t[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a){if(e[a]==null)continue;var n=e[a];var i="<si>";if(n.r)i+=n.r;else{i+="<t";if(!n.t)n.t="";if(n.t.match(ai))i+=' xml:space="preserve"';i+=">"+Kr(n.t)+"</t>"}i+="</si>";t[t.length]=i}if(t.length>2){t[t.length]="</sst>";t[1]=t[1].replace("/>",">")}return t.join("")}function ii(e){var r=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(r.slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]}function si(e){for(var r=0,t=1;r!=3;++r)t=t*256+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function li(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255;var n=Math.max(r,t,a),i=Math.min(r,t,a),s=n-i;if(s===0)return[0,0,r];var l=0,o=0,c=n+i;o=s/(c>1?2-c:c);switch(n){case r:l=((t-a)/s+6)%6;break;case t:l=(a-r)/s+2;break;case a:l=(r-t)/s+4;break;}return[l/6,o,c/2]}function oi(e){var r=e[0],t=e[1],a=e[2];var n=t*2*(a<.5?a:1-a),i=a-n/2;var s=[i,i,i],l=6*r;var o;if(t!==0)switch(l|0){case 0:;case 6:o=n*l;s[0]+=n;s[1]+=o;break;case 1:o=n*(2-l);s[0]+=o;s[1]+=n;break;case 2:o=n*(l-2);s[1]+=n;s[2]+=o;break;case 3:o=n*(4-l);s[1]+=o;s[2]+=n;break;case 4:o=n*(l-4);s[2]+=n;s[0]+=o;break;case 5:o=n*(6-l);s[2]+=o;s[0]+=n;break;}for(var c=0;c!=3;++c)s[c]=Math.round(s[c]*255);return s}function ci(e,r){if(r===0)return e;var t=li(ii(e));if(r<0)t[2]=t[2]*(1+r);else t[2]=1-(1-t[2])*(1-r);return si(oi(t))}var fi=6,ui=15,hi=1,pi=fi;function di(e){return Math.floor((e+Math.round(128/pi)/256)*pi)}function mi(e){return Math.floor((e-5)/pi*100+.5)/100}function vi(e){return Math.round((e*pi+5)/pi*256)/256}function gi(e){return vi(mi(di(e)))}function bi(e){var r=Math.abs(e-gi(e)),t=pi;if(r>.005)for(pi=hi;pi<ui;++pi)if(Math.abs(e-gi(e))<=r){r=Math.abs(e-gi(e));t=pi}pi=t}function wi(e){if(e.width){e.wpx=di(e.width);e.wch=mi(e.wpx);e.MDW=pi}else if(e.wpx){e.wch=mi(e.wpx);e.width=vi(e.wch);e.MDW=pi}else if(typeof e.wch=="number"){e.width=vi(e.wch);e.wpx=di(e.width);e.MDW=pi}if(e.customWidth)delete e.customWidth}var yi=96,ki=yi;function xi(e){return e*96/ki}function Si(e){return e*ki/96}var Ci={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function _i(e,r,t,a){r.Borders=[];var n={};var i=false;(e[0].match(zr)||[]).forEach(function(e){var t=jr(e);switch(Hr(t[0])){case"<borders":;case"<borders>":;case"</borders>":break;case"<border":;case"<border>":;case"<border/>":n={};if(t.diagonalUp)n.diagonalUp=at(t.diagonalUp);if(t.diagonalDown)n.diagonalDown=at(t.diagonalDown);r.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":;case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":;case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":;case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":;case"<bottom>":break;case"</bottom>":break;case"<diagonal":;case"<diagonal>":;case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":;case"<horizontal>":;case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":;case"<vertical>":;case"<vertical/>":break;case"</vertical>":break;case"<start":;case"<start>":;case"<start/>":break;case"</start>":break;case"<end":;case"<end>":;case"<end/>":break;case"</end>":break;case"<color":;case"<color>":break;case"<color/>":;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in borders")};}})}function Ai(e,r,t,a){r.Fills=[];var n={};var i=false;(e[0].match(zr)||[]).forEach(function(e){var t=jr(e);switch(Hr(t[0])){case"<fills":;case"<fills>":;case"</fills>":break;case"<fill>":;case"<fill":;case"<fill/>":n={};r.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":;case"</gradientFill>":r.Fills.push(n);n={};break;case"<patternFill":;case"<patternFill>":if(t.patternType)n.patternType=t.patternType;break;case"<patternFill/>":;case"</patternFill>":break;case"<bgColor":if(!n.bgColor)n.bgColor={};if(t.indexed)n.bgColor.indexed=parseInt(t.indexed,10);if(t.theme)n.bgColor.theme=parseInt(t.theme,10);if(t.tint)n.bgColor.tint=parseFloat(t.tint);if(t.rgb)n.bgColor.rgb=t.rgb.slice(-6);break;case"<bgColor/>":;case"</bgColor>":break;case"<fgColor":if(!n.fgColor)n.fgColor={};if(t.theme)n.fgColor.theme=parseInt(t.theme,10);if(t.tint)n.fgColor.tint=parseFloat(t.tint);if(t.rgb!=null)n.fgColor.rgb=t.rgb.slice(-6);break;case"<fgColor/>":;case"</fgColor>":break;case"<stop":;case"<stop/>":break;case"</stop>":break;case"<color":;case"<color/>":break;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in fills")};}})}function Ti(e,r,t,a){r.Fonts=[];var i={};var s=false;(e[0].match(zr)||[]).forEach(function(e){var l=jr(e);switch(Hr(l[0])){case"<fonts":;case"<fonts>":;case"</fonts>":break;case"<font":;case"<font>":break;case"</font>":;case"<font/>":r.Fonts.push(i);i={};break;case"<name":if(l.val)i.name=ot(l.val);break;case"<name/>":;case"</name>":break;case"<b":i.bold=l.val?at(l.val):1;break;case"<b/>":i.bold=1;break;case"<i":i.italic=l.val?at(l.val):1;break;case"<i/>":i.italic=1;break;case"<u":switch(l.val){case"none":i.underline=0;break;case"single":i.underline=1;break;case"double":i.underline=2;break;case"singleAccounting":i.underline=33;break;case"doubleAccounting":i.underline=34;break;}break;case"<u/>":i.underline=1;break;case"<strike":i.strike=l.val?at(l.val):1;break;case"<strike/>":i.strike=1;break;case"<outline":i.outline=l.val?at(l.val):1;break;case"<outline/>":i.outline=1;break;case"<shadow":i.shadow=l.val?at(l.val):1;break;case"<shadow/>":i.shadow=1;break;case"<condense":i.condense=l.val?at(l.val):1;break;case"<condense/>":i.condense=1;break;case"<extend":i.extend=l.val?at(l.val):1;break;case"<extend/>":i.extend=1;break;case"<sz":if(l.val)i.sz=+l.val;break;case"<sz/>":;case"</sz>":break;case"<vertAlign":if(l.val)i.vertAlign=l.val;break;case"<vertAlign/>":;case"</vertAlign>":break;case"<family":if(l.val)i.family=parseInt(l.val,10);break;case"<family/>":;case"</family>":break;case"<scheme":if(l.val)i.scheme=l.val;break;case"<scheme/>":;case"</scheme>":break;case"<charset":if(l.val=="1")break;l.codepage=n[parseInt(l.val,10)];break;case"<color":if(!i.color)i.color={};if(l.auto)i.color.auto=at(l.auto);if(l.rgb)i.color.rgb=l.rgb.slice(-6);else if(l.indexed){i.color.index=parseInt(l.indexed,10);var o=sn[i.color.index];if(i.color.index==81)o=sn[1];if(!o)o=sn[1];i.color.rgb=o[0].toString(16)+o[1].toString(16)+o[2].toString(16)}else if(l.theme){i.color.theme=parseInt(l.theme,10);if(l.tint)i.color.tint=parseFloat(l.tint);if(l.theme&&t.themeElements&&t.themeElements.clrScheme){i.color.rgb=ci(t.themeElements.clrScheme[i.color.theme].rgb,i.color.tint||0)}}break;case"<color/>":;case"</color>":break;case"<AlternateContent":s=true;break;case"</AlternateContent>":s=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":s=true;break;case"</ext>":s=false;break;default:if(a&&a.WTF){if(!s)throw new Error("unrecognized "+l[0]+" in fonts")};}})}function Ei(e,r,t){r.NumberFmt=[];var a=er(X);for(var n=0;n<a.length;++n)r.NumberFmt[a[n]]=X[a[n]];var i=e[0].match(zr);if(!i)return;for(n=0;n<i.length;++n){var s=jr(i[n]);switch(Hr(s[0])){case"<numFmts":;case"</numFmts>":;case"<numFmts/>":;case"<numFmts>":break;case"<numFmt":{var l=Gr(ot(s.formatCode)),o=parseInt(s.numFmtId,10);r.NumberFmt[o]=l;if(o>0){if(o>392){for(o=392;o>60;--o)if(r.NumberFmt[o]==null)break;r.NumberFmt[o]=l}$e(l,o)}}break;case"</numFmt>":break;default:if(t.WTF)throw new Error("unrecognized "+s[0]+" in numFmts");}}}function Fi(e){var r=["<numFmts>"];[[5,8],[23,26],[41,44],[50,392]].forEach(function(t){for(var a=t[0];a<=t[1];++a)if(e[a]!=null)r[r.length]=wt("numFmt",null,{numFmtId:a,formatCode:Kr(e[a])})});if(r.length===1)return"";r[r.length]="</numFmts>";r[0]=wt("numFmts",null,{count:r.length-2}).replace("/>",">");return r.join("")}var Di=["numFmtId","fillId","fontId","borderId","xfId"];var Oi=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Mi(e,r,t){r.CellXf=[];var a;var n=false;(e[0].match(zr)||[]).forEach(function(e){var i=jr(e),s=0;switch(Hr(i[0])){case"<cellXfs":;case"<cellXfs>":;case"<cellXfs/>":;case"</cellXfs>":break;case"<xf":;case"<xf/>":a=i;delete a[0];for(s=0;s<Di.length;++s)if(a[Di[s]])a[Di[s]]=parseInt(a[Di[s]],10);for(s=0;s<Oi.length;++s)if(a[Oi[s]])a[Oi[s]]=at(a[Oi[s]]);if(r.NumberFmt&&a.numFmtId>392){for(s=392;s>60;--s)if(r.NumberFmt[a.numFmtId]==r.NumberFmt[s]){a.numFmtId=s;break}}r.CellXf.push(a);break;case"</xf>":break;case"<alignment":;case"<alignment/>":var l={};if(i.vertical)l.vertical=i.vertical;if(i.horizontal)l.horizontal=i.horizontal;if(i.textRotation!=null)l.textRotation=i.textRotation;if(i.indent)l.indent=i.indent;if(i.wrapText)l.wrapText=at(i.wrapText);a.alignment=l;break;case"</alignment>":break;case"<protection":break;case"</protection>":;case"<protection/>":break;case"<AlternateContent":n=true;break;case"</AlternateContent>":n=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":n=true;break;case"</ext>":n=false;break;default:if(t&&t.WTF){if(!n)throw new Error("unrecognized "+i[0]+" in cellXfs")};}})}function Ni(e){var r=[];r[r.length]=wt("cellXfs",null);e.forEach(function(e){r[r.length]=wt("xf",null,e)});r[r.length]="</cellXfs>";if(r.length===2)return"";r[0]=wt("cellXfs",null,{count:r.length-2}).replace("/>",">");return r.join("")}var Pi=function Bc(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/;var r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/;var t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/;var a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/;var n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function i(s,l,o){var c={};if(!s)return c;s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var f;if(f=s.match(e))Ei(f,c,o);if(f=s.match(a))Ti(f,c,l,o);if(f=s.match(t))Ai(f,c,l,o);if(f=s.match(n))_i(f,c,l,o);if(f=s.match(r))Mi(f,c,o);return c}}();function Ii(e,r){var t=[Rr,wt("styleSheet",null,{xmlns:_t[0],"xmlns:vt":Ct.vt})],a;if(e.SSF&&(a=Fi(e.SSF))!=null)t[t.length]=a;t[t.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>';t[t.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>';t[t.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>';t[t.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>';if(a=Ni(r.cellXfs))t[t.length]=a;t[t.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>';t[t.length]='<dxfs count="0"/>';t[t.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>';if(t.length>2){t[t.length]="</styleSheet>";t[1]=t[1].replace("/>",">")}return t.join("")}var Ri=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Li(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(zr)||[]).forEach(function(e){var n=jr(e);switch(n[0]){case"<a:clrScheme":;case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":;case"</a:dk1>":;case"<a:lt1>":;case"</a:lt1>":;case"<a:dk2>":;case"</a:dk2>":;case"<a:lt2>":;case"</a:lt2>":;case"<a:accent1>":;case"</a:accent1>":;case"<a:accent2>":;case"</a:accent2>":;case"<a:accent3>":;case"</a:accent3>":;case"<a:accent4>":;case"</a:accent4>":;case"<a:accent5>":;case"</a:accent5>":;case"<a:accent6>":;case"</a:accent6>":;case"<a:hlink>":;case"</a:hlink>":;case"<a:folHlink>":;case"</a:folHlink>":if(n[0].charAt(1)==="/"){r.themeElements.clrScheme[Ri.indexOf(n[0])]=a;a={}}else{a.name=n[0].slice(3,n[0].length-1)}break;default:if(t&&t.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme");}})}function Bi(){}function $i(){}var zi=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/;var Ui=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/;var Wi=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function ji(e,r,t){r.themeElements={};var a;[["clrScheme",zi,Li],["fontScheme",Ui,Bi],["fmtScheme",Wi,$i]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,r,t)})}var Hi=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Vi(e,r){if(!e||e.length===0)e=Xi();var t;var a={};if(!(t=e.match(Hi)))throw new Error("themeElements not found in theme");ji(t[0],a,r);a.raw=e;return a}function Xi(e,r){if(r&&r.themeXLSX)return r.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var t=[Rr];t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">';t[t.length]="<a:themeElements>";t[t.length]='<a:clrScheme name="Office">';t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>';t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>';t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>';t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>';t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>';t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>';t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>';t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>';t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>';t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>';t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>';t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>';t[t.length]="</a:clrScheme>";t[t.length]='<a:fontScheme name="Office">';t[t.length]="<a:majorFont>";t[t.length]='<a:latin typeface="Cambria"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>';t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:majorFont>";t[t.length]="<a:minorFont>";t[t.length]='<a:latin typeface="Calibri"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Arial"/>';t[t.length]='<a:font script="Hebr" typeface="Arial"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Arial"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:minorFont>";t[t.length]="</a:fontScheme>";t[t.length]='<a:fmtScheme name="Office">';t[t.length]="<a:fillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="1"/>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="0"/>';t[t.length]="</a:gradFill>";t[t.length]="</a:fillStyleLst>";t[t.length]="<a:lnStyleLst>";t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]="</a:lnStyleLst>";t[t.length]="<a:effectStyleLst>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>';t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>';t[t.length]="</a:effectStyle>";t[t.length]="</a:effectStyleLst>";t[t.length]="<a:bgFillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]="</a:bgFillStyleLst>";t[t.length]="</a:fmtScheme>";t[t.length]="</a:themeElements>";t[t.length]="<a:objectDefaults>";t[t.length]="<a:spDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>';
t[t.length]="</a:spDef>";t[t.length]="<a:lnDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>';t[t.length]="</a:lnDef>";t[t.length]="</a:objectDefaults>";t[t.length]="<a:extraClrSchemeLst/>";t[t.length]="</a:theme>";return t.join("")}function Gi(e,r,t){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=false;var i=2;var s;e.replace(zr,function(e){var r=jr(e);switch(Hr(r[0])){case"<?xml":break;case"<metadata":;case"</metadata>":break;case"<metadataTypes":;case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:r.name});break;case"</metadataType>":break;case"<futureMetadata":for(var l=0;l<a.Types.length;++l)if(a.Types[l].name==r.name)s=a.Types[l];break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":if(i==1)a.Cell.push({type:a.Types[r.t-1].name,index:+r.v});else if(i==0)a.Value.push({type:a.Types[r.t-1].name,index:+r.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":n=true;break;case"</ext>":n=false;break;case"<rvb":if(!s)break;if(!s.offsets)s.offsets=[];s.offsets.push(+r.i);break;default:if(!n&&t.WTF)throw new Error("unrecognized "+r[0]+" in metadata");}return e});return a}function Yi(){var e=[Rr];e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>');return e.join("")}function Ji(){}function Ki(e,r,t,a){if(!e)return e;var n=a||{};var i=false,s=false;ha(e,function l(e,r,t){if(s)return;switch(t){case 359:;case 363:;case 364:;case 366:;case 367:;case 368:;case 369:;case 370:;case 371:;case 472:;case 577:;case 578:;case 579:;case 580:;case 581:;case 582:;case 583:;case 584:;case 585:;case 586:;case 587:break;case 35:i=true;break;case 36:i=false;break;default:if(r.T){}else if(!i||n.WTF)throw new Error("Unexpected record 0x"+t.toString(16));}},n)}function qi(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}var Zi=1024;function Qi(e,r){var t=[21600,21600];var a=["m0,0l0",t[1],t[0],t[1],t[0],"0xe"].join(",");var n=[wt("xml",null,{"xmlns:v":At.v,"xmlns:o":At.o,"xmlns:x":At.x,"xmlns:mv":At.mv}).replace(/\/>/,">"),wt("o:shapelayout",wt("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),wt("v:shapetype",[wt("v:stroke",null,{joinstyle:"miter"}),wt("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:t.join(","),path:a})];while(Zi<e*1e3)Zi+=1e3;r.forEach(function(e){var r=Ea(e[0]);var t={color2:"#BEFF82",type:"gradient"};if(t.type=="gradient")t.angle="-180";var a=t.type=="gradient"?wt("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null;var i=wt("v:fill",a,t);var s={on:"t",obscured:"t"};++Zi;n=n.concat(["<v:shape"+bt({id:"_x0000_s"+Zi,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,wt("v:shadow",null,s),wt("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",gt("x:Anchor",[r.c+1,0,r.r+1,0,r.c+3,20,r.r+5,20].join(",")),gt("x:AutoFill","False"),gt("x:Row",String(r.r)),gt("x:Column",String(r.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])});n.push("</xml>");return n.join("")}function es(e,r,t,a){var n=Array.isArray(e);var i;r.forEach(function(r){var s=Ea(r.ref);if(n){if(!e[s.r])e[s.r]=[];i=e[s.r][s.c]}else i=e[r.ref];if(!i){i={t:"z"};if(n)e[s.r][s.c]=i;else e[r.ref]=i;var l=Ma(e["!ref"]||"BDWGO1000001:A1");if(l.s.r>s.r)l.s.r=s.r;if(l.e.r<s.r)l.e.r=s.r;if(l.s.c>s.c)l.s.c=s.c;if(l.e.c<s.c)l.e.c=s.c;var o=Oa(l);if(o!==e["!ref"])e["!ref"]=o}if(!i.c)i.c=[];var c={a:r.author,t:r.t,r:r.r,T:t};if(r.h)c.h=r.h;for(var f=i.c.length-1;f>=0;--f){if(!t&&i.c[f].T)return;if(t&&!i.c[f].T)i.c.splice(f,1)}if(t&&a)for(f=0;f<a.length;++f){if(c.a==a[f].id){c.a=a[f].name||c.a;break}}i.c.push(c)})}function rs(e,r){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var t=[];var a=[];var n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);if(n&&n[1])n[1].split(/<\/\w*:?author>/).forEach(function(e){if(e===""||e.trim()==="")return;var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);if(r)t.push(r[1])});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);if(i&&i[1])i[1].split(/<\/\w*:?comment>/).forEach(function(e){if(e===""||e.trim()==="")return;var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(!n)return;var i=jr(n[0]);var s={author:i.authorId&&t[i.authorId]||"sheetjsghost",ref:i.ref,guid:i.guid};var l=Ea(i.ref);if(r.sheetRows&&r.sheetRows<=l.r)return;var o=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/);var c=!!o&&!!o[1]&&Zn(o[1])||{r:"",t:"",h:""};s.r=c.r;if(c.r=="<t></t>")c.t=c.h="";s.t=(c.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n");if(r.cellHTML)s.h=c.h;a.push(s)});return a}function ts(e){var r=[Rr,wt("comments",null,{xmlns:_t[0]})];var t=[];r.push("<authors>");e.forEach(function(e){e[1].forEach(function(e){var a=Kr(e.a);if(t.indexOf(a)==-1){t.push(a);r.push("<author>"+a+"</author>")}if(e.T&&e.ID&&t.indexOf("tc="+e.ID)==-1){t.push("tc="+e.ID);r.push("<author>"+"tc="+e.ID+"</author>")}})});if(t.length==0){t.push("SheetJ5");r.push("<author>SheetJ5</author>")}r.push("</authors>");r.push("<commentList>");e.forEach(function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID)a=t.indexOf("tc="+e[1][0].ID);else e[1].forEach(function(e){if(e.a)a=t.indexOf(Kr(e.a));n.push(e.t||"")});r.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>');if(n.length<=1)r.push(gt("t",Kr(n[0]||"")));else{var i="Comment:\n    "+n[0]+"\n";for(var s=1;s<n.length;++s)i+="Reply:\n    "+n[s]+"\n";r.push(gt("t",Kr(i)))}r.push("</text></comment>")});r.push("</commentList>");if(r.length>2){r[r.length]="</comments>";r[1]=r[1].replace("/>",">")}return r.join("")}function as(e,r){var t=[];var a=false,n={},i=0;e.replace(zr,function s(l,o){var c=jr(l);switch(Hr(c[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":if(n.t!=null)t.push(n);break;case"<text>":;case"<text":i=o+l.length;break;case"</text>":n.t=e.slice(i,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":;case"<mentions>":a=true;break;case"</mentions>":a=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments");}return l});return t}function ns(e,r,t){var a=[Rr,wt("ThreadedComments",null,{xmlns:Ct.TCMNT}).replace(/[\/]>/,">")];e.forEach(function(e){var n="";(e[1]||[]).forEach(function(i,s){if(!i.T){delete i.ID;return}if(i.a&&r.indexOf(i.a)==-1)r.push(i.a);var l={ref:e[0],id:"{54EE7951-**************-"+("000000000000"+t.tcid++).slice(-12)+"}"};if(s==0)n=l.id;else l.parentId=n;i.ID=l.id;if(i.a)l.personId="{54EE7950-**************-"+("000000000000"+r.indexOf(i.a)).slice(-12)+"}";a.push(wt("threadedComment",gt("text",i.t||""),l))})});a.push("</ThreadedComments>");return a.join("")}function is(e,r){var t=[];var a=false;e.replace(zr,function n(e){var n=jr(e);switch(Hr(n[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":t.push({name:n.displayname,id:n.id});break;case"</person>":break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments");}return e});return t}function ss(e){var r=[Rr,wt("personList",null,{xmlns:Ct.TCMNT,"xmlns:x":_t[0]}).replace(/[\/]>/,">")];e.forEach(function(e,t){r.push(wt("person",null,{displayName:e,id:"{54EE7950-**************-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))});r.push("</personList>");return r.join("")}var ls="application/vnd.ms-office.vbaProject";function os(e){var r=Ye.utils.cfb_new({root:"R"});e.FullPaths.forEach(function(t,a){if(t.slice(-1)==="/"||!t.match(/_VBA_PROJECT_CUR/))return;var n=t.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Ye.utils.cfb_add(r,n,e.FileIndex[a].content)});return Ye.write(r)}function cs(e,r){r.FullPaths.forEach(function(t,a){if(a==0)return;var n=t.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");if(n.slice(-1)!=="/")Ye.utils.cfb_add(e,n,r.FileIndex[a].content)})}var fs=["xlsb","xlsm","xlam","biff8","xla"];function us(){return{"!type":"dialog"}}function hs(){return{"!type":"dialog"}}function ps(){return{"!type":"macro"}}function ds(){return{"!type":"macro"}}var ms=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g;var r={r:0,c:0};function t(e,t,a,n){var i=false,s=false;if(a.length==0)s=true;else if(a.charAt(0)=="["){s=true;a=a.slice(1,-1)}if(n.length==0)i=true;else if(n.charAt(0)=="["){i=true;n=n.slice(1,-1)}var l=a.length>0?parseInt(a,10)|0:0,o=n.length>0?parseInt(n,10)|0:0;if(i)o+=r.c;else--o;if(s)l+=r.r;else--l;return t+(i?"":"$")+Ca(o)+(s?"":"$")+ya(l)}return function a(n,i){r=i;return n.replace(e,t)}}();var vs=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g;var gs=function(){return function e(r,t){return r.replace(vs,function(e,r,a,n,i,s){var l=Sa(n)-(a?0:t.c);var o=wa(s)-(i?0:t.r);var c=o==0?"":!i?"["+o+"]":o+1;var f=l==0?"":!a?"["+l+"]":l+1;return r+"R"+c+"C"+f})}}();function bs(e,r){return e.replace(vs,function(e,t,a,n,i,s){return t+(a=="$"?a+n:Ca(Sa(n)+r.c))+(i=="$"?i+s:ya(wa(s)+r.r))})}function ws(e,r,t){var a=Da(r),n=a.s,i=Ea(t);var s={r:i.r-n.r,c:i.c-n.c};return bs(e,s)}function ys(e){if(e.length==1)return false;return true}function ks(e){return e.replace(/_xlfn\./g,"")}function xs(e){if(e.slice(0,3)=="of:")e=e.slice(3);if(e.charCodeAt(0)==61){e=e.slice(1);if(e.charCodeAt(0)==61)e=e.slice(1)}e=e.replace(/COM\.MICROSOFT\./g,"");e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,r){return r.replace(/\./g,"")});e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1");return e.replace(/[;~]/g,",").replace(/\|/g,";")}function Ss(e){var r="of:="+e.replace(vs,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return r.replace(/;/g,"|").replace(/,/g,";")}function Cs(e){var r=e.split(":");var t=r[0].split(".")[0];return[t,r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}function _s(e){return e.replace(/\./,"!")}var As={};var Ts={};var Es=typeof Map!=="undefined";function Fs(e,r,t){var a=0,n=e.length;if(t){if(Es?t.has(r):Object.prototype.hasOwnProperty.call(t,r)){var i=Es?t.get(r):t[r];for(;a<i.length;++a){if(e[i[a]].t===r){e.Count++;return i[a]}}}}else for(;a<n;++a){if(e[a].t===r){e.Count++;return a}}e[n]={t:r};e.Count++;e.Unique++;if(t){if(Es){if(!t.has(r))t.set(r,[]);t.get(r).push(n)}else{if(!Object.prototype.hasOwnProperty.call(t,r))t[r]=[];t[r].push(n)}}return n}function Ds(e,r){var t={min:e+1,max:e+1};var a=-1;if(r.MDW)pi=r.MDW;if(r.width!=null)t.customWidth=1;else if(r.wpx!=null)a=mi(r.wpx);else if(r.wch!=null)a=r.wch;if(a>-1){t.width=vi(a);t.customWidth=1}else if(r.width!=null)t.width=r.width;if(r.hidden)t.hidden=true;if(r.level!=null){t.outlineLevel=t.level=r.level}return t}function Os(e,r){if(!e)return;var t=[.7,.7,.75,.75,.3,.3];if(r=="xlml")t=[1,1,1,1,.5,.5];if(e.left==null)e.left=t[0];if(e.right==null)e.right=t[1];if(e.top==null)e.top=t[2];if(e.bottom==null)e.bottom=t[3];if(e.header==null)e.header=t[4];if(e.footer==null)e.footer=t[5]}function Ms(e,r,t){var a=t.revssf[r.z!=null?r.z:"General"];var n=60,i=e.length;if(a==null&&t.ssf){for(;n<392;++n)if(t.ssf[n]==null){$e(r.z,n);t.ssf[n]=r.z;t.revssf[r.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1};return i}function Ns(e,r,t,a,n,i){try{if(a.cellNF)e.z=X[r]}catch(s){if(a.WTF)throw s}if(e.t==="z"&&!a.cellStyles)return;if(e.t==="d"&&typeof e.v==="string")e.v=mr(e.v);if((!a||a.cellText!==false)&&e.t!=="z")try{if(X[r]==null)$e(je[r]||"General",r);if(e.t==="e")e.w=e.w||ln[e.v];else if(r===0){if(e.t==="n"){if((e.v|0)===e.v)e.w=e.v.toString(10);else e.w=ie(e.v)}else if(e.t==="d"){var l=sr(e.v);if((l|0)===l)e.w=l.toString(10);else e.w=ie(l)}else if(e.v===undefined)return"";else e.w=se(e.v,Ts)}else if(e.t==="d")e.w=Be(r,sr(e.v),Ts);else e.w=Be(r,e.v,Ts)}catch(s){if(a.WTF)throw s}if(!a.cellStyles)return;if(t!=null)try{e.s=i.Fills[t];if(e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb){e.s.fgColor.rgb=ci(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0);if(a.WTF)e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb}if(e.s.bgColor&&e.s.bgColor.theme){e.s.bgColor.rgb=ci(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0);if(a.WTF)e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb}}catch(s){if(a.WTF&&i.Fills)throw s}}function Ps(e,r,t){if(e&&e["!ref"]){var a=Ma(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+t+"): "+e["!ref"])}}function Is(e,r){var t=Ma(r);if(t.s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0)e["!ref"]=Oa(t)}var Rs=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g;var Ls=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/;var Bs=/<(?:\w:)?hyperlink [^>]*>/gm;var $s=/"(\w*:\w*)"/;var zs=/<(?:\w:)?col\b[^>]*[\/]?>/g;var Us=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g;var Ws=/<(?:\w:)?pageMargins[^>]*\/>/g;var js=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/;var Hs=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/;var Vs=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Xs(e,r,t,a,n,i,s){if(!e)return e;if(!a)a={"!id":{}};if(m!=null&&r.dense==null)r.dense=m;var l=r.dense?[]:{};var o={s:{r:2e6,c:2e6},e:{r:0,c:0}};var c="",f="";var u=e.match(Ls);if(u){c=e.slice(0,u.index);f=e.slice(u.index+u[0].length)}else c=f=e;var h=c.match(js);if(h)Ys(h[0],l,n,t);else if(h=c.match(Hs))Js(h[0],h[1]||"",l,n,t,s,i);var p=(c.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(p>0){var d=c.slice(p,p+50).match($s);if(d)Is(l,d[1])}var v=c.match(Vs);if(v&&v[1])ol(v[1],n);var g=[];if(r.cellStyles){var b=c.match(zs);if(b)al(g,b)}if(u)ul(u[1],l,r,o,i,s);var w=f.match(Us);if(w)l["!autofilter"]=il(w[0]);var y=[];var k=f.match(Rs);if(k)for(p=0;p!=k.length;++p)y[p]=Ma(k[p].slice(k[p].indexOf('"')+1));var x=f.match(Bs);if(x)el(l,x,a);var S=f.match(Ws);if(S)l["!margins"]=rl(jr(S[0]));if(!l["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r)l["!ref"]=Oa(o);if(r.sheetRows>0&&l["!ref"]){var C=Ma(l["!ref"]);if(r.sheetRows<=+C.e.r){C.e.r=r.sheetRows-1;if(C.e.r>o.e.r)C.e.r=o.e.r;if(C.e.r<C.s.r)C.s.r=C.e.r;if(C.e.c>o.e.c)C.e.c=o.e.c;if(C.e.c<C.s.c)C.s.c=C.e.c;l["!fullref"]=l["!ref"];l["!ref"]=Oa(C)}}if(g.length>0)l["!cols"]=g;if(y.length>0)l["!merges"]=y;return l}function Gs(e){if(e.length===0)return"";var r='<mergeCells count="'+e.length+'">';for(var t=0;t!=e.length;++t)r+='<mergeCell ref="'+Oa(e[t])+'"/>';return r+"</mergeCells>"}function Ys(e,r,t,a){var n=jr(e);if(!t.Sheets[a])t.Sheets[a]={};if(n.codeName)t.Sheets[a].CodeName=Gr(ot(n.codeName))}function Js(e,r,t,a,n){Ys(e.slice(0,e.indexOf(">")),t,a,n)}function Ks(e,r,t,a,n){var i=false;var s={},l=null;if(a.bookType!=="xlsx"&&r.vbaraw){var o=r.SheetNames[t];try{if(r.Workbook)o=r.Workbook.Sheets[t].CodeName||o}catch(c){}i=true;s.codeName=ct(Kr(o))}if(e&&e["!outline"]){var f={summaryBelow:1,summaryRight:1};if(e["!outline"].above)f.summaryBelow=0;if(e["!outline"].left)f.summaryRight=0;l=(l||"")+wt("outlinePr",null,f)}if(!i&&!l)return;n[n.length]=wt("sheetPr",l,s)}var qs=["objects","scenarios","selectLockedCells","selectUnlockedCells"];var Zs=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Qs(e){var r={sheet:1};qs.forEach(function(t){if(e[t]!=null&&e[t])r[t]="1"});Zs.forEach(function(t){if(e[t]!=null&&!e[t])r[t]="0"});if(e.password)r.password=crypto_CreatePasswordVerifier_Method1(e.password).toString(16).toUpperCase();return wt("sheetProtection",null,r)}function el(e,r,t){var a=Array.isArray(e);for(var n=0;n!=r.length;++n){var i=jr(ot(r[n]),true);if(!i.ref)return;var s=((t||{})["!id"]||[])[i.id];if(s){i.Target=s.Target;if(i.location)i.Target+="#"+Gr(i.location)}else{i.Target="#"+Gr(i.location);s={Target:i.Target,TargetMode:"Internal"}}i.Rel=s;if(i.tooltip){i.Tooltip=i.tooltip;delete i.tooltip}var l=Ma(i.ref);for(var o=l.s.r;o<=l.e.r;++o)for(var c=l.s.c;c<=l.e.c;++c){var f=Fa({c:c,r:o});if(a){if(!e[o])e[o]=[];if(!e[o][c])e[o][c]={t:"z",v:undefined};e[o][c].l=i}else{if(!e[f])e[f]={t:"z",v:undefined};e[f].l=i}}}}function rl(e){var r={};["left","right","top","bottom","header","footer"].forEach(function(t){if(e[t])r[t]=parseFloat(e[t])});return r}function tl(e){Os(e);return wt("pageMargins",null,e)}function al(e,r){var t=false;for(var a=0;a!=r.length;++a){var n=jr(r[a],true);if(n.hidden)n.hidden=at(n.hidden);var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;if(n.outlineLevel)n.level=+n.outlineLevel||0;delete n.min;delete n.max;n.width=+n.width;if(!t&&n.width){t=true;bi(n.width)}wi(n);while(i<=s)e[i++]=gr(n)}}function nl(e,r){var t=["<cols>"],a;for(var n=0;n!=r.length;++n){if(!(a=r[n]))continue;t[t.length]=wt("col",null,Ds(n,a))}t[t.length]="</cols>";return t.join("")}function il(e){var r={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return r}function sl(e,r,t,a){var n=typeof e.ref=="string"?e.ref:Oa(e.ref);if(!t.Workbook)t.Workbook={Sheets:[]};if(!t.Workbook.Names)t.Workbook.Names=[];var i=t.Workbook.Names;var s=Da(n);if(s.s.r==s.e.r){s.e.r=Da(r["!ref"]).e.r;n=Oa(s)}for(var l=0;l<i.length;++l){var o=i[l];if(o.Name!="_xlnm._FilterDatabase")continue;if(o.Sheet!=a)continue;o.Ref="'"+t.SheetNames[a]+"'!"+n;break}if(l==i.length)i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+n});return wt("autoFilter",null,{ref:n})}var ll=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function ol(e,r){if(!r.Views)r.Views=[{}];(e.match(ll)||[]).forEach(function(e,t){var a=jr(e);if(!r.Views[t])r.Views[t]={};if(+a.zoomScale)r.Views[t].zoom=+a.zoomScale;if(at(a.rightToLeft))r.Views[t].RTL=true})}function cl(e,r,t,a){var n={workbookViewId:"0"};if((((a||{}).Workbook||{}).Views||[])[0])n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0";return wt("sheetViews",wt("sheetView",null,n),{})}function fl(e,r,t,a){if(e.c)t["!comments"].push([r,e.c]);if(e.v===undefined&&typeof e.f!=="string"||e.t==="z"&&!e.f)return"";var n="";var i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=ln[e.v];break;case"d":if(a&&a.cellDates)n=mr(e.v,-1).toISOString();else{e=gr(e);e.t="n";n=""+(e.v=sr(mr(e.v)))}if(typeof e.z==="undefined")e.z=X[14];break;default:n=e.v;break;}var l=gt("v",Kr(n)),o={r:r};var c=Ms(a.cellXfs,e,a);if(c!==0)o.s=c;switch(e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){l=gt("v",""+Fs(a.Strings,e.v,a.revStrings));o.t="s";break}o.t="str";break;}if(e.t!=i){e.t=i;e.v=s}if(typeof e.f=="string"&&e.f){var f=e.F&&e.F.slice(0,r.length)==r?{t:"array",ref:e.F}:null;l=wt("f",Kr(e.f),f)+(e.v!=null?l:"")}if(e.l)t["!links"].push([r,e.l]);if(e.D)o.cm=1;return wt("c",l,o)}var ul=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/;var t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/;var n=/ref=["']([^"']*)["']/;var i=ft("v"),s=ft("f");return function l(o,c,f,u,h,p){var d=0,m="",v=[],g=[],b=0,w=0,y=0,k="",x;var S,C=0,_=0;var A,T;var E=0,F=0;var D=Array.isArray(p.CellXf),O;var M=[];var N=[];var P=Array.isArray(c);var I=[],R={},L=false;var B=!!f.sheetStubs;for(var $=o.split(r),z=0,U=$.length;z!=U;++z){m=$[z].trim();var W=m.length;if(W===0)continue;var j=0;e:for(d=0;d<W;++d)switch(m[d]){case">":if(m[d-1]!="/"){++d;break e}if(f&&f.cellStyles){S=jr(m.slice(j,d),true);C=S.r!=null?parseInt(S.r,10):C+1;_=-1;if(f.sheetRows&&f.sheetRows<C)continue;R={};L=false;if(S.ht){L=true;R.hpt=parseFloat(S.ht);R.hpx=Si(R.hpt)}if(S.hidden=="1"){L=true;R.hidden=true}if(S.outlineLevel!=null){L=true;R.level=+S.outlineLevel}if(L)I[C-1]=R}break;case"<":j=d;break;}if(j>=d)break;S=jr(m.slice(j,d),true);C=S.r!=null?parseInt(S.r,10):C+1;_=-1;if(f.sheetRows&&f.sheetRows<C)continue;if(u.s.r>C-1)u.s.r=C-1;if(u.e.r<C-1)u.e.r=C-1;if(f&&f.cellStyles){R={};L=false;if(S.ht){L=true;R.hpt=parseFloat(S.ht);R.hpx=Si(R.hpt)}if(S.hidden=="1"){L=true;R.hidden=true}if(S.outlineLevel!=null){L=true;R.level=+S.outlineLevel}if(L)I[C-1]=R}v=m.slice(d).split(e);for(var H=0;H!=v.length;++H)if(v[H].trim().charAt(0)!="<")break;v=v.slice(H);for(d=0;d!=v.length;++d){m=v[d].trim();if(m.length===0)continue;g=m.match(t);b=d;w=0;y=0;m="<c "+(m.slice(0,1)=="<"?">":"")+m;if(g!=null&&g.length===2){b=0;k=g[1];for(w=0;w!=k.length;++w){if((y=k.charCodeAt(w)-64)<1||y>26)break;b=26*b+y}--b;_=b}else++_;for(w=0;w!=m.length;++w)if(m.charCodeAt(w)===62)break;++w;S=jr(m.slice(0,w),true);if(!S.r)S.r=Fa({r:C-1,c:_});k=m.slice(w);x={t:""};if((g=k.match(i))!=null&&g[1]!=="")x.v=Gr(g[1]);if(f.cellFormula){if((g=k.match(s))!=null&&g[1]!==""){x.f=Gr(ot(g[1])).replace(/\r\n/g,"\n");if(!f.xlfn)x.f=ks(x.f);if(g[0].indexOf('t="array"')>-1){x.F=(k.match(n)||[])[1];if(x.F.indexOf(":")>-1)M.push([Ma(x.F),x.F])}else if(g[0].indexOf('t="shared"')>-1){T=jr(g[0]);var V=Gr(ot(g[1]));if(!f.xlfn)V=ks(V);N[parseInt(T.si,10)]=[T,V,S.r]}}else if(g=k.match(/<f[^>]*\/>/)){T=jr(g[0]);if(N[T.si])x.f=ws(N[T.si][1],N[T.si][2],S.r)}var G=Ea(S.r);for(w=0;w<M.length;++w)if(G.r>=M[w][0].s.r&&G.r<=M[w][0].e.r)if(G.c>=M[w][0].s.c&&G.c<=M[w][0].e.c)x.F=M[w][1]}if(S.t==null&&x.v===undefined){if(x.f||x.F){x.v=0;x.t="n"}else if(!B)continue;else x.t="z"}else x.t=S.t||"n";if(u.s.c>_)u.s.c=_;if(u.e.c<_)u.e.c=_;switch(x.t){case"n":if(x.v==""||x.v==null){if(!B)continue;x.t="z"}else x.v=parseFloat(x.v);break;case"s":if(typeof x.v=="undefined"){if(!B)continue;x.t="z"}else{A=As[parseInt(x.v,10)];x.v=A.t;x.r=A.r;if(f.cellHTML)x.h=A.h}break;case"str":x.t="s";x.v=x.v!=null?ot(x.v):"";if(f.cellHTML)x.h=Qr(x.v);break;case"inlineStr":g=k.match(a);x.t="s";if(g!=null&&(A=Zn(g[1]))){x.v=A.t;if(f.cellHTML)x.h=A.h}else x.v="";break;case"b":x.v=at(x.v);break;case"d":if(f.cellDates)x.v=mr(x.v,1);else{x.v=sr(mr(x.v,1));x.t="n"}break;case"e":if(!f||f.cellText!==false)x.w=x.v;x.v=on[x.v];break;}E=F=0;O=null;if(D&&S.s!==undefined){O=p.CellXf[S.s];if(O!=null){if(O.numFmtId!=null)E=O.numFmtId;if(f.cellStyles){if(O.fillId!=null)F=O.fillId}}}Ns(x,E,F,f,h,p);if(f.cellDates&&D&&x.t=="n"&&Ne(X[E])){x.t="d";x.v=fr(x.v)}if(S.cm&&f.xlmeta){var Y=(f.xlmeta.Cell||[])[+S.cm-1];if(Y&&Y.type=="XLDAPR")x.D=true}if(P){var J=Ea(S.r);if(!c[J.r])c[J.r]=[];c[J.r][J.c]=x}else c[S.r]=x}}if(I.length>0)c["!rows"]=I}}();function hl(e,r,t,a){var n=[],i=[],s=Ma(e["!ref"]),l="",o,c="",f=[],u=0,h=0,p=e["!rows"];var d=Array.isArray(e);var m={r:c},v,g=-1;for(h=s.s.c;h<=s.e.c;++h)f[h]=Ca(h);for(u=s.s.r;u<=s.e.r;++u){i=[];c=ya(u);for(h=s.s.c;h<=s.e.c;++h){o=f[h]+c;var b=d?(e[u]||[])[h]:e[o];if(b===undefined)continue;if((l=fl(b,o,e,r,t,a))!=null)i.push(l)}if(i.length>0||p&&p[u]){m={r:c};if(p&&p[u]){v=p[u];if(v.hidden)m.hidden=1;g=-1;if(v.hpx)g=xi(v.hpx);else if(v.hpt)g=v.hpt;if(g>-1){m.ht=g;m.customHeight=1}if(v.level){m.outlineLevel=v.level}}n[n.length]=wt("row",i.join(""),m)}}if(p)for(;u<p.length;++u){if(p&&p[u]){m={r:u+1};v=p[u];if(v.hidden)m.hidden=1;g=-1;if(v.hpx)g=xi(v.hpx);else if(v.hpt)g=v.hpt;if(g>-1){m.ht=g;m.customHeight=1}if(v.level){m.outlineLevel=v.level}n[n.length]=wt("row","",m)}}return n.join("")}function pl(e,r,t,a){var n=[Rr,wt("worksheet",null,{xmlns:_t[0],"xmlns:r":Ct.r})];var i=t.SheetNames[e],s=0,l="";var o=t.Sheets[i];if(o==null)o={};var c=o["!ref"]||"A1";var f=Ma(c);if(f.e.c>16383||f.e.r>1048575){if(r.WTF)throw new Error("Range "+c+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383);f.e.r=Math.min(f.e.c,1048575);c=Oa(f)}if(!a)a={};o["!comments"]=[];var u=[];Ks(o,t,e,r,n);n[n.length]=wt("dimension",null,{ref:c});n[n.length]=cl(o,r,e,t);if(r.sheetFormat)n[n.length]=wt("sheetFormatPr",null,{defaultRowHeight:r.sheetFormat.defaultRowHeight||"16",baseColWidth:r.sheetFormat.baseColWidth||"10",outlineLevelRow:r.sheetFormat.outlineLevelRow||"7"});if(o["!cols"]!=null&&o["!cols"].length>0)n[n.length]=nl(o,o["!cols"]);n[s=n.length]="<sheetData/>";o["!links"]=[];if(o["!ref"]!=null){l=hl(o,r,e,t,a);if(l.length>0)n[n.length]=l}if(n.length>s+1){n[n.length]="</sheetData>";n[s]=n[s].replace("/>",">")}if(o["!protect"])n[n.length]=Qs(o["!protect"]);if(o["!autofilter"]!=null)n[n.length]=sl(o["!autofilter"],o,t,e);if(o["!merges"]!=null&&o["!merges"].length>0)n[n.length]=Gs(o["!merges"]);var h=-1,p,d=-1;if(o["!links"].length>0){n[n.length]="<hyperlinks>";o["!links"].forEach(function(e){if(!e[1].Target)return;p={ref:e[0]};if(e[1].Target.charAt(0)!="#"){d=bn(a,-1,Kr(e[1].Target).replace(/#.*$/,""),dn.HLINK);p["r:id"]="rId"+d}if((h=e[1].Target.indexOf("#"))>-1)p.location=Kr(e[1].Target.slice(h+1));if(e[1].Tooltip)p.tooltip=Kr(e[1].Tooltip);n[n.length]=wt("hyperlink",null,p)});n[n.length]="</hyperlinks>"}delete o["!links"];if(o["!margins"]!=null)n[n.length]=tl(o["!margins"]);if(!r||r.ignoreEC||r.ignoreEC==void 0)n[n.length]=gt("ignoredErrors",wt("ignoredError",null,{numberStoredAsText:1,sqref:c}));if(u.length>0){d=bn(a,-1,"../drawings/drawing"+(e+1)+".xml",dn.DRAW);n[n.length]=wt("drawing",null,{"r:id":"rId"+d});o["!drawing"]=u}if(o["!comments"].length>0){d=bn(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",dn.VML);n[n.length]=wt("legacyDrawing",null,{"r:id":"rId"+d});o["!legacy"]=d}if(n.length>1){n[n.length]="</worksheet>";n[1]=n[1].replace("/>",">")}return n.join("")}function dl(e){var r=[];var t=e.match(/^<c:numCache>/);var a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach(function(e){var a=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);if(!a)return;r[+a[1]]=t?+a[2]:a[2]});var n=Gr((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach(function(e){a=e.replace(/<.*?>/g,"")});return[r,n,a]}function ml(e,r,t,a,n,i){var s=i||{"!type":"chart"};if(!e)return i;var l=0,o=0,c="A";var f={s:{r:2e6,c:2e6},e:{r:0,c:0}};(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var r=dl(e);f.s.r=f.s.c=0;f.e.c=l;c=Ca(l);r[0].forEach(function(e,t){s[c+ya(t)]={t:"n",v:e,z:r[1]};o=t});if(f.e.r<o)f.e.r=o;++l});if(l>0)s["!ref"]=Oa(f);return s}dn.CS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet";function vl(e,r,t,a,n){if(!e)return e;if(!a)a={"!id":{}};var i={"!type":"chart","!drawel":null,"!rel":""};var s;var l=e.match(js);if(l)Ys(l[0],i,n,t);if(s=e.match(/drawing r:id="(.*?)"/))i["!rel"]=s[1];if(a["!id"][i["!rel"]])i["!drawel"]=a["!id"][i["!rel"]];return i}function gl(e,r,t,a){var n=[Rr,wt("chartsheet",null,{xmlns:_t[0],"xmlns:r":Ct.r})];n[n.length]=wt("drawing",null,{"r:id":"rId1"});bn(a,-1,"../drawings/drawing"+(e+1)+".xml",dn.DRAW);if(n.length>2){n[n.length]="</chartsheet>";n[1]=n[1].replace("/>",">")}return n.join("")}var bl=[["allowRefreshQuery",false,"bool"],["autoCompressPictures",true,"bool"],["backupFile",false,"bool"],["checkCompatibility",false,"bool"],["CodeName",""],["date1904",false,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",false,"bool"],["hidePivotFieldList",false,"bool"],["promptedSolutions",false,"bool"],["publishItems",false,"bool"],["refreshAllConnections",false,"bool"],["saveExternalLinkValues",true,"bool"],["showBorderUnselectedTables",true,"bool"],["showInkAnnotation",true,"bool"],["showObjects","all"],["showPivotChartFilter",false,"bool"],["updateLinks","userSet"]];var wl=[["activeTab",0,"int"],["autoFilterDateGrouping",true,"bool"],["firstSheet",0,"int"],["minimized",false,"bool"],["showHorizontalScroll",true,"bool"],["showSheetTabs",true,"bool"],["showVerticalScroll",true,"bool"],["tabRatio",600,"int"],["visibility","visible"]];var yl=[];var kl=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function xl(e,r){for(var t=0;t!=e.length;++t){var a=e[t];for(var n=0;n!=r.length;++n){var i=r[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":if(typeof a[i[0]]=="string")a[i[0]]=at(a[i[0]]);break;case"int":if(typeof a[i[0]]=="string")a[i[0]]=parseInt(a[i[0]],10);break;}}}}function Sl(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":if(typeof e[a[0]]=="string")e[a[0]]=at(e[a[0]]);break;case"int":if(typeof e[a[0]]=="string")e[a[0]]=parseInt(e[a[0]],10);break;}}}function Cl(e){Sl(e.WBProps,bl);Sl(e.CalcPr,kl);xl(e.WBView,wl);xl(e.Sheets,yl);Ts.date1904=at(e.WBProps.date1904)}function _l(e){if(!e.Workbook)return"false";if(!e.Workbook.WBProps)return"false";return at(e.Workbook.WBProps.date1904)?"true":"false"}var Al="][*?/\\".split("");function Tl(e,r){if(e.length>31){if(r)return false;throw new Error("Sheet names cannot exceed 31 chars")}var t=true;Al.forEach(function(a){if(e.indexOf(a)==-1)return;if(!r)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");t=false});return t}function El(e,r,t){e.forEach(function(a,n){Tl(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(t){var s=r&&r[n]&&r[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function Fl(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var r=e.Workbook&&e.Workbook.Sheets||[];El(e.SheetNames,r,!!e.vbaraw);for(var t=0;t<e.SheetNames.length;++t)Ps(e.Sheets[e.SheetNames[t]],e.SheetNames[t],t)}var Dl=/<\w+:workbook/;function Ol(e,r){if(!e)throw new Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""};var a=false,n="xmlns";var i={},s=0;e.replace(zr,function l(o,c){var f=jr(o);switch(Hr(f[0])){case"<?xml":break;case"<workbook":if(o.match(Dl))n="xmlns"+o.match(/<(\w+):/)[1];t.xmlns=f[n];break;case"</workbook>":break;case"<fileVersion":delete f[0];t.AppVersion=f;break;case"<fileVersion/>":;case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":;case"<workbookPr/>":bl.forEach(function(e){if(f[e[0]]==null)return;switch(e[2]){case"bool":t.WBProps[e[0]]=at(f[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(f[e[0]],10);break;default:t.WBProps[e[0]]=f[e[0]];}});if(f.codeName)t.WBProps.CodeName=ot(f.codeName);break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":
break;case"<bookViews":;case"<bookViews>":;case"</bookViews>":break;case"<workbookView":;case"<workbookView/>":delete f[0];t.WBView.push(f);break;case"</workbookView>":break;case"<sheets":;case"<sheets>":;case"</sheets>":break;case"<sheet":switch(f.state){case"hidden":f.Hidden=1;break;case"veryHidden":f.Hidden=2;break;default:f.Hidden=0;}delete f.state;f.name=Gr(ot(f.name));delete f[0];t.Sheets.push(f);break;case"</sheet>":break;case"<functionGroups":;case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":;case"</externalReferences>":;case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":;case"<definedNames":a=true;break;case"</definedNames>":a=false;break;case"<definedName":{i={};i.Name=ot(f.name);if(f.comment)i.Comment=f.comment;if(f.localSheetId)i.Sheet=+f.localSheetId;if(at(f.hidden||"0"))i.Hidden=true;s=c+o.length}break;case"</definedName>":{i.Ref=Gr(ot(e.slice(s,c)));t.Names.push(i)}break;case"<definedName/>":break;case"<calcPr":delete f[0];t.CalcPr=f;break;case"<calcPr/>":delete f[0];t.CalcPr=f;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":;case"</customWorkbookViews>":;case"<customWorkbookViews":break;case"<customWorkbookView":;case"</customWorkbookView>":break;case"<pivotCaches>":;case"</pivotCaches>":;case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":;case"<smartTagPr/>":break;case"<smartTagTypes":;case"<smartTagTypes>":;case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":;case"<webPublishing/>":break;case"<fileRecoveryPr":;case"<fileRecoveryPr/>":break;case"<webPublishObjects>":;case"<webPublishObjects":;case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;case"<ArchID":break;case"<AlternateContent":;case"<AlternateContent>":a=true;break;case"</AlternateContent>":a=false;break;case"<revisionPtr":break;default:if(!a&&r.WTF)throw new Error("unrecognized "+f[0]+" in workbook");}return o});if(_t.indexOf(t.xmlns)===-1)throw new Error("Unknown Namespace: "+t.xmlns);Cl(t);return t}function Ml(e){var r=[Rr];r[r.length]=wt("workbook",null,{xmlns:_t[0],"xmlns:r":Ct.r});var t=e.Workbook&&(e.Workbook.Names||[]).length>0;var a={codeName:"ThisWorkbook"};if(e.Workbook&&e.Workbook.WBProps){bl.forEach(function(r){if(e.Workbook.WBProps[r[0]]==null)return;if(e.Workbook.WBProps[r[0]]==r[1])return;a[r[0]]=e.Workbook.WBProps[r[0]]});if(e.Workbook.WBProps.CodeName){a.codeName=e.Workbook.WBProps.CodeName;delete a.CodeName}}r[r.length]=wt("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[];var i=0;if(n&&n[0]&&!!n[0].Hidden){r[r.length]="<bookViews>";for(i=0;i!=e.SheetNames.length;++i){if(!n[i])break;if(!n[i].Hidden)break}if(i==e.SheetNames.length)i=0;r[r.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>';r[r.length]="</bookViews>"}r[r.length]="<sheets>";for(i=0;i!=e.SheetNames.length;++i){var s={name:Kr(e.SheetNames[i].slice(0,31))};s.sheetId=""+(i+1);s["r:id"]="rId"+(i+1);if(n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break;}r[r.length]=wt("sheet",null,s)}r[r.length]="</sheets>";if(t){r[r.length]="<definedNames>";if(e.Workbook&&e.Workbook.Names)e.Workbook.Names.forEach(function(e){var t={name:e.Name};if(e.Comment)t.comment=e.Comment;if(e.Sheet!=null)t.localSheetId=""+e.Sheet;if(e.Hidden)t.hidden="1";if(!e.Ref)return;r[r.length]=wt("definedName",Kr(e.Ref),t)});r[r.length]="</definedNames>"}if(r.length>2){r[r.length]="</workbook>";r[1]=r[1].replace("/>",">")}return r.join("")}function Nl(e,r,t){if(r.slice(-4)===".bin")return parse_wb_bin(e,t);return Ol(e,t)}function Pl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return parse_ws_bin(e,a,t,n,i,s,l);return Xs(e,a,t,n,i,s,l)}function Il(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return parse_cs_bin(e,a,t,n,i,s,l);return vl(e,a,t,n,i,s,l)}function Rl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return ps(e,a,t,n,i,s,l);return ds(e,a,t,n,i,s,l)}function Ll(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return us(e,a,t,n,i,s,l);return hs(e,a,t,n,i,s,l)}function Bl(e,r,t,a){if(r.slice(-4)===".bin")return parse_sty_bin(e,t,a);return Pi(e,t,a)}function $l(e,r,t){return Vi(e,t)}function zl(e,r,t){if(r.slice(-4)===".bin")return parse_sst_bin(e,t);return ti(e,t)}function Ul(e,r,t){if(r.slice(-4)===".bin")return parse_comments_bin(e,t);return rs(e,t)}function Wl(e,r,t){if(r.slice(-4)===".bin")return parse_cc_bin(e,r,t);return parse_cc_xml(e,r,t)}function jl(e,r,t,a){if(t.slice(-4)===".bin")return Ki(e,r,t,a);return Ji(e,r,t,a)}function Hl(e,r,t){if(r.slice(-4)===".bin")return parse_xlmeta_bin(e,r,t);return Gi(e,r,t)}function Vl(e,r,t){return(r.slice(-4)===".bin"?write_wb_bin:Ml)(e,t)}function Xl(e,r,t,a,n){return(r.slice(-4)===".bin"?write_ws_bin:pl)(e,t,a,n)}function Gl(e,r,t,a,n){return(r.slice(-4)===".bin"?write_cs_bin:gl)(e,t,a,n)}function Yl(e,r,t){return(r.slice(-4)===".bin"?write_sty_bin:Ii)(e,t)}function Jl(e,r,t){return(r.slice(-4)===".bin"?write_sst_bin:ni)(e,t)}function Kl(e,r,t){return(r.slice(-4)===".bin"?write_comments_bin:ts)(e,t)}function ql(e){return(e.slice(-4)===".bin"?write_xlmeta_bin:Yi)()}function Zl(e,r){var t=r||{};if(m!=null&&t.dense==null)t.dense=m;var a=t.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i);var s=n.index,l=i&&i.index||e.length;var o=xr(e.slice(s,l),/(:?<tr[^>]*>)/i,"<tr>");var c=-1,f=0,u=0,h=0;var p={s:{r:1e7,c:1e7},e:{r:0,c:0}};var d=[];for(s=0;s<o.length;++s){var v=o[s].trim();var g=v.slice(0,3).toLowerCase();if(g=="<tr"){++c;if(t.sheetRows&&t.sheetRows<=c){--c;break}f=0;continue}if(g!="<td"&&g!="<th")continue;var b=v.split(/<\/t[dh]>/i);for(l=0;l<b.length;++l){var w=b[l].trim();if(!w.match(/<t[dh]/i))continue;var y=w,k=0;while(y.charAt(0)=="<"&&(k=y.indexOf(">"))>-1)y=y.slice(k+1);for(var x=0;x<d.length;++x){var S=d[x];if(S.s.c==f&&S.s.r<c&&c<=S.e.r){f=S.e.c+1;x=-1}}var C=jr(w.slice(0,w.indexOf(">")));h=C.colspan?+C.colspan:1;if((u=+C.rowspan)>1||h>1)d.push({s:{r:c,c:f},e:{r:c+(u||1)-1,c:f+h-1}});var _=C.t||C["data-t"]||"";if(!y.length){f+=h;continue}y=ut(y);if(p.s.r>c)p.s.r=c;if(p.e.r<c)p.e.r=c;if(p.s.c>f)p.s.c=f;if(p.e.c<f)p.e.c=f;if(!y.length){f+=h;continue}var A={t:"s",v:y};if(t.raw||!y.trim().length||_=="s"){}else if(y==="TRUE")A={t:"b",v:true};else if(y==="FALSE")A={t:"b",v:false};else if(!isNaN(wr(y)))A={t:"n",v:wr(y)};else if(!isNaN(kr(y).getDate())){A={t:"d",v:mr(y)};if(!t.cellDates)A={t:"n",v:sr(A.v)};A.z=t.dateNF||X[14]}if(t.dense){if(!a[c])a[c]=[];a[c][f]=A}else a[Fa({r:c,c:f})]=A;f+=h}}a["!ref"]=Oa(p);if(d.length)a["!merges"]=d;return a}function Ql(e,r,t,a){var n=e["!merges"]||[];var i=[];for(var s=r.s.c;s<=r.e.c;++s){var l=0,o=0;for(var c=0;c<n.length;++c){if(n[c].s.r>t||n[c].s.c>s)continue;if(n[c].e.r<t||n[c].e.c<s)continue;if(n[c].s.r<t||n[c].s.c<s){l=-1;break}l=n[c].e.r-n[c].s.r+1;o=n[c].e.c-n[c].s.c+1;break}if(l<0)continue;var f=Fa({r:t,c:s});var u=a.dense?(e[t]||[])[s]:e[f];var h=u&&u.v!=null&&(u.h||Qr(u.w||(Pa(u),u.w)||""))||"";var p={};if(l>1)p.rowspan=l;if(o>1)p.colspan=o;if(a.editable)h='<span contenteditable="true">'+h+"</span>";else if(u){p["data-t"]=u&&u.t||"z";if(u.v!=null)p["data-v"]=u.v;if(u.z!=null)p["data-z"]=u.z;if(u.l&&(u.l.Target||"#").charAt(0)!="#")h='<a href="'+u.l.Target+'">'+h+"</a>"}p.id=(a.id||"sjs")+"-"+f;i.push(wt("td",h,p))}var d="<tr>";return d+i.join("")+"</tr>"}var eo='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>';var ro="</body></html>";function to(e,r){var t=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!t||t.length==0)throw new Error("Invalid HTML: could not find <table>");if(t.length==1)return Ia(Zl(t[0],r),r);var a=hc();t.forEach(function(e,t){pc(a,Zl(e,r),"Sheet"+(t+1))});return a}function ao(e,r,t){var a=[];return a.join("")+"<table"+(t&&t.id?' id="'+t.id+'"':"")+">"}function no(e,r){var t=r||{};var a=t.header!=null?t.header:eo;var n=t.footer!=null?t.footer:ro;var i=[a];var s=Da(e["!ref"]);t.dense=Array.isArray(e);i.push(ao(e,s,t));for(var l=s.s.r;l<=s.e.r;++l)i.push(Ql(e,s,l,t));i.push("</table>"+n);return i.join("")}function io(e,r,t){var a=t||{};if(m!=null)a.dense=m;var n=0,i=0;if(a.origin!=null){if(typeof a.origin=="number")n=a.origin;else{var s=typeof a.origin=="string"?Ea(a.origin):a.origin;n=s.r;i=s.c}}var l=r.getElementsByTagName("tr");var o=Math.min(a.sheetRows||1e7,l.length);var c={s:{r:0,c:0},e:{r:n,c:i}};if(e["!ref"]){var f=Da(e["!ref"]);c.s.r=Math.min(c.s.r,f.s.r);c.s.c=Math.min(c.s.c,f.s.c);c.e.r=Math.max(c.e.r,f.e.r);c.e.c=Math.max(c.e.c,f.e.c);if(n==-1)c.e.r=n=f.e.r+1}var u=[],h=0;var p=e["!rows"]||(e["!rows"]=[]);var d=0,v=0,g=0,b=0,w=0,y=0;if(!e["!cols"])e["!cols"]=[];for(;d<l.length&&v<o;++d){var k=l[d];if(oo(k)){if(a.display)continue;p[v]={hidden:true}}var x=k.children;for(g=b=0;g<x.length;++g){var S=x[g];if(a.display&&oo(S))continue;var C=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):ut(S.innerHTML);var _=S.getAttribute("data-z")||S.getAttribute("z");for(h=0;h<u.length;++h){var A=u[h];if(A.s.c==b+i&&A.s.r<v+n&&v+n<=A.e.r){b=A.e.c+1-i;h=-1}}y=+S.getAttribute("colspan")||1;if((w=+S.getAttribute("rowspan")||1)>1||y>1)u.push({s:{r:v+n,c:b+i},e:{r:v+n+(w||1)-1,c:b+i+(y||1)-1}});var T={t:"s",v:C};var E=S.getAttribute("data-t")||S.getAttribute("t")||"";if(C!=null){if(C.length==0)T.t=E||"z";else if(a.raw||C.trim().length==0||E=="s"){}else if(C==="TRUE")T={t:"b",v:true};else if(C==="FALSE")T={t:"b",v:false};else if(!isNaN(wr(C)))T={t:"n",v:wr(C)};else if(!isNaN(kr(C).getDate())){T={t:"d",v:mr(C)};if(!a.cellDates)T={t:"n",v:sr(T.v)};T.z=a.dateNF||X[14]}}if(T.z===undefined&&_!=null)T.z=_;var F="",D=S.getElementsByTagName("A");if(D&&D.length)for(var O=0;O<D.length;++O)if(D[O].hasAttribute("href")){F=D[O].getAttribute("href");if(F.charAt(0)!="#")break}if(F&&F.charAt(0)!="#")T.l={Target:F};if(a.dense){if(!e[v+n])e[v+n]=[];e[v+n][b+i]=T}else e[Fa({c:b+i,r:v+n})]=T;if(c.e.c<b+i)c.e.c=b+i;b+=y}++v}if(u.length)e["!merges"]=(e["!merges"]||[]).concat(u);c.e.r=Math.max(c.e.r,v-1+n);e["!ref"]=Oa(c);if(v>=o)e["!fullref"]=Oa((c.e.r=l.length-d+v-1+n,c));return e}function so(e,r){var t=r||{};var a=t.dense?[]:{};return io(a,e,r)}function lo(e,r){return Ia(so(e,r),r)}function oo(e){var r="";var t=co(e);if(t)r=t(e).getPropertyValue("display");if(!r)r=e.style&&e.style.display;return r==="none"}function co(e){if(e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle==="function")return e.ownerDocument.defaultView.getComputedStyle;if(typeof getComputedStyle==="function")return getComputedStyle;return null}function fo(e){var r=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,r){return Array(parseInt(r,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n");var t=Gr(r.replace(/<[^>]*>/g,""));return[t]}var uo={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function ho(e,r){var t=r||{};if(m!=null&&t.dense==null)t.dense=m;var a=xt(e);var n=[],i;var s;var l={name:""},o="",c=0;var f;var u;var h={},p=[];var d=t.dense?[]:{};var v,g;var b={value:""};var w="",y=0,k;var x=[];var S=-1,C=-1,_={s:{r:1e6,c:1e7},e:{r:0,c:0}};var A=0;var T={};var E=[],F={},D=0,O=0;var M=[],N=1,P=1;var I=[];var R={Names:[]};var L={};var B=["",""];var $=[],z={};var U="",W=0;var j=false,H=false;var V=0;St.lastIndex=0;a=a.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");while(v=St.exec(a))switch(v[3]=v[3].replace(/_.*$/,"")){case"table":;case"工作表":if(v[1]==="/"){if(_.e.c>=_.s.c&&_.e.r>=_.s.r)d["!ref"]=Oa(_);else d["!ref"]="A1:A1";if(t.sheetRows>0&&t.sheetRows<=_.e.r){d["!fullref"]=d["!ref"];_.e.r=t.sheetRows-1;d["!ref"]=Oa(_)}if(E.length)d["!merges"]=E;if(M.length)d["!rows"]=M;f.name=f["名称"]||f.name;if(typeof JSON!=="undefined")JSON.stringify(f);p.push(f.name);h[f.name]=d;H=false}else if(v[0].charAt(v[0].length-2)!=="/"){f=jr(v[0],false);S=C=-1;_.s.r=_.s.c=1e7;_.e.r=_.e.c=0;d=t.dense?[]:{};E=[];M=[];H=true}break;case"table-row-group":if(v[1]==="/")--A;else++A;break;case"table-row":;case"行":if(v[1]==="/"){S+=N;N=1;break}u=jr(v[0],false);if(u["行号"])S=u["行号"]-1;else if(S==-1)S=0;N=+u["number-rows-repeated"]||1;if(N<10)for(V=0;V<N;++V)if(A>0)M[S+V]={level:A};C=-1;break;case"covered-table-cell":if(v[1]!=="/")++C;if(t.sheetStubs){if(t.dense){if(!d[S])d[S]=[];d[S][C]={t:"z"}}else d[Fa({r:S,c:C})]={t:"z"}}w="";x=[];break;case"table-cell":;case"数据":if(v[0].charAt(v[0].length-2)==="/"){++C;b=jr(v[0],false);P=parseInt(b["number-columns-repeated"]||"1",10);g={t:"z",v:null};if(b.formula&&t.cellFormula!=false)g.f=xs(Gr(b.formula));if((b["数据类型"]||b["value-type"])=="string"){g.t="s";g.v=Gr(b["string-value"]||"");if(t.dense){if(!d[S])d[S]=[];d[S][C]=g}else{d[Fa({r:S,c:C})]=g}}C+=P-1}else if(v[1]!=="/"){++C;w="";y=0;x=[];P=1;var X=N?S+N-1:S;if(C>_.e.c)_.e.c=C;if(C<_.s.c)_.s.c=C;if(S<_.s.r)_.s.r=S;if(X>_.e.r)_.e.r=X;b=jr(v[0],false);$=[];z={};g={t:b["数据类型"]||b["value-type"],v:null};if(t.cellFormula){if(b.formula)b.formula=Gr(b.formula);if(b["number-matrix-columns-spanned"]&&b["number-matrix-rows-spanned"]){D=parseInt(b["number-matrix-rows-spanned"],10)||0;O=parseInt(b["number-matrix-columns-spanned"],10)||0;F={s:{r:S,c:C},e:{r:S+D-1,c:C+O-1}};g.F=Oa(F);I.push([F,g.F])}if(b.formula)g.f=xs(b.formula);else for(V=0;V<I.length;++V)if(S>=I[V][0].s.r&&S<=I[V][0].e.r)if(C>=I[V][0].s.c&&C<=I[V][0].e.c)g.F=I[V][1]}if(b["number-columns-spanned"]||b["number-rows-spanned"]){D=parseInt(b["number-rows-spanned"],10)||0;O=parseInt(b["number-columns-spanned"],10)||0;F={s:{r:S,c:C},e:{r:S+D-1,c:C+O-1}};E.push(F)}if(b["number-columns-repeated"])P=parseInt(b["number-columns-repeated"],10);switch(g.t){case"boolean":g.t="b";g.v=at(b["boolean-value"]);break;case"float":g.t="n";g.v=parseFloat(b.value);break;case"percentage":g.t="n";g.v=parseFloat(b.value);break;case"currency":g.t="n";g.v=parseFloat(b.value);break;case"date":g.t="d";g.v=mr(b["date-value"]);if(!t.cellDates){g.t="n";g.v=sr(g.v)}g.z="m/d/yy";break;case"time":g.t="n";g.v=ur(b["time-value"])/86400;if(t.cellDates){g.t="d";g.v=fr(g.v)}g.z="HH:MM:SS";break;case"number":g.t="n";g.v=parseFloat(b["数据数值"]);break;default:if(g.t==="string"||g.t==="text"||!g.t){g.t="s";if(b["string-value"]!=null){w=Gr(b["string-value"]);x=[]}}else throw new Error("Unsupported value type "+g.t);}}else{j=false;if(g.t==="s"){g.v=w||"";if(x.length)g.R=x;j=y==0}if(L.Target)g.l=L;if($.length>0){g.c=$;$=[]}if(w&&t.cellText!==false)g.w=w;if(j){g.t="z";delete g.v}if(!j||t.sheetStubs){if(!(t.sheetRows&&t.sheetRows<=S)){for(var G=0;G<N;++G){P=parseInt(b["number-columns-repeated"]||"1",10);if(t.dense){if(!d[S+G])d[S+G]=[];d[S+G][C]=G==0?g:gr(g);while(--P>0)d[S+G][C+P]=gr(g)}else{d[Fa({r:S+G,c:C})]=g;while(--P>0)d[Fa({r:S+G,c:C+P})]=gr(g)}if(_.e.c<=C)_.e.c=C}}}P=parseInt(b["number-columns-repeated"]||"1",10);C+=P-1;P=0;g={};w="";x=[]}L={};break;case"document":;case"document-content":;case"电子表格文档":;case"spreadsheet":;case"主体":;case"scripts":;case"styles":;case"font-face-decls":;case"master-styles":if(v[1]==="/"){if((i=n.pop())[0]!==v[3])throw"Bad state: "+i}else if(v[0].charAt(v[0].length-2)!=="/")n.push([v[3],true]);break;case"annotation":if(v[1]==="/"){if((i=n.pop())[0]!==v[3])throw"Bad state: "+i;z.t=w;if(x.length)z.R=x;z.a=U;$.push(z)}else if(v[0].charAt(v[0].length-2)!=="/"){n.push([v[3],false])}U="";W=0;w="";y=0;x=[];break;case"creator":if(v[1]==="/"){U=a.slice(W,v.index)}else W=v.index+v[0].length;break;case"meta":;case"元数据":;case"settings":;case"config-item-set":;case"config-item-map-indexed":;case"config-item-map-entry":;case"config-item-map-named":;case"shapes":;case"frame":;case"text-box":;case"image":;case"data-pilot-tables":;case"list-style":;case"form":;case"dde-links":;case"event-listeners":;case"chart":if(v[1]==="/"){if((i=n.pop())[0]!==v[3])throw"Bad state: "+i}else if(v[0].charAt(v[0].length-2)!=="/")n.push([v[3],false]);w="";y=0;x=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":;case"percentage-style":;case"date-style":;case"time-style":if(v[1]==="/"){T[l.name]=o;if((i=n.pop())[0]!==v[3])throw"Bad state: "+i}else if(v[0].charAt(v[0].length-2)!=="/"){o="";l=jr(v[0],false);n.push([v[3],true])}break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":;case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":;case"date-style":s=jr(v[0],false);o+=uo[v[3]][s.style==="long"?1:0];break;}break;case"fraction":break;case"day":;case"month":;case"year":;case"era":;case"day-of-week":;case"week-of-year":;case"quarter":;case"hours":;case"minutes":;case"seconds":;case"am-pm":switch(n[n.length-1][0]){case"time-style":;case"date-style":s=jr(v[0],false);o+=uo[v[3]][s.style==="long"?1:0];break;}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(v[0].slice(-2)==="/>")break;else if(v[1]==="/")switch(n[n.length-1][0]){case"number-style":;case"date-style":;case"time-style":o+=a.slice(c,v.index);break;}else c=v.index+v[0].length;break;case"named-range":s=jr(v[0],false);B=Cs(s["cell-range-address"]);var Y={Name:s.name,Ref:B[0]+"!"+B[1]};if(H)Y.Sheet=p.length;R.Names.push(Y);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":;case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":;case"文本串":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(v[1]==="/"&&(!b||!b["string-value"])){var J=fo(a.slice(y,v.index),k);w=(w.length>0?w+"\n":"")+J[0]}else{k=jr(v[0],false);y=v.index+v[0].length}break;case"s":break;case"database-range":if(v[1]==="/")break;try{B=Cs(jr(v[0])["target-range-address"]);h[B[0]]["!autofilter"]={ref:B[1]}}catch(K){}break;case"date":break;case"object":break;case"title":;case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":;case"sender-lastname":;case"sender-initials":;case"sender-title":;case"sender-position":;case"sender-email":;case"sender-phone-private":;case"sender-fax":;case"sender-company":;case"sender-phone-work":;case"sender-street":;case"sender-city":;case"sender-postal-code":;case"sender-country":;case"sender-state-or-province":;case"author-name":;case"author-initials":;case"chapter":;case"file-name":;case"template-name":;case"sheet-name":break;case"event-listener":break;case"initial-creator":;case"creation-date":;case"print-date":;case"generator":;case"document-statistic":;case"user-defined":;case"editing-duration":;case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":;case"source-cell-range":;case"source-service":;case"data-pilot-field":;case"data-pilot-level":;case"data-pilot-subtotals":;case"data-pilot-subtotal":;case"data-pilot-members":;case"data-pilot-member":;case"data-pilot-display-info":;case"data-pilot-sort-info":;case"data-pilot-layout-info":;case"data-pilot-field-reference":;case"data-pilot-groups":;case"data-pilot-group":;case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":;case"dde-connection-decl":;case"dde-link":;case"dde-source":break;case"properties":break;case"property":break;case"a":if(v[1]!=="/"){L=jr(v[0],false);if(!L.href)break;L.Target=Gr(L.href);delete L.href;if(L.Target.charAt(0)=="#"&&L.Target.indexOf(".")>-1){B=Cs(L.Target.slice(1));L.Target="#"+B[0]+"!"+B[1]}else if(L.Target.match(/^\.\.[\\\/]/))L.Target=L.Target.slice(3)}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(v[2]){case"dc:":;case"calcext:":;case"loext:":;case"ooo:":;case"chartooo:":;case"draw:":;case"style:":;case"chart:":;case"form:":;case"uof:":;case"表:":;case"字:":break;default:if(t.WTF)throw new Error(v);};}var q={Sheets:h,SheetNames:p,Workbook:R};if(t.bookSheets)delete q.Sheets;return q}function po(e,r){r=r||{};if(Ar(e,"META-INF/manifest.xml"))yn(Er(e,"META-INF/manifest.xml"),r);var t=Fr(e,"content.xml");if(!t)throw new Error("Missing content.xml in ODS / UOF file");var a=ho(ot(t),r);if(Ar(e,"meta.xml"))a.Props=En(Er(e,"meta.xml"));return a}function mo(e,r){return ho(e,r)}var vo=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join("");var r="<office:document-styles "+bt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function t(){return Rr+r}}();var go=function(){var e=function(e){return Kr(e).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")};var r="          <table:table-cell />\n";var t="          <table:covered-table-cell/>\n";var a=function(a,n,i){var s=[];s.push('      <table:table table:name="'+Kr(n.SheetNames[i])+'" table:style-name="ta1">\n');var l=0,o=0,c=Da(a["!ref"]||"A1");var f=a["!merges"]||[],u=0;var h=Array.isArray(a);if(a["!cols"]){for(o=0;o<=c.e.c;++o)s.push("        <table:table-column"+(a["!cols"][o]?' table:style-name="co'+a["!cols"][o].ods+'"':"")+"></table:table-column>\n")}var p="",d=a["!rows"]||[];for(l=0;l<c.s.r;++l){p=d[l]?' table:style-name="ro'+d[l].ods+'"':"";s.push("        <table:table-row"+p+"></table:table-row>\n")}for(;l<=c.e.r;++l){p=d[l]?' table:style-name="ro'+d[l].ods+'"':"";s.push("        <table:table-row"+p+">\n");for(o=0;o<c.s.c;++o)s.push(r);for(;o<=c.e.c;++o){var m=false,v={},g="";for(u=0;u!=f.length;++u){if(f[u].s.c>o)continue;if(f[u].s.r>l)continue;if(f[u].e.c<o)continue;if(f[u].e.r<l)continue;if(f[u].s.c!=o||f[u].s.r!=l)m=true;v["table:number-columns-spanned"]=f[u].e.c-f[u].s.c+1;v["table:number-rows-spanned"]=f[u].e.r-f[u].s.r+1;break}if(m){s.push(t);continue}var b=Fa({r:l,c:o}),w=h?(a[l]||[])[o]:a[b];if(w&&w.f){v["table:formula"]=Kr(Ss(w.f));if(w.F){if(w.F.slice(0,b.length)==b){var y=Da(w.F);v["table:number-matrix-columns-spanned"]=y.e.c-y.s.c+1;v["table:number-matrix-rows-spanned"]=y.e.r-y.s.r+1}}}if(!w){s.push(r);continue}switch(w.t){case"b":g=w.v?"TRUE":"FALSE";v["office:value-type"]="boolean";v["office:boolean-value"]=w.v?"true":"false";break;case"n":g=w.w||String(w.v||0);v["office:value-type"]="float";v["office:value"]=w.v||0;break;case"s":;case"str":g=w.v==null?"":w.v;v["office:value-type"]="string";break;case"d":g=w.w||mr(w.v).toISOString();v["office:value-type"]="date";v["office:date-value"]=mr(w.v).toISOString();v["table:style-name"]="ce1";break;default:s.push(r);continue;}var k=e(g);if(w.l&&w.l.Target){var x=w.l.Target;x=x.charAt(0)=="#"?"#"+_s(x.slice(1)):x;if(x.charAt(0)!="#"&&!x.match(/^\w+:/))x="../"+x;k=wt("text:a",k,{"xlink:href":x.replace(/&/g,"&amp;")})}s.push("          "+wt("table:table-cell",wt("text:p",k,{}),v)+"\n")}s.push("        </table:table-row>\n")}s.push("      </table:table>\n");return s.join("")};var n=function(e,r){e.push(" <office:automatic-styles>\n");e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n');e.push('   <number:month number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push('   <number:day number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push("   <number:year/>\n");e.push("  </number:date-style>\n");var t=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!cols"]){for(var a=0;a<r["!cols"].length;++a)if(r["!cols"][a]){var n=r["!cols"][a];if(n.width==null&&n.wpx==null&&n.wch==null)continue;wi(n);n.ods=t;var i=r["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+t+'" style:family="table-column">\n');e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n');e.push("  </style:style>\n");++t}}});var a=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!rows"]){for(var t=0;t<r["!rows"].length;++t)if(r["!rows"][t]){r["!rows"][t].ods=a;var n=r["!rows"][t].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n');e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n');e.push("  </style:style>\n");++a}}});e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n');e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n');e.push("  </style:style>\n");e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n');e.push(" </office:automatic-styles>\n")};return function i(e,r){var t=[Rr];var i=bt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"});var s=bt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});if(r.bookType=="fods"){t.push("<office:document"+i+s+">\n");t.push(_n().replace(/office:document-meta/g,"office:meta"))}else t.push("<office:document-content"+i+">\n");n(t,e);t.push("  <office:body>\n");t.push("    <office:spreadsheet>\n");for(var l=0;l!=e.SheetNames.length;++l)t.push(a(e.Sheets[e.SheetNames[l]],e,l,r));t.push("    </office:spreadsheet>\n");t.push("  </office:body>\n");if(r.bookType=="fods")t.push("</office:document>");else t.push("</office:document-content>");return t.join("")}}();function bo(e,r){if(r.bookType=="fods")return go(e,r);var t=Nr();var a="";var n=[];var i=[];a="mimetype";Mr(t,a,"application/vnd.oasis.opendocument.spreadsheet");a="content.xml";Mr(t,a,go(e,r));n.push([a,"text/xml"]);i.push([a,"ContentFile"]);a="styles.xml";Mr(t,a,vo(e,r));n.push([a,"text/xml"]);i.push([a,"StylesFile"]);a="meta.xml";Mr(t,a,Rr+_n());n.push([a,"text/xml"]);i.push([a,"MetadataFile"]);a="manifest.rdf";Mr(t,a,Cn(i));n.push([a,"application/rdf+xml"]);a="META-INF/manifest.xml";Mr(t,a,kn(n));return t}function wo(e){return function r(t){for(var a=0;a!=e.length;++a){var n=e[a];if(t[n[0]]===undefined)t[n[0]]=n[1];if(n[2]==="n")t[n[0]]=Number(t[n[0]])}}}function yo(e){wo([["cellNF",false],["cellHTML",true],["cellFormula",true],["cellStyles",false],["cellText",true],["cellDates",false],["sheetStubs",false],["sheetRows",0,"n"],["bookDeps",false],["bookSheets",false],["bookProps",false],["bookFiles",false],["bookVBA",false],["password",""],["WTF",false]])(e)}function ko(e){wo([["cellDates",false],["bookSST",false],["bookType","xlsx"],["compression",false],["WTF",false]])(e)}function xo(e){if(dn.WS.indexOf(e)>-1)return"sheet";if(dn.CS&&e==dn.CS)return"chart";if(dn.DS&&e==dn.DS)return"dialog";if(dn.MS&&e==dn.MS)return"macro";return e&&e.length?e:"sheet"}function So(e,r){if(!e)return 0;try{e=r.map(function a(r){if(!r.id)r.id=r.strRelID;return[r.name,e["!id"][r.id].Target,xo(e["!id"][r.id].Type)]})}catch(t){return null}return!e||e.length===0?null:e}function Co(e,r,t,a,n,i,s,l,o,c,f,u){try{i[a]=vn(Fr(e,t,true),r);var h=Er(e,r);var p;switch(l){case"sheet":p=Pl(h,r,n,o,i[a],c,f,u);break;case"chart":p=Il(h,r,n,o,i[a],c,f,u);if(!p||!p["!drawel"])break;var d=Ir(p["!drawel"].Target,r);var m=mn(d);var v=qi(Fr(e,d,true),vn(Fr(e,m,true),d));var g=Ir(v,d);var b=mn(g);p=ml(Fr(e,g,true),g,o,vn(Fr(e,b,true),g),c,p);break;case"macro":p=Rl(h,r,n,o,i[a],c,f,u);break;case"dialog":p=Ll(h,r,n,o,i[a],c,f,u);break;default:throw new Error("Unrecognized sheet type "+l);}s[a]=p;var w=[];if(i&&i[a])er(i[a]).forEach(function(t){var n="";if(i[a][t].Type==dn.CMNT){n=Ir(i[a][t].Target,r);var s=Ul(Er(e,n,true),n,o);if(!s||!s.length)return;es(p,s,false)}if(i[a][t].Type==dn.TCMNT){n=Ir(i[a][t].Target,r);w=w.concat(as(Er(e,n,true),o));
}});if(w&&w.length)es(p,w,true,o.people||[])}catch(y){if(o.WTF)throw y}}function _o(e){return e.charAt(0)=="/"?e.slice(1):e}function Ao(e,r){Ue();r=r||{};yo(r);if(Ar(e,"META-INF/manifest.xml"))return po(e,r);if(Ar(e,"objectdata.xml"))return po(e,r);if(Ar(e,"Index/Document.iwa")){if(typeof Uint8Array=="undefined")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof parse_numbers_iwa!="undefined"){if(e.FileIndex)return parse_numbers_iwa(e);var t=Ye.utils.cfb_new();Or(e).forEach(function(r){Mr(t,r,Dr(e,r))});return parse_numbers_iwa(t)}throw new Error("Unsupported NUMBERS file")}if(!Ar(e,"[Content_Types].xml")){if(Ar(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Ar(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a=Or(e);var n=hn(Fr(e,"[Content_Types].xml"));var i=false;var s,l;if(n.workbooks.length===0){l="xl/workbook.xml";if(Er(e,l,true))n.workbooks.push(l)}if(n.workbooks.length===0){l="xl/workbook.bin";if(!Er(e,l,true))throw new Error("Could not find workbook");n.workbooks.push(l);i=true}if(n.workbooks[0].slice(-3)=="bin")i=true;var o={};var c={};if(!r.bookSheets&&!r.bookProps){As=[];if(n.sst)try{As=zl(Er(e,_o(n.sst)),n.sst,r)}catch(f){if(r.WTF)throw f}if(r.cellStyles&&n.themes.length)o=$l(Fr(e,n.themes[0].replace(/^\//,""),true)||"",n.themes[0],r);if(n.style)c=Bl(Er(e,_o(n.style)),n.style,o,r)}n.links.map(function(t){try{var a=vn(Fr(e,mn(_o(t))),t);return jl(Er(e,_o(t)),a,t,r)}catch(n){}});var u=Nl(Er(e,_o(n.workbooks[0])),n.workbooks[0],r);var h={},p="";if(n.coreprops.length){p=Er(e,_o(n.coreprops[0]),true);if(p)h=En(p);if(n.extprops.length!==0){p=Er(e,_o(n.extprops[0]),true);if(p)Pn(p,h,r)}}var d={};if(!r.bookSheets||r.bookProps){if(n.custprops.length!==0){p=Fr(e,_o(n.custprops[0]),true);if(p)d=Ln(p,r)}}var m={};if(r.bookSheets||r.bookProps){if(u.Sheets)s=u.Sheets.map(function O(e){return e.name});else if(h.Worksheets&&h.SheetNames.length>0)s=h.SheetNames;if(r.bookProps){m.Props=h;m.Custprops=d}if(r.bookSheets&&typeof s!=="undefined")m.SheetNames=s;if(r.bookSheets?m.SheetNames:r.bookProps)return m}s={};var v={};if(r.bookDeps&&n.calcchain)v=Wl(Er(e,_o(n.calcchain)),n.calcchain,r);var g=0;var b={};var w,y;{var k=u.Sheets;h.Worksheets=k.length;h.SheetNames=[];for(var x=0;x!=k.length;++x){h.SheetNames[x]=k[x].name}}var S=i?"bin":"xml";var C=n.workbooks[0].lastIndexOf("/");var _=(n.workbooks[0].slice(0,C+1)+"_rels/"+n.workbooks[0].slice(C+1)+".rels").replace(/^\//,"");if(!Ar(e,_))_="xl/_rels/workbook."+S+".rels";var A=vn(Fr(e,_,true),_.replace(/_rels.*/,"s5s"));if((n.metadata||[]).length>=1){r.xlmeta=Hl(Er(e,_o(n.metadata[0])),n.metadata[0],r)}if((n.people||[]).length>=1){r.people=is(Er(e,_o(n.people[0])),r)}if(A)A=So(A,u.Sheets);var T=Er(e,"xl/worksheets/sheet.xml",true)?1:0;e:for(g=0;g!=h.Worksheets;++g){var E="sheet";if(A&&A[g]){w="xl/"+A[g][1].replace(/[\/]?xl\//,"");if(!Ar(e,w))w=A[g][1];if(!Ar(e,w))w=_.replace(/_rels\/.*$/,"")+A[g][1];E=A[g][2]}else{w="xl/worksheets/sheet"+(g+1-T)+"."+S;w=w.replace(/sheet0\./,"sheet.")}y=w.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels");if(r&&r.sheets!=null)switch(typeof r.sheets){case"number":if(g!=r.sheets)continue e;break;case"string":if(h.SheetNames[g].toLowerCase()!=r.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(r.sheets)){var F=false;for(var D=0;D!=r.sheets.length;++D){if(typeof r.sheets[D]=="number"&&r.sheets[D]==g)F=1;if(typeof r.sheets[D]=="string"&&r.sheets[D].toLowerCase()==h.SheetNames[g].toLowerCase())F=1}if(!F)continue e};}Co(e,w,y,h.SheetNames[g],g,b,s,E,r,u,o,c)}m={Directory:n,Workbook:u,Props:h,Custprops:d,Deps:v,Sheets:s,SheetNames:h.SheetNames,Strings:As,Styles:c,Themes:o,SSF:gr(X)};if(r&&r.bookFiles){if(e.files){m.keys=a;m.files=e.files}else{m.keys=[];m.files={};e.FullPaths.forEach(function(r,t){r=r.replace(/^Root Entry[\/]/,"");m.keys.push(r);m.files[r]=e.FileIndex[t]})}}if(r&&r.bookVBA){if(n.vba.length>0)m.vbaraw=Er(e,_o(n.vba[0]),true);else if(n.defaults&&n.defaults.bin===ls)m.vbaraw=Er(e,"xl/vbaProject.bin",true)}return m}function To(e,r){var t=r||{};var a="Workbook",n=Ye.find(e,a);try{a="/!DataSpaces/Version";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);parse_DataSpaceVersionInfo(n.content);a="/!DataSpaces/DataSpaceMap";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=parse_DataSpaceMap(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=parse_DataSpaceDefinition(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);parse_Primary(n.content)}catch(l){}a="/EncryptionInfo";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var o=parse_EncryptionInfo(n.content);a="/EncryptedPackage";n=Ye.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(o[0]==4&&typeof decrypt_agile!=="undefined")return decrypt_agile(o[1],n.content,t.password||"",t);if(o[0]==2&&typeof decrypt_std76!=="undefined")return decrypt_std76(o[1],n.content,t.password||"",t);throw new Error("File is password-protected")}function Eo(e,r){if(r.bookType=="ods")return bo(e,r);if(r.bookType=="numbers")return write_numbers_iwa(e,r);if(r.bookType=="xlsb")return Fo(e,r);return Do(e,r)}function Fo(e,r){Zi=1024;if(e&&!e.SSF){e.SSF=gr(X)}if(e&&e.SSF){Ue();ze(e.SSF);r.revssf=ar(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF}r.rels={};r.wbrels={};r.Strings=[];r.Strings.Count=0;r.Strings.Unique=0;if(Es)r.revStrings=new Map;else{r.revStrings={};r.revStrings.foo=[];delete r.revStrings.foo}var t=r.bookType=="xlsb"?"bin":"xml";var a=fs.indexOf(r.bookType)>-1;var n=un();ko(r=r||{});var i=Nr();var s="",l=0;r.cellXfs=[];Ms(r.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};s="docProps/core.xml";Mr(i,s,Dn(e.Props,r));n.coreprops.push(s);bn(r.rels,2,s,dn.CORE_PROPS);s="docProps/app.xml";if(e.Props&&e.Props.SheetNames){}else if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{var o=[];for(var c=0;c<e.SheetNames.length;++c)if((e.Workbook.Sheets[c]||{}).Hidden!=2)o.push(e.SheetNames[c]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length;Mr(i,s,In(e.Props,r));n.extprops.push(s);bn(r.rels,3,s,dn.EXT_PROPS);if(e.Custprops!==e.Props&&er(e.Custprops||{}).length>0){s="docProps/custom.xml";Mr(i,s,Bn(e.Custprops,r));n.custprops.push(s);bn(r.rels,4,s,dn.CUST_PROPS)}for(l=1;l<=e.SheetNames.length;++l){var f={"!id":{}};var u=e.Sheets[e.SheetNames[l-1]];var h=(u||{})["!type"]||"sheet";switch(h){case"chart":;default:s="xl/worksheets/sheet"+l+"."+t;Mr(i,s,Xl(l-1,s,r,e,f));n.sheets.push(s);bn(r.wbrels,-1,"worksheets/sheet"+l+"."+t,dn.WS[0]);}if(u){var p=u["!comments"];var d=false;var m="";if(p&&p.length>0){m="xl/comments"+l+"."+t;Mr(i,m,Kl(p,m,r));n.comments.push(m);bn(f,-1,"../comments"+l+"."+t,dn.CMNT);d=true}if(u["!legacy"]){if(d)Mr(i,"xl/drawings/vmlDrawing"+l+".vml",Qi(l,u["!comments"]))}delete u["!comments"];delete u["!legacy"]}if(f["!id"].rId1)Mr(i,mn(s),gn(f))}if(r.Strings!=null&&r.Strings.length>0){s="xl/sharedStrings."+t;Mr(i,s,Jl(r.Strings,s,r));n.strs.push(s);bn(r.wbrels,-1,"sharedStrings."+t,dn.SST)}s="xl/workbook."+t;Mr(i,s,Vl(e,s,r));n.workbooks.push(s);bn(r.rels,1,s,dn.WB);s="xl/theme/theme1.xml";Mr(i,s,Xi(e.Themes,r));n.themes.push(s);bn(r.wbrels,-1,"theme/theme1.xml",dn.THEME);s="xl/styles."+t;Mr(i,s,Yl(e,s,r));n.styles.push(s);bn(r.wbrels,-1,"styles."+t,dn.STY);if(e.vbaraw&&a){s="xl/vbaProject.bin";Mr(i,s,e.vbaraw);n.vba.push(s);bn(r.wbrels,-1,"vbaProject.bin",dn.VBA)}s="xl/metadata."+t;Mr(i,s,ql(s));n.metadata.push(s);bn(r.wbrels,-1,"metadata."+t,dn.XLMETA);Mr(i,"[Content_Types].xml",pn(n,r));Mr(i,"_rels/.rels",gn(r.rels));Mr(i,"xl/_rels/workbook."+t+".rels",gn(r.wbrels));delete r.revssf;delete r.ssf;return i}function Do(e,r){Zi=1024;if(e&&!e.SSF){e.SSF=gr(X)}if(e&&e.SSF){Ue();ze(e.SSF);r.revssf=ar(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF}r.rels={};r.wbrels={};r.Strings=[];r.Strings.Count=0;r.Strings.Unique=0;if(Es)r.revStrings=new Map;else{r.revStrings={};r.revStrings.foo=[];delete r.revStrings.foo}var t="xml";var a=fs.indexOf(r.bookType)>-1;var n=un();ko(r=r||{});var i=Nr();var s="",l=0;r.cellXfs=[];Ms(r.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};s="docProps/core.xml";Mr(i,s,Dn(e.Props,r));n.coreprops.push(s);bn(r.rels,2,s,dn.CORE_PROPS);s="docProps/app.xml";if(e.Props&&e.Props.SheetNames){}else if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{var o=[];for(var c=0;c<e.SheetNames.length;++c)if((e.Workbook.Sheets[c]||{}).Hidden!=2)o.push(e.SheetNames[c]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length;Mr(i,s,In(e.Props,r));n.extprops.push(s);bn(r.rels,3,s,dn.EXT_PROPS);if(e.Custprops!==e.Props&&er(e.Custprops||{}).length>0){s="docProps/custom.xml";Mr(i,s,Bn(e.Custprops,r));n.custprops.push(s);bn(r.rels,4,s,dn.CUST_PROPS)}var f=["SheetJ5"];r.tcid=0;for(l=1;l<=e.SheetNames.length;++l){var u={"!id":{}};var h=e.Sheets[e.SheetNames[l-1]];var p=(h||{})["!type"]||"sheet";switch(p){case"chart":;default:s="xl/worksheets/sheet"+l+"."+t;Mr(i,s,pl(l-1,r,e,u));n.sheets.push(s);bn(r.wbrels,-1,"worksheets/sheet"+l+"."+t,dn.WS[0]);}if(h){var d=h["!comments"];var m=false;var v="";if(d&&d.length>0){var g=false;d.forEach(function(e){e[1].forEach(function(e){if(e.T==true)g=true})});if(g){v="xl/threadedComments/threadedComment"+l+"."+t;Mr(i,v,ns(d,f,r));n.threadedcomments.push(v);bn(u,-1,"../threadedComments/threadedComment"+l+"."+t,dn.TCMNT)}v="xl/comments"+l+"."+t;Mr(i,v,ts(d,r));n.comments.push(v);bn(u,-1,"../comments"+l+"."+t,dn.CMNT);m=true}if(h["!legacy"]){if(m)Mr(i,"xl/drawings/vmlDrawing"+l+".vml",Qi(l,h["!comments"]))}delete h["!comments"];delete h["!legacy"]}if(u["!id"].rId1)Mr(i,mn(s),gn(u))}if(r.Strings!=null&&r.Strings.length>0){s="xl/sharedStrings."+t;Mr(i,s,ni(r.Strings,r));n.strs.push(s);bn(r.wbrels,-1,"sharedStrings."+t,dn.SST)}s="xl/workbook."+t;Mr(i,s,Ml(e,r));n.workbooks.push(s);bn(r.rels,1,s,dn.WB);s="xl/theme/theme1.xml";Mr(i,s,Xi(e.Themes,r));n.themes.push(s);bn(r.wbrels,-1,"theme/theme1.xml",dn.THEME);s="xl/styles."+t;Mr(i,s,Ii(e,r));n.styles.push(s);bn(r.wbrels,-1,"styles."+t,dn.STY);if(e.vbaraw&&a){s="xl/vbaProject.bin";Mr(i,s,e.vbaraw);n.vba.push(s);bn(r.wbrels,-1,"vbaProject.bin",dn.VBA)}s="xl/metadata."+t;Mr(i,s,Yi());n.metadata.push(s);bn(r.wbrels,-1,"metadata."+t,dn.XLMETA);if(f.length>1){s="xl/persons/person.xml";Mr(i,s,ss(f,r));n.people.push(s);bn(r.wbrels,-1,"persons/person.xml",dn.PEOPLE)}Mr(i,"[Content_Types].xml",pn(n,r));Mr(i,"_rels/.rels",gn(r.rels));Mr(i,"xl/_rels/workbook."+t+".rels",gn(r.wbrels));delete r.revssf;delete r.ssf;return i}function Oo(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=w(e.slice(0,12));break;case"binary":t=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(r&&r.type||"undefined"));}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function Mo(e,r){if(Ye.find(e,"EncryptedPackage"))return To(e,r);return parse_xlscfb(e,r)}function No(e,r){var t,a=e;var n=r||{};if(!n.type)n.type=y&&Buffer.isBuffer(e)?"buffer":"base64";t=Pr(a,n);return Ao(t,n)}function Po(e,r){var t=0;e:while(t<e.length)switch(e.charCodeAt(t)){case 10:;case 13:;case 32:++t;break;case 60:return parse_xlml(e.slice(t),r);default:break e;}return Hn.to_workbook(e,r)}function Io(e,r){var t="",a=Oo(e,r);switch(r.type){case"base64":t=w(e);break;case"binary":t=e;break;case"buffer":t=e.toString("binary");break;case"array":t=vr(e);break;default:throw new Error("Unrecognized type "+r.type);}if(a[0]==239&&a[1]==187&&a[2]==191)t=ot(t);r.type="binary";return Po(t,r)}function Ro(e,r){var t=e;if(r.type=="base64")t=w(t);t=$cptable.utils.decode(1200,t.slice(2),"str");r.type="binary";return Po(t,r)}function Lo(e){return!e.match(/[^\x00-\x7F]/)?e:ct(e)}function Bo(e,r,t,a){if(a){t.type="string";return Hn.to_workbook(e,t)}return Hn.to_workbook(r,t)}function $o(e,r){o();var t=r||{};if(typeof ArrayBuffer!=="undefined"&&e instanceof ArrayBuffer)return $o(new Uint8Array(e),(t=gr(t),t.type="array",t));if(typeof Uint8Array!=="undefined"&&e instanceof Uint8Array&&!t.type)t.type=typeof Deno!=="undefined"?"buffer":"array";var a=e,n=[0,0,0,0],i=false;if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}Ts={};if(t.dateNF)Ts.dateNF=t.dateNF;if(!t.type)t.type=y&&Buffer.isBuffer(e)?"buffer":"base64";if(t.type=="file"){t.type=y?"buffer":"binary";a=Qe(e);if(typeof Uint8Array!=="undefined"&&!y)t.type="array"}if(t.type=="string"){i=true;t.type="binary";t.codepage=65001;a=Lo(e)}if(t.type=="array"&&typeof Uint8Array!=="undefined"&&e instanceof Uint8Array&&typeof ArrayBuffer!=="undefined"){var s=new ArrayBuffer(3),l=new Uint8Array(s);l.foo="bar";if(!l.foo){t=gr(t);t.type="array";return $o(E(a),t)}}switch((n=Oo(a,t))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return Mo(Ye.read(a,t),t);break;case 9:if(n[1]<=8)return parse_xlscfb(a,t);break;case 60:return parse_xlml(a,t);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return Vn(a,t);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return Wn.to_workbook(a,t);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?No(a,t):Bo(e,a,t,i);case 239:return n[3]===60?parse_xlml(a,t):Bo(e,a,t,i);case 255:if(n[1]===254){return Ro(a,t)}else if(n[1]===0&&n[2]===2&&n[3]===0)return WK_.to_workbook(a,t);break;case 0:if(n[1]===0){if(n[2]>=2&&n[3]===0)return WK_.to_workbook(a,t);if(n[2]===0&&(n[3]===8||n[3]===9))return WK_.to_workbook(a,t)}break;case 3:;case 131:;case 139:;case 140:return zn.to_workbook(a,t);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return RTF.to_workbook(a,t);break;case 10:;case 13:;case 32:return Io(a,t);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break;}if($n.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31)return zn.to_workbook(a,t);return Bo(e,a,t,i)}function zo(e,r){var t=r||{};t.type="file";return $o(e,t)}function Uo(e,r){switch(r.type){case"base64":;case"binary":break;case"buffer":;case"array":r.type="";break;case"file":return Ze(r.file,Ye.write(e,{type:y?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");default:throw new Error("Unrecognized type "+r.type);}return Ye.write(e,r)}function Wo(e,r){var t=gr(r||{});var a=Eo(e,t);return Ho(a,t)}function jo(e,r){var t=gr(r||{});var a=Do(e,t);return Ho(a,t)}function Ho(e,r){var t={};var a=y?"nodebuffer":typeof Uint8Array!=="undefined"?"array":"string";if(r.compression)t.compression="DEFLATE";if(r.password)t.type=a;else switch(r.type){case"base64":t.type="base64";break;case"binary":t.type="string";break;case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");case"buffer":;case"file":t.type=a;break;default:throw new Error("Unrecognized type "+r.type);}var n=e.FullPaths?Ye.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[t.type]||t.type,compression:!!r.compression}):e.generate(t);if(typeof Deno!=="undefined"){if(typeof n=="string"){if(r.type=="binary"||r.type=="base64")return n;n=new Uint8Array(_(n))}}if(r.password&&typeof encrypt_agile!=="undefined")return Uo(encrypt_agile(n,r.password),r);if(r.type==="file")return Ze(r.file,n);return r.type=="string"?ot(n):n}function Vo(e,r){var t=r||{};var a=write_xlscfb(e,t);return Uo(a,t)}function Xo(e,r,t){if(!t)t="";var a=t+e;switch(r.type){case"base64":return b(ct(a));case"binary":return ct(a);case"string":return e;case"file":return Ze(r.file,a,"utf8");case"buffer":{if(y)return k(a,"utf8");else if(typeof TextEncoder!=="undefined")return(new TextEncoder).encode(a);else return Xo(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function Go(e,r){switch(r.type){case"base64":return b(e);case"binary":return e;case"string":return e;case"file":return Ze(r.file,e,"binary");case"buffer":{if(y)return k(e,"binary");else return e.split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function Yo(e,r){switch(r.type){case"string":;case"base64":;case"binary":var t="";for(var a=0;a<e.length;++a)t+=String.fromCharCode(e[a]);return r.type=="base64"?b(t):r.type=="string"?ot(t):t;case"file":return Ze(r.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+r.type);}}function Jo(e,r){o();Fl(e);var t=gr(r||{});if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}if(t.type=="array"){t.type="binary";var a=Jo(e,t);t.type="array";return _(a)}return jo(e,t)}function Ko(e,r){o();Fl(e);var t=gr(r||{});if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}if(t.type=="array"){t.type="binary";var a=Ko(e,t);t.type="array";return _(a)}var n=0;if(t.sheet){if(typeof t.sheet=="number")n=t.sheet;else n=e.SheetNames.indexOf(t.sheet);if(!e.SheetNames[n])throw new Error("Sheet not found: "+t.sheet+" : "+typeof t.sheet)}switch(t.bookType||"xlsb"){case"xml":;case"xlml":return Xo(write_xlml(e,t),t);case"slk":;case"sylk":return Xo(Un.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"htm":;case"html":return Xo(no(e.Sheets[e.SheetNames[n]],t),t);case"txt":return Go(sc(e.Sheets[e.SheetNames[n]],t),t);case"csv":return Xo(ic(e.Sheets[e.SheetNames[n]],t),t,"\ufeff");case"dif":return Xo(Wn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"dbf":return Yo(zn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"prn":return Xo(Hn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"rtf":return Xo(RTF.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"eth":return Xo(jn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"fods":return Xo(bo(e,t),t);case"wk1":return Yo(WK_.sheet_to_wk1(e.Sheets[e.SheetNames[n]],t),t);case"wk3":return Yo(WK_.book_to_wk3(e,t),t);case"biff2":if(!t.biff)t.biff=2;case"biff3":if(!t.biff)t.biff=3;case"biff4":if(!t.biff)t.biff=4;return Yo(write_biff_buf(e,t),t);case"biff5":if(!t.biff)t.biff=5;case"biff8":;case"xla":;case"xls":if(!t.biff)t.biff=8;return Vo(e,t);case"xlsx":;case"xlsm":;case"xlam":;case"xlsb":;case"numbers":;case"ods":return Wo(e,t);default:throw new Error("Unrecognized bookType |"+t.bookType+"|");}}function qo(e){if(e.bookType)return;var r={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"};var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();if(t.match(/^\.[a-z]+$/))e.bookType=t.slice(1);e.bookType=r[e.bookType]||e.bookType}function Zo(e,r,t){var a=t||{};a.type="file";a.file=r;qo(a);return Ko(e,a)}function Qo(e,r,t){var a=t||{};a.type="file";a.file=r;qo(a);return Jo(e,a)}function ec(e,r,t,a){var n=t||{};n.type="file";n.file=e;qo(n);n.type="buffer";var i=a;if(!(i instanceof Function))i=t;return Je.writeFile(e,Ko(r,n),i)}function rc(e,r,t,a,n,i,s,l){var o=ya(t);var c=l.defval,f=l.raw||!Object.prototype.hasOwnProperty.call(l,"raw");var u=true;var h=n===1?[]:{};if(n!==1){if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:t,enumerable:false})}catch(p){h.__rowNum__=t}else h.__rowNum__=t}if(!s||e[t])for(var d=r.s.c;d<=r.e.c;++d){var m=s?e[t][d]:e[a[d]+o];if(m===undefined||m.t===undefined){if(c===undefined)continue;if(i[d]!=null){h[i[d]]=c}continue}var v=m.v;switch(m.t){case"z":if(v==null)break;continue;case"e":v=v==0?null:void 0;break;case"s":;case"d":;case"b":;case"n":break;default:throw new Error("unrecognized type "+m.t);}if(i[d]!=null){if(v==null){if(m.t=="e"&&v===null)h[i[d]]=null;else if(c!==undefined)h[i[d]]=c;else if(f&&v===null)h[i[d]]=null;else continue}else{h[i[d]]=f&&(m.t!=="n"||m.t==="n"&&l.rawNumbers!==false)?v:Pa(m,v,l)}if(v!=null)u=false}}return{row:h,isempty:u}}function tc(e,r){if(e==null||e["!ref"]==null)return[];var t={t:"n",v:0},a=0,n=1,i=[],s=0,l="";var o={s:{r:0,c:0},e:{r:0,c:0}};var c=r||{};var f=c.range!=null?c.range:e["!ref"];if(c.header===1)a=1;else if(c.header==="A")a=2;else if(Array.isArray(c.header))a=3;else if(c.header==null)a=0;switch(typeof f){case"string":o=Ma(f);break;case"number":o=Ma(e["!ref"]);o.s.r=f;break;default:o=f;}if(a>0)n=0;var u=ya(o.s.r);var h=[];var p=[];var d=0,m=0;var v=Array.isArray(e);var g=o.s.r,b=0;var w={};if(v&&!e[g])e[g]=[];var y=c.skipHidden&&e["!cols"]||[];var k=c.skipHidden&&e["!rows"]||[];for(b=o.s.c;b<=o.e.c;++b){if((y[b]||{}).hidden)continue;h[b]=Ca(b);t=v?e[g][b]:e[h[b]+u];switch(a){case 1:i[b]=b-o.s.c;break;case 2:i[b]=h[b];break;case 3:i[b]=c.header[b-o.s.c];break;default:if(t==null)t={w:"__EMPTY",t:"s"};l=s=Pa(t,null,c);m=w[s]||0;if(!m)w[s]=1;else{do{l=s+"_"+m++}while(w[l]);w[s]=m;w[l]=1}i[b]=l;}}for(g=o.s.r+n;g<=o.e.r;++g){if((k[g]||{}).hidden)continue;var x=rc(e,o,g,h,a,i,v,c);if(x.isempty===false||(a===1?c.blankrows!==false:!!c.blankrows))p[d++]=x.row}p.length=d;return p}var ac=/"/g;function nc(e,r,t,a,n,i,s,l){var o=true;var c=[],f="",u=ya(t);for(var h=r.s.c;h<=r.e.c;++h){if(!a[h])continue;var p=l.dense?(e[t]||[])[h]:e[a[h]+u];if(p==null)f="";else if(p.v!=null){o=false;f=""+(l.rawNumbers&&p.t=="n"?p.v:Pa(p,null,l));for(var d=0,m=0;d!==f.length;++d)if((m=f.charCodeAt(d))===n||m===i||m===34||l.forceQuotes){f='"'+f.replace(ac,'""')+'"';break}if(f=="ID")f='"ID"'}else if(p.f!=null&&!p.F){o=false;f="="+p.f;if(f.indexOf(",")>=0)f='"'+f.replace(ac,'""')+'"'}else f="";c.push(f)}if(l.blankrows===false&&o)return null;return c.join(s)}function ic(e,r){var t=[];var a=r==null?{}:r;if(e==null||e["!ref"]==null)return"";var n=Ma(e["!ref"]);var i=a.FS!==undefined?a.FS:",",s=i.charCodeAt(0);var l=a.RS!==undefined?a.RS:"\n",o=l.charCodeAt(0);var c=new RegExp((i=="|"?"\\|":i)+"+$");var f="",u=[];a.dense=Array.isArray(e);var h=a.skipHidden&&e["!cols"]||[];var p=a.skipHidden&&e["!rows"]||[];for(var d=n.s.c;d<=n.e.c;++d)if(!(h[d]||{}).hidden)u[d]=Ca(d);var m=0;for(var v=n.s.r;v<=n.e.r;++v){if((p[v]||{}).hidden)continue;f=nc(e,n,v,u,s,o,i,a);if(f==null){continue}if(a.strip)f=f.replace(c,"");if(f||a.blankrows!==false)t.push((m++?l:"")+f)}delete a.dense;return t.join("")}function sc(e,r){if(!r)r={};r.FS="\t";r.RS="\n";var t=ic(e,r);if(typeof $cptable=="undefined"||r.type=="string")return t;var a=$cptable.utils.encode(1200,t,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function lc(e){var r="",t,a="";if(e==null||e["!ref"]==null)return[];var n=Ma(e["!ref"]),i="",s=[],l;var o=[];var c=Array.isArray(e);for(l=n.s.c;l<=n.e.c;++l)s[l]=Ca(l);for(var f=n.s.r;f<=n.e.r;++f){i=ya(f);for(l=n.s.c;l<=n.e.c;++l){r=s[l]+i;t=c?(e[f]||[])[l]:e[r];a="";if(t===undefined)continue;else if(t.F!=null){r=t.F;if(!t.f)continue;a=t.f;if(r.indexOf(":")==-1)r=r+":"+r}if(t.f!=null)a=t.f;else if(t.t=="z")continue;else if(t.t=="n"&&t.v!=null)a=""+t.v;else if(t.t=="b")a=t.v?"TRUE":"FALSE";else if(t.w!==undefined)a="'"+t.w;else if(t.v===undefined)continue;else if(t.t=="s")a="'"+t.v;else a=""+t.v;o[o.length]=r+"="+a}}return o}function oc(e,r,t){var a=t||{};var n=+!a.skipHeader;var i=e||{};var s=0,l=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var o=typeof a.origin=="string"?Ea(a.origin):a.origin;s=o.r;l=o.c}}var c;var f={s:{c:0,r:0},e:{c:l,r:s+r.length-1+n}};if(i["!ref"]){var u=Ma(i["!ref"]);f.e.c=Math.max(f.e.c,u.e.c);f.e.r=Math.max(f.e.r,u.e.r);if(s==-1){s=u.e.r+1;f.e.r=s+r.length-1+n}}else{if(s==-1){s=0;f.e.r=r.length-1+n}}var h=a.header||[],p=0;r.forEach(function(e,r){er(e).forEach(function(t){if((p=h.indexOf(t))==-1)h[p=h.length]=t;var o=e[t];var f="z";var u="";var d=Fa({c:l+p,r:s+r+n});c=fc(i,d);if(o&&typeof o==="object"&&!(o instanceof Date)){i[d]=o}else{if(typeof o=="number")f="n";else if(typeof o=="boolean")f="b";else if(typeof o=="string")f="s";else if(o instanceof Date){f="d";if(!a.cellDates){f="n";o=sr(o)}u=a.dateNF||X[14]}else if(o===null&&a.nullError){f="e";o=0}if(!c)i[d]=c={t:f,v:o};else{c.t=f;c.v=o;delete c.w;delete c.R;if(u)c.z=u}if(u)c.z=u}})});f.e.c=Math.max(f.e.c,l+h.length-1);var d=ya(s);if(n)for(p=0;p<h.length;++p)i[Ca(p+l)+d]={t:"s",v:h[p]};i["!ref"]=Oa(f);return i}function cc(e,r){return oc(null,e,r)}function fc(e,r,t){if(typeof r=="string"){if(Array.isArray(e)){var a=Ea(r);if(!e[a.r])e[a.r]=[];return e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[r]||(e[r]={t:"z"})}if(typeof r!="number")return fc(e,Fa(r));return fc(e,Fa({r:r,c:t||0}))}function uc(e,r){if(typeof r=="number"){if(r>=0&&e.SheetNames.length>r)return r;throw new Error("Cannot find sheet # "+r)}else if(typeof r=="string"){var t=e.SheetNames.indexOf(r);if(t>-1)return t;throw new Error("Cannot find sheet name |"+r+"|")}else throw new Error("Cannot find sheet |"+r+"|")}function hc(){return{SheetNames:[],Sheets:{}}}function pc(e,r,t,a){var n=1;if(!t)for(;n<=65535;++n,t=undefined)if(e.SheetNames.indexOf(t="Sheet"+n)==-1)break;if(!t||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(t)>=0){var i=t.match(/(^.*?)(\d+)$/);n=i&&+i[2]||0;var s=i&&i[1]||t;for(++n;n<=65535;++n)if(e.SheetNames.indexOf(t=s+n)==-1)break}Tl(t);if(e.SheetNames.indexOf(t)>=0)throw new Error("Worksheet with name |"+t+"| already exists!");e.SheetNames.push(t);e.Sheets[t]=r;return t}function dc(e,r,t){if(!e.Workbook)e.Workbook={};if(!e.Workbook.Sheets)e.Workbook.Sheets=[];var a=uc(e,r);if(!e.Workbook.Sheets[a])e.Workbook.Sheets[a]={};switch(t){case 0:;case 1:;case 2:break;default:throw new Error("Bad sheet visibility setting "+t);}e.Workbook.Sheets[a].Hidden=t}function mc(e,r){e.z=r;return e}function vc(e,r,t){if(!r){delete e.l}else{e.l={Target:r};if(t)e.l.Tooltip=t}return e}function gc(e,r,t){return vc(e,"#"+r,t)}function bc(e,r,t){if(!e.c)e.c=[];e.c.push({t:r,a:t||"SheetJS"})}function wc(e,r,t,a){var n=typeof r!="string"?r:Ma(r);var i=typeof r=="string"?r:Oa(r);for(var s=n.s.r;s<=n.e.r;++s)for(var l=n.s.c;l<=n.e.c;++l){var o=fc(e,s,l);o.t="n";o.F=i;delete o.v;if(s==n.s.r&&l==n.s.c){o.f=t;if(a)o.D=true}}return e}var yc={encode_col:Ca,encode_row:ya,encode_cell:Fa,encode_range:Oa,decode_col:Sa,decode_row:wa,split_cell:Ta,decode_cell:Ea,decode_range:Da,format_cell:Pa,sheet_add_aoa:Ra,sheet_add_json:oc,sheet_add_dom:io,aoa_to_sheet:La,json_to_sheet:cc,table_to_sheet:so,table_to_book:lo,sheet_to_csv:ic,sheet_to_txt:sc,sheet_to_json:tc,sheet_to_html:no,sheet_to_formulae:lc,sheet_to_row_object_array:tc,sheet_get_cell:fc,book_new:hc,book_append_sheet:pc,book_set_sheet_visibility:dc,cell_set_number_format:mc,cell_set_hyperlink:vc,cell_set_internal_link:gc,cell_add_comment:bc,sheet_set_array_formula:wc,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};if(typeof parse_xlscfb!=="undefined")e.parse_xlscfb=parse_xlscfb;e.parse_zip=Ao;e.read=$o;e.readFile=zo;e.readFileSync=zo;e.write=Ko;e.writeFile=Zo;e.writeFileSync=Zo;e.writeFileAsync=ec;e.utils=yc;e.writeXLSX=Jo;e.writeFileXLSX=Qo;e.SSF=We;if(typeof __stream!=="undefined")e.stream=__stream;if(typeof Ye!=="undefined")e.CFB=Ye;if(typeof require!=="undefined"){var kc=undefined;if((kc||{}).Readable)set_readable(kc.Readable)}}if(typeof exports!=="undefined")make_xlsx_lib(exports);else if(typeof module!=="undefined"&&module.exports)make_xlsx_lib(module.exports);else if(typeof define==="function"&&define.amd)define("xlsx",function(){if(!XLSX.version)make_xlsx_lib(XLSX);return XLSX});else make_xlsx_lib(XLSX);if(typeof window!=="undefined"&&!window.XLSX)try{window.XLSX=XLSX}catch(e){}
