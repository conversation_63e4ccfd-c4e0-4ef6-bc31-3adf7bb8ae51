from django.urls import path
from . import views

app_name = 'da<PERSON><PERSON><PERSON><PERSON><PERSON>'

urlpatterns = [
    # Index
    path('', views.index, name='index'),
    
    # API chung
    path('api/get-other-category-data/', views.get_category_data, name='api_get_other_category_data'),
    
    # Tỉnh
    path('tinh/', views.tinh_list, name='tinh_list'),
    path('tinh/api/', views.tinh_list_api, name='tinh_list_api'),
    path('tinh/create/', views.tinh_create, name='tinh_create'),
    path('tinh/<int:pk>/edit/', views.tinh_edit, name='tinh_edit'),
    path('tinh/<int:pk>/delete/', views.tinh_delete, name='tinh_delete'),
    path('tinh/<int:pk>/toggle-hieu-luc/', views.tinh_toggle_hieu_luc, name='tinh_toggle_hieu_luc'),
    path('tinh/export/', views.tinh_export, name='tinh_export'),
    path('tinh/import/', views.tinh_import, name='tinh_import'),
    
    # Quận huyện
    path('quanhuyen/', views.quanhuyen_list, name='quanhuyen_list'),
    path('quanhuyen/api/', views.quanhuyen_list_api, name='quanhuyen_list_api'),
    path('quanhuyen/create/', views.quanhuyen_create, name='quanhuyen_create'),
    path('quanhuyen/<int:pk>/edit/', views.quanhuyen_edit, name='quanhuyen_edit'),
    path('quanhuyen/<int:pk>/delete/', views.quanhuyen_delete, name='quanhuyen_delete'),
    path('quanhuyen/<int:pk>/toggle-hieu-luc/', views.quanhuyen_toggle_hieu_luc, name='quanhuyen_toggle_hieu_luc'),
    path('quanhuyen/export/', views.quanhuyen_export, name='quanhuyen_export'),
    path('quanhuyen/import/', views.quanhuyen_import, name='quanhuyen_import'),
    
    # Xã phường
    path('xaphuong/', views.xaphuong_list, name='xaphuong_list'),
    path('xaphuong/api/', views.xaphuong_list_api, name='xaphuong_list_api'),
    path('xaphuong/create/', views.xaphuong_create, name='xaphuong_create'),
    path('xaphuong/<int:pk>/edit/', views.xaphuong_edit, name='xaphuong_edit'),
    path('xaphuong/<int:pk>/delete/', views.xaphuong_delete, name='xaphuong_delete'),
    path('xaphuong/<int:pk>/toggle-hieu-luc/', views.xaphuong_toggle_hieu_luc, name='xaphuong_toggle_hieu_luc'),
    path('xaphuong/export/', views.xaphuong_export, name='xaphuong_export'),
    path('xaphuong/import/', views.xaphuong_import, name='xaphuong_import'),

    # Nơi đăng kí ban đầu
    path('noi-dkbd/', views.noi_dkbd_list, name='noi_dkbd_list'),
    path('noi-dkbd/api/', views.noi_dkbd_list_api, name='noi_dkbd_list_api'),
    path('noi-dkbd/create/', views.noi_dkbd_create, name='noi_dkbd_create'),
    path('noi-dkbd/<int:pk>/edit/', views.noi_dkbd_edit, name='noi_dkbd_edit'),
    path('noi-dkbd/<int:pk>/delete/', views.noi_dkbd_delete, name='noi_dkbd_delete'),
    path('noi-dkbd/<int:pk>/toggle-hieu-luc/', views.noi_dkbd_toggle_hieu_luc, name='noi_dkbd_toggle_hieu_luc'),
    path('noi-dkbd/export/', views.noi_dkbd_export, name='noi_dkbd_export'),
    path('noi-dkbd/import/', views.noi_dkbd_import, name='noi_dkbd_import'),

    # Phạm vi chuyên môn
    path('pvcm/', views.pvcm_list, name='pvcm_list'),
    path('pvcm/api/', views.pvcm_list_api, name='pvcm_list_api'),
    path('pvcm/create/', views.pvcm_create, name='pvcm_create'),
    path('pvcm/<int:pk>/edit/', views.pvcm_edit, name='pvcm_edit'),
    path('pvcm/<int:pk>/delete/', views.pvcm_delete, name='pvcm_delete'),
    path('pvcm/<int:pk>/toggle-hieu-luc/', views.pvcm_toggle_hieu_luc, name='pvcm_toggle_hieu_luc'),
    path('pvcm/export/', views.pvcm_export, name='pvcm_export'),
    path('pvcm/import/', views.pvcm_import, name='pvcm_import'),

    # Bulk operations
    path('tinh/bulk-delete/', views.tinh_bulk_delete, name='tinh_bulk_delete'),
    path('tinh/bulk-toggle/', views.tinh_bulk_toggle, name='tinh_bulk_toggle'),
    path('quanhuyen/bulk-delete/', views.quanhuyen_bulk_delete, name='quanhuyen_bulk_delete'),
    path('quanhuyen/bulk-toggle/', views.quanhuyen_bulk_toggle, name='quanhuyen_bulk_toggle'),
    path('xaphuong/bulk-delete/', views.xaphuong_bulk_delete, name='xaphuong_bulk_delete'),
    path('xaphuong/bulk-toggle/', views.xaphuong_bulk_toggle, name='xaphuong_bulk_toggle'),
    path('noi-dkbd/bulk-delete/', views.noi_dkbd_bulk_delete, name='noi_dkbd_bulk_delete'),
    path('noi-dkbd/bulk-toggle/', views.noi_dkbd_bulk_toggle, name='noi_dkbd_bulk_toggle'),
    path('pvcm/bulk-delete/', views.pvcm_bulk_delete, name='pvcm_bulk_delete'),
    path('pvcm/bulk-toggle/', views.pvcm_bulk_toggle, name='pvcm_bulk_toggle'),
]
