from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from .models import (
    <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, NoiDangKiBanDau, PhamViChuyenMon
)

class BaseModelForm(forms.ModelForm):
    """Base form with common validation"""
    
    hieu_luc = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'custom-control-input'
        }),
        label=_('Hiệu lực'),
        help_text=_('Cho phép sử dụng trong hệ thống')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID động cho checkbox hiệu lực
        if 'hieu_luc' in self.fields:
            model_name = self._meta.model.__name__.lower()
            self.fields['hieu_luc'].widget.attrs['id'] = f'id_{model_name}_hieu_luc'
    
    def clean(self):
        cleaned_data = super().clean()
        # Add any common validation here
        return cleaned_data

    class Meta:
        fields = ['hieu_luc']

# Form cho tỉnh
class TinhForm(BaseModelForm):
    """Form cho quản lý tỉnh"""
    ma_tinh = forms.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex='^[0-9]{2}$',
                message=_('Mã tỉnh phải là 2 chữ số')
            )
        ],
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập mã tỉnh (2 chữ số)'),
            'maxlength': '2'
        }),
        label=_('Mã tỉnh')
    )
    ten_tinh = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập tên tỉnh/thành phố')
        }),
        label=_('Tên tỉnh/thành phố')
    )

    class Meta:
        model = Tinh
        fields = ['ma_tinh', 'ten_tinh', 'hieu_luc']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID cụ thể cho form tỉnh
        self.fields['hieu_luc'].widget.attrs['id'] = 'id_hieu_luc'

    def clean_ma_tinh(self):
        ma_tinh = self.cleaned_data.get('ma_tinh')
        if ma_tinh:
            # Kiểm tra trùng lặp (trừ instance hiện tại nếu đang edit)
            queryset = Tinh.objects.filter(ma_tinh=ma_tinh)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('Mã tỉnh này đã tồn tại'))
        return ma_tinh

# Form cho Quận huyện
class QuanHuyenForm(BaseModelForm):
    """Form cho quản lý huyện"""
    ma_quan_huyen = forms.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex='^[0-9]{3}$',
                message=_('Mã quận huyện phải là 3 chữ số')
            )
        ],
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập mã quận huyện (3 chữ số)'),
            'maxlength': '3'
        }),
        label=_('Mã quận huyện')
    )
    ten_quan_huyen = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập tên quận huyện')
        }),
        label=_('Tên quận huyện')
    )
    ma_tinh = forms.ModelChoiceField(
        queryset=Tinh.objects.filter(hieu_luc=True),
        widget=forms.Select(attrs={
            'class': 'form-control select2',
            'data-placeholder': _('Chọn tỉnh/thành phố')
        }),
        label=_('Tỉnh/Thành phố'),
        empty_label=_('-- Chọn tỉnh/thành phố --')
    )

    class Meta:
        model = QuanHuyen
        fields = ['ma_tinh', 'ma_quan_huyen', 'ten_quan_huyen', 'hieu_luc']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID cụ thể cho form quận huyện
        self.fields['hieu_luc'].widget.attrs['id'] = 'id_quanhuyen_hieu_luc'

    def clean_ma_quan_huyen(self):
        ma_quan_huyen = self.cleaned_data.get('ma_quan_huyen')
        if ma_quan_huyen:
            # Kiểm tra trùng lặp (trừ instance hiện tại nếu đang edit)
            queryset = QuanHuyen.objects.filter(ma_quan_huyen=ma_quan_huyen)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('Mã quận huyện này đã tồn tại'))
        return ma_quan_huyen

# Form cho Xã phường
class XaPhuongForm(BaseModelForm):
    """Form cho quản lý xã phường"""
    ma_xa_phuong = forms.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex='^[0-9]{5}$',
                message=_('Mã xã phường phải là 5 chữ số')
            )
        ],
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập mã xã phường (5 chữ số)'),
            'maxlength': '5'
        }),
        label=_('Mã xã phường')
    )
    ten_xa_phuong = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập tên xã phường')
        }),
        label=_('Tên xã phường')
    )
    ma_tinh = forms.ModelChoiceField(
        queryset=Tinh.objects.filter(hieu_luc=True),
        widget=forms.Select(attrs={
            'class': 'form-control select2',
            'data-placeholder': _('Chọn tỉnh/thành phố')
        }),
        label=_('Tỉnh/Thành phố'),
        empty_label=_('-- Chọn tỉnh/thành phố --')
    )
    ma_quan_huyen = forms.ModelChoiceField(
        queryset=QuanHuyen.objects.filter(hieu_luc=True),
        widget=forms.Select(attrs={
            'class': 'form-control select2',
            'data-placeholder': _('Chọn quận huyện')
        }),
        label=_('Quận/Huyện'),
        empty_label=_('-- Chọn quận/huyện --')
    )

    class Meta:
        model = XaPhuong
        fields = ['ma_tinh', 'ma_quan_huyen', 'ma_xa_phuong', 'ten_xa_phuong', 'hieu_luc']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID cụ thể cho form xã phường
        self.fields['hieu_luc'].widget.attrs['id'] = 'id_xaphuong_hieu_luc'
        
        # Filter quận huyện dựa trên tỉnh được chọn
        if 'ma_tinh' in self.data:
            try:
                ma_tinh_id = int(self.data.get('ma_tinh'))
                self.fields['ma_quan_huyen'].queryset = QuanHuyen.objects.filter(
                    ma_tinh_id=ma_tinh_id,
                    hieu_luc=True
                ).order_by('ma_quan_huyen')
            except (ValueError, TypeError):
                self.fields['ma_quan_huyen'].queryset = QuanHuyen.objects.none()
        elif self.instance.pk and self.instance.ma_tinh:
            self.fields['ma_quan_huyen'].queryset = QuanHuyen.objects.filter(
                ma_tinh=self.instance.ma_tinh,
                hieu_luc=True
            ).order_by('ma_quan_huyen')
        else:
            self.fields['ma_quan_huyen'].queryset = QuanHuyen.objects.none()

    def clean_ma_xa_phuong(self):
        ma_xa_phuong = self.cleaned_data.get('ma_xa_phuong')
        if ma_xa_phuong:
            # Kiểm tra trùng lặp (trừ instance hiện tại nếu đang edit)
            queryset = XaPhuong.objects.filter(ma_xa_phuong=ma_xa_phuong)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('Mã xã phường này đã tồn tại'))
        return ma_xa_phuong

    def clean(self):
        cleaned_data = super().clean()
        ma_tinh = cleaned_data.get('ma_tinh')
        ma_quan_huyen = cleaned_data.get('ma_quan_huyen')

        # Kiểm tra xem quận huyện có thuộc tỉnh được chọn không
        if ma_tinh and ma_quan_huyen:
            if ma_quan_huyen.ma_tinh != ma_tinh:
                raise forms.ValidationError({
                    'ma_quan_huyen': _('Quận/huyện này không thuộc tỉnh/thành phố đã chọn')
                })

        return cleaned_data
    
# Form cho nơi đăng kí ban đầu
class NoiDangKiBanDauForm(BaseModelForm):
    """Form cho quản lý nơi đăng kí ban đầu"""
    ma = forms.CharField(
        max_length=5,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập mã nơi đăng kí ban đầu'),
            'maxlength': '5'
        }),
        label=_('Mã')
    )

    ten = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập tên nơi đăng kí ban đầu'),
            'maxlength': '5'
        }),
        label=_('Tên')
    )

    tuyen_cmkt = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập tuyến CMKT')
        }),
        label=_('Tên tuyến CMKT')
    )

    hang_benh_vien = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập hạng bệnh viện')
        }),
        label=_('Tên hàng bệnh viện')
    )

    cap_cmkt = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập cấp CMKT')
        }),
        label=_('Cấp CMKT')
    )

    diem = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập điểm')
        }),
        label=_('Điểm')
    )

    dia_chi = forms.CharField(
        max_length=250,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập địa chỉ')
        }),
        label=_('Địa chỉ')
    )

    class Meta:
        model = NoiDangKiBanDau
        fields = ['ma', 'ten', 'tuyen_cmkt', 'hang_benh_vien', 'cap_cmkt', 'diem', 'dia_chi', 'hieu_luc']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID cụ thể cho form nơi đăng kí ban đầu
        self.fields['hieu_luc'].widget.attrs['id'] = 'id_hieu_luc'
        self.fields['tuyen_cmkt'].required = False
        self.fields['hang_benh_vien'].required = False
        self.fields['cap_cmkt'].required = False
        self.fields['diem'].required = False
        self.fields['dia_chi'].required = False

    def clean_ma_dkbd(self):
        ma_dkbd = self.cleaned_data.get('ma')
        if ma_dkbd:
            # Kiểm tra trùng lặp (trừ instance hiện tại nếu đang edit)
            queryset = NoiDangKiBanDau.objects.filter(ma=ma_dkbd)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('Mã ĐKBĐ này đã tồn tại'))
        return ma_dkbd

# Form cho Phạm vi chuyên môn
class PhamViChuyenMonForm(BaseModelForm):
    """Form cho quản lý tỉnh"""
    ma_pham_vi = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập mã phạm vi'),
        }),
        label=_('Mã phạm vi')
    )
    ten_chuyen_khoa = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Tên chuyên khoa')
        }),
        label=_('Tên chuyên khoa')
    )

    class Meta:
        model = PhamViChuyenMon
        fields = ['ma_pham_vi', 'ten_chuyen_khoa', 'hieu_luc']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Đặt ID cụ thể cho form tỉnh
        self.fields['hieu_luc'].widget.attrs['id'] = 'id_hieu_luc'

    def clean_ma_pham_vi(self):
        ma_pham_vi = self.cleaned_data.get('ma_pham_vi')
        if ma_pham_vi:
            # Kiểm tra trùng lặp (trừ instance hiện tại nếu đang edit)
            queryset = PhamViChuyenMon.objects.filter(ma_pham_vi=ma_pham_vi)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('Mã Phạm vi chuyên môn này đã tồn tại'))
        return ma_pham_vi

# Form tìm kiếm chung
class SearchForm(forms.Form):
    """Form tìm kiếm chung"""
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Nhập từ khóa tìm kiếm...'),
            'autocomplete': 'off'
        }),
        label=_('Tìm kiếm')
    )
    
    hieu_luc = forms.ChoiceField(
        choices=[
            ('', _('Tất cả')),
            ('1', _('Có hiệu lực')),
            ('0', _('Không hiệu lực'))
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label=_('Trạng thái')
    )