#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run Django with Uvicorn ASGI server
This script runs Django with Uvicorn to support WebSocket
Enhanced with network diagnostics and hostname resolution fixes
"""

import os
import sys
import subprocess
import time
import platform
import socket
import ipaddress
import tempfile

def check_uvicorn_installed():
    """Check if Uvicorn is installed"""
    try:
        __import__('uvicorn')
        return True
    except ImportError:
        return False

def install_uvicorn():
    """Install Uvicorn"""
    print("Installing Uvicorn...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "uvicorn"])
    print("Uvicorn installed successfully")

def get_local_ip():
    """Get the local IP address of the machine"""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
        return local_ip
    except Exception:
        return "127.0.0.1"

def get_all_network_interfaces():
    """Get all network interfaces and their IPs"""
    interfaces = []
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            current_adapter = None
            
            for line in lines:
                line = line.strip()
                if 'adapter' in line.lower() and ':' in line:
                    current_adapter = line
                elif 'IPv4 Address' in line and current_adapter:
                    ip = line.split(':')[-1].strip()
                    if ip and not ip.startswith('127.'):
                        interfaces.append((current_adapter, ip))
        else:
            # Linux/Mac
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            ips = result.stdout.strip().split()
            for ip in ips:
                if not ip.startswith('127.'):
                    interfaces.append(('Network', ip))
    except Exception as e:
        print(f"Error getting network interfaces: {e}")
    
    return interfaces

def check_port_available(host, port):
    """Check if a port is available for binding"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind((host, port))
            return True
    except OSError as e:
        print(f"Port {port} is not available on {host}: {e}")
        return False

def check_firewall_windows(port):
    """Check Windows Firewall rules for the port"""
    try:
        # Check if there's a firewall rule allowing the port
        cmd = f'netsh advfirewall firewall show rule name="Django-{port}" dir=in'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if "No rules match" in result.stdout:
            print(f"No firewall rule found for port {port}")
            return False
        else:
            print(f"Firewall rule exists for port {port}")
            return True
    except Exception as e:
        print(f"Error checking firewall: {e}")
        return False

def add_firewall_rule_windows(port):
    """Add Windows Firewall rule for the port"""
    try:
        print(f"Adding Windows Firewall rule for port {port}...")
        cmd = f'netsh advfirewall firewall add rule name="Django-{port}" dir=in action=allow protocol=TCP localport={port}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=True)
        print(f"Firewall rule added successfully for port {port}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to add firewall rule (you may need to run as administrator): {e}")
        return False
    except Exception as e:
        print(f"Error adding firewall rule: {e}")
        return False

def kill_process_on_port(port):
    """Kill process running on a specific port"""
    print(f"Checking for processes using port {port}...")

    if platform.system() == "Windows":
        find_pid_cmd = f"netstat -ano | findstr :{port}"
        try:
            output = subprocess.check_output(find_pid_cmd, shell=True).decode('utf-8')
            if output:
                for line in output.splitlines():
                    if f":{port}" in line and "LISTENING" in line:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[4]
                            print(f"Found process with PID {pid} using port {port}")
                            try:
                                subprocess.call(f"taskkill /F /PID {pid}", shell=True)
                                print(f"Terminated process with PID {pid}")
                                return True
                            except Exception as e:
                                print(f"Error killing process: {e}")
                                return False
        except Exception as e:
            print(f"Error finding process: {e}")
            return False
    else:
        try:
            find_pid_cmd = f"lsof -i :{port} -t"
            pid = subprocess.check_output(find_pid_cmd, shell=True).decode('utf-8').strip()
            if pid:
                subprocess.call(f"kill -9 {pid}", shell=True)
                print(f"Terminated process with PID {pid}")
                return True
        except Exception as e:
            print(f"Error finding or killing process: {e}")
            return False

    return False

def test_hostname_resolution():
    """Test if bvqy211.local resolves correctly"""
    print("\n" + "="*50)
    print("HOSTNAME RESOLUTION TEST")
    print("="*50)
    
    try:
        ip = socket.gethostbyname('bvqy211.local')
        local_ip = get_local_ip()
        print(f"bvqy211.local resolves to: {ip}")
        print(f"Local machine IP: {local_ip}")
        
        if ip == local_ip:
            print("✓ Hostname resolution is correct on this machine")
            return True
        elif ip == "127.0.0.1":
            print("⚠ PROBLEM FOUND: bvqy211.local resolves to localhost (127.0.0.1)")
            print("  This means other machines cannot access the server!")
            return False
        else:
            print("⚠ Hostname resolution mismatch!")
            print("You may need to update your hosts file or DNS settings")
            return False
    except socket.gaierror:
        print("✗ bvqy211.local does not resolve")
        print("You need to add it to your hosts file or DNS server")
        return False

def generate_hosts_file_entries():
    """Generate hosts file entries for all machines"""
    local_ip = get_local_ip()
    interfaces = get_all_network_interfaces()
    
    print("\n" + "="*50)
    print("HOSTS FILE CONFIGURATION")
    print("="*50)
    
    print(f"Your server's network information:")
    print(f"Primary IP: {local_ip}")
    
    if interfaces:
        print("All network interfaces:")
        for adapter, ip in interfaces:
            print(f"  {ip} - {adapter}")
    
    print(f"\n🔧 SOLUTION: Update hosts files on ALL machines")
    print("="*50)
    
    # For the server machine
    print(f"\n1. ON THIS SERVER MACHINE (bvqy211):")
    print(f"   Edit: C:\\Windows\\System32\\drivers\\etc\\hosts")
    print(f"   Add this line:")
    print(f"   {local_ip} bvqy211.local")
    print(f"   (Replace any existing 127.0.0.1 bvqy211.local line)")
    
    # For client machines
    print(f"\n2. ON ALL CLIENT MACHINES:")
    print(f"   Edit: C:\\Windows\\System32\\drivers\\etc\\hosts")
    print(f"   Add this line:")
    print(f"   {local_ip} bvqy211.local")
    
    # Create a sample hosts file
    hosts_content = f"""# Django Server Configuration
# Add this line to your hosts file on ALL machines
{local_ip} bvqy211.local

# How to edit hosts file:
# 1. Open Notepad as Administrator
# 2. Open: C:\\Windows\\System32\\drivers\\etc\\hosts
# 3. Add the line above
# 4. Save the file
"""
    
    # Save to a temporary file for easy copying
    try:
        temp_file = os.path.join(tempfile.gettempdir(), "bvqy211_hosts_entry.txt")
        with open(temp_file, 'w') as f:
            f.write(hosts_content)
        print(f"\n📄 Hosts file entry saved to: {temp_file}")
        print("   You can copy this file to other machines for easy setup")
    except Exception as e:
        print(f"Could not save hosts file entry: {e}")
    
    return local_ip

def test_network_connectivity(local_ip, port=8000):
    """Test network connectivity"""
    print("\n" + "="*50)
    print("NETWORK CONNECTIVITY TEST")
    print("="*50)
    
    print("Testing server binding...")
    if check_port_available("0.0.0.0", port):
        print(f"✓ Port {port} is available for binding")
    else:
        print(f"✗ Port {port} is not available")
        return False
    
    print(f"\nServer will be accessible via these URLs:")
    print(f"  ✓ Local access:     http://localhost:{port}")
    print(f"  ✓ Local access:     http://127.0.0.1:{port}")
    print(f"  ✓ Network access:   http://{local_ip}:{port}")
    print(f"  ? Hostname access:  http://bvqy211.local:{port}")
    
    print(f"\n📋 TROUBLESHOOTING CHECKLIST:")
    print(f"   □ Server binds to 0.0.0.0:{port} (not 127.0.0.1)")
    print(f"   □ Windows Firewall allows port {port}")
    print(f"   □ Hosts file on server: {local_ip} bvqy211.local")
    print(f"   □ Hosts file on clients: {local_ip} bvqy211.local")
    print(f"   □ Other machines can ping {local_ip}")
    print(f"   □ Other machines can ping bvqy211.local")
    
    return True

def run_network_diagnostics(port=8000):
    """Run comprehensive network diagnostics"""
    print("\n" + "="*50)
    print("COMPREHENSIVE NETWORK DIAGNOSTICS")
    print("="*50)
    
    # Get local IP
    local_ip = get_local_ip()
    print(f"Local IP Address: {local_ip}")
    
    # Test hostname resolution
    hostname_ok = test_hostname_resolution()
    
    # Generate hosts file entries
    generate_hosts_file_entries()
    
    # Test network connectivity
    test_network_connectivity(local_ip, port)
    
    # Check firewall (Windows only)
    if platform.system() == "Windows":
        print(f"\nChecking Windows Firewall for port {port}...")
        if not check_firewall_windows(port):
            print(f"⚠ No firewall rule found for port {port}")
            response = input(f"Would you like to add a firewall rule for port {port}? (y/n): ")
            if response.lower() == 'y':
                add_firewall_rule_windows(port)
        else:
            print(f"✓ Firewall rule exists for port {port}")
    
    # Final recommendations
    print(f"\n" + "="*50)
    print("NEXT STEPS")
    print("="*50)
    
    if not hostname_ok:
        print("🚨 CRITICAL: Fix hostname resolution first!")
        print("1. Update hosts file on this server")
        print("2. Update hosts file on all client machines")
        print("3. Test: ping bvqy211.local from client machines")
        print("4. Restart this script")
    else:
        print("✓ Hostname resolution looks good")
        print("✓ Ready to start server")
    
    print(f"\n🔗 After starting server, test these URLs:")
    print(f"   From server: http://bvqy211.local:{port}")
    print(f"   From clients: http://bvqy211.local:{port}")
    print(f"   Direct IP: http://{local_ip}:{port}")

def run_uvicorn():
    """Run Django with Uvicorn"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Change to the project root directory (two levels up from script)
    project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
    os.chdir(project_root)
    
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospitalmanager.settings')
    
    # Run comprehensive network diagnostics
    run_network_diagnostics()
    
    # Kill any existing processes
    kill_process_on_port(6081)
    kill_process_on_port(8000)
    
    # Wait a moment for the port to be released
    time.sleep(2)
    
    # Final port check
    if not check_port_available("0.0.0.0", 8000):
        print("Error: Port 8000 is still not available!")
        return
    
    print("\n" + "="*50)
    print("STARTING DJANGO SERVER")
    print("="*50)
    print("Server will be available at http://0.0.0.0:8000")
    print("Press Ctrl+C to stop the server")
    print(f"Working directory: {os.getcwd()}")
    
    # Set environment variables
    print("Setting up environment variables...")
    os.environ["UVICORN_LOG_LEVEL"] = "info"
    os.environ["DJANGO_LOG_LEVEL"] = "info"
    os.environ["CHANNELS_LOG_LEVEL"] = "info"
    
    # Run Uvicorn with improved settings
    try:
        subprocess.call([
            sys.executable,
            "-m",
            "uvicorn",
            "hospitalmanager.asgi:application",
            "--host", "0.0.0.0",  # Bind to all interfaces
            "--port", "8000",
            "--reload",
            "--log-level", "info",
            "--access-log",  # Enable access logging
            "--use-colors",  # Enable colored output
        ])
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")

if __name__ == "__main__":
    # Check if Uvicorn is installed
    if not check_uvicorn_installed():
        install_uvicorn()
    
    # Run Uvicorn
    run_uvicorn()