{% extends 'layouts/base.html' %}

{% block title %}{{ title }} | Hospital Manager{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'permissions:user_role_list' %}">Quản lý phân quyền người dùng</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary">
      <div class="card-header">
        <h3 class="card-title">Thông tin phân quyền</h3>
      </div>
      <!-- /.card-header -->
      <!-- form start -->
      <form role="form" method="post">
        {% csrf_token %}
        <div class="card-body">
          {% if form.non_field_errors %}
          <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}

          {% if messages %}
          <div class="alert alert-success">
            {% for message in messages %}
              {{ message }}
            {% endfor %}
          </div>
          {% endif %}

          <div class="row">
            <!-- Thông tin cơ bản - 1/3 bên trái -->
            <div class="col-md-4">
              <div class="form-group">
                <label for="{{ form.user.id_for_label }}">Người dùng</label>
                {{ form.user }}
                {% if form.user.errors %}
                <div class="text-danger">
                  {% for error in form.user.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>

            <!-- Vai trò - 2/3 bên phải -->
            <div class="col-md-8">
              <div class="form-group">
                <label>Vai trò</label>
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">Chọn vai trò cho người dùng</h3>
                  </div>
                  <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <div class="row">
                      {% for role in available_roles %}
                      <div class="col-md-6">
                        <div class="form-check">
                          <input type="radio" class="form-check-input" id="role_{{ role.id }}"
                                 name="role" value="{{ role.id }}"
                                 {% if selected_role == role.id %}checked{% endif %}>
                          <label class="form-check-label" for="role_{{ role.id }}">
                            {{ role.name }}
                          </label>
                          <p class="text-muted small">{{ role.description|default:"" }}</p>
                          <p class="text-muted small">Số quyền: {{ role.permissions.count }}</p>
                        </div>
                      </div>
                      {% empty %}
                      <div class="col-md-12">
                        <p class="text-muted">Không có vai trò nào được định nghĩa.</p>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
                {% if form.role.errors %}
                <div class="text-danger">
                  {% for error in form.role.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        <!-- /.card-body -->

        <div class="card-footer">
          <button type="submit" class="btn btn-primary">Lưu</button>
          <a href="{% url 'permissions:user_role_list' %}" class="btn btn-default">Hủy</a>
        </div>
      </form>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    $('.select2').select2();
  });
</script>
{% endblock %}
