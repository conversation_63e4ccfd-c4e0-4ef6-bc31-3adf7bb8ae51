{% extends 'layouts/base.html' %}

{% block title %}Quản lý khoa phòng và nhân sự | LAN Insight Guardian{% endblock %}

{% block page_title %}Quản lý khoa phòng và nhân sự{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Quản lý khoa phòng và nhân sự</li>
{% endblock %}

{% block content %}
<!-- Info boxes -->
<div class="row">
  <div class="col-12 col-sm-6 col-md-4">
    <div class="info-box">
      <span class="info-box-icon bg-info elevation-1"><i class="fas fa-building"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">Khoa/Phòng ban</span>
        <span class="info-box-number">
          {{ total_departments }}
          <small>đơn vị</small>
        </span>
      </div>
      <!-- /.info-box-content -->
    </div>
    <!-- /.info-box -->
  </div>
  <!-- /.col -->
  <div class="col-12 col-sm-6 col-md-4">
    <div class="info-box mb-3">
      <span class="info-box-icon bg-success elevation-1"><i class="fas fa-user-tie"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">Chức vụ</span>
        <span class="info-box-number">{{ total_positions }}</span>
      </div>
      <!-- /.info-box-content -->
    </div>
    <!-- /.info-box -->
  </div>
  <!-- /.col -->

  <!-- fix for small devices only -->
  <div class="clearfix hidden-md-up"></div>

  <div class="col-12 col-sm-6 col-md-4">
    <div class="info-box mb-3">
      <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-users"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">Nhân viên</span>
        <span class="info-box-number">{{ total_staff }}</span>
      </div>
      <!-- /.info-box-content -->
    </div>
    <!-- /.info-box -->
  </div>
  <!-- /.col -->
</div>
<!-- /.row -->

<div class="row">
  <div class="col-md-6">
    <!-- Departments with most staff -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Khoa/Phòng ban có nhiều nhân viên nhất</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body p-0">
        <ul class="products-list product-list-in-card pl-2 pr-2">
          {% for department in departments_with_staff %}
          <li class="item">
            <div class="product-img">
              <i class="fas fa-building fa-2x text-info"></i>
            </div>
            <div class="product-info">
              <a href="{% url 'departments:department_detail' department.pk %}" class="product-title">
                {{ department.name }}
                <span class="badge badge-info float-right">{{ department.staff_count }} nhân viên</span>
              </a>
              <span class="product-description">
                {{ department.description|truncatechars:100 }}
              </span>
            </div>
          </li>
          {% empty %}
          <li class="item">
            <div class="product-info">
              <span class="product-description text-center py-3">
                Chưa có dữ liệu khoa/phòng ban
              </span>
            </div>
          </li>
          {% endfor %}
        </ul>
      </div>
      <!-- /.card-body -->
      <div class="card-footer text-center">
        <a href="{% url 'departments:department_list' %}" class="uppercase">Xem tất cả khoa/phòng ban</a>
      </div>
      <!-- /.card-footer -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->

  <div class="col-md-6">
    <!-- Positions with most staff -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Chức vụ có nhiều nhân viên nhất</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body p-0">
        <ul class="products-list product-list-in-card pl-2 pr-2">
          {% for position in positions_with_staff %}
          <li class="item">
            <div class="product-img">
              <i class="fas fa-user-tie fa-2x text-success"></i>
            </div>
            <div class="product-info">
              <a href="{% url 'departments:position_detail' position.pk %}" class="product-title">
                {{ position.name }}
                <span class="badge badge-success float-right">{{ position.staff_count }} nhân viên</span>
              </a>
              <span class="product-description">
                {{ position.description|truncatechars:100 }}
              </span>
            </div>
          </li>
          {% empty %}
          <li class="item">
            <div class="product-info">
              <span class="product-description text-center py-3">
                Chưa có dữ liệu chức vụ
              </span>
            </div>
          </li>
          {% endfor %}
        </ul>
      </div>
      <!-- /.card-body -->
      <div class="card-footer text-center">
        <a href="{% url 'departments:position_list' %}" class="uppercase">Xem tất cả chức vụ</a>
      </div>
      <!-- /.card-footer -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
</div>
<!-- /.row -->

<div class="row">
  <div class="col-md-12">
    <!-- Recent staff -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Nhân viên mới thêm gần đây</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table m-0">
            <thead>
              <tr>
                <th>Mã NV</th>
                <th>Họ tên</th>
                <th>Khoa/Phòng ban</th>
                <th>Chức vụ</th>
                <th>Ngày tạo</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {% for staff in recent_staff %}
              <tr>
                <td>{{ staff.staff_id }}</td>
                <td>{{ staff.full_name }}</td>
                <td>{{ staff.department.name|default:"Chưa phân công" }}</td>
                <td>{{ staff.position.name|default:"Chưa phân công" }}</td>
                <td>{{ staff.created_at|date:"d/m/Y H:i" }}</td>
                <td>
                  <a href="{% url 'departments:staff_detail' staff.pk %}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye"></i>
                  </a>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="6" class="text-center py-3">Chưa có dữ liệu nhân viên</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <!-- /.table-responsive -->
      </div>
      <!-- /.card-body -->
      <div class="card-footer text-center">
        <a href="{% url 'departments:staff_list' %}" class="uppercase">Xem tất cả nhân viên</a>
      </div>
      <!-- /.card-footer -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
</div>
<!-- /.row -->

<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Thao tác nhanh</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <a href="{% url 'departments:department_create' %}" class="btn btn-info btn-block mb-3">
              <i class="fas fa-plus mr-2"></i> Thêm khoa/phòng ban mới
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'departments:position_create' %}" class="btn btn-success btn-block mb-3">
              <i class="fas fa-plus mr-2"></i> Thêm chức vụ mới
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'departments:staff_create' %}" class="btn btn-warning btn-block mb-3">
              <i class="fas fa-plus mr-2"></i> Thêm nhân viên mới
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
