{% extends 'layouts/base.html' %}

{% block title %}Đ<PERSON><PERSON> mật khẩu | LAN Insight Guardian{% endblock %}

{% block extra_css %}
<!-- Select2 -->
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
{% endblock %}

{% block page_title %}Đ<PERSON>i mật khẩu{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:list' %}">Quản lý người dùng</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:detail' user_id %}">Chi tiết người dùng</a></li>
<li class="breadcrumb-item active">Đổi mật khẩu</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-6">
    <div class="card card-primary">
      <div class="card-header">
        <h3 class="card-title">Đổi mật khẩu cho người dùng: {{ username }}</h3>
      </div>
      <!-- /.card-header -->
      <!-- form start -->
      <form role="form" method="post">
        {% csrf_token %}
        <div class="card-body">
          {% if form.non_field_errors %}
          <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}

          <!-- Django messages will be handled by SweetAlert2 -->

          {% if form.old_password %}
          <!-- UserPasswordChangeForm (change_password_self) -->
          <div class="form-group">
            <label for="id_old_password">Mật khẩu cũ</label>
            <input type="password" name="old_password" class="form-control {% if form.old_password.errors %}is-invalid{% endif %}" id="id_old_password" placeholder="Nhập mật khẩu cũ">
            {% if form.old_password.errors %}
            <div class="invalid-feedback">
              {% for error in form.old_password.errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}
          </div>
          {% endif %}

          <div class="form-group">
            <label for="id_new_password1">Mật khẩu mới</label>
            <input type="password" name="new_password1" class="form-control {% if form.new_password1.errors %}is-invalid{% endif %}" id="id_new_password1" placeholder="Nhập mật khẩu mới">
            {% if form.new_password1.errors %}
            <div class="invalid-feedback">
              {% for error in form.new_password1.errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}
            <small class="form-text text-muted">
              Mật khẩu phải có ít nhất 8 ký tự và không được quá đơn giản.
            </small>
          </div>

          <div class="form-group">
            <label for="id_new_password2">Xác nhận mật khẩu mới</label>
            <input type="password" name="new_password2" class="form-control {% if form.new_password2.errors %}is-invalid{% endif %}" id="id_new_password2" placeholder="Nhập lại mật khẩu mới">
            {% if form.new_password2.errors %}
            <div class="invalid-feedback">
              {% for error in form.new_password2.errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}
          </div>

        </div>
        <!-- /.card-body -->

        <div class="card-footer">
          <button type="submit" class="btn btn-primary" id="save-button">
            <i class="fas fa-key"></i> Đổi mật khẩu
          </button>
          <a href="{% url 'users:list' %}" class="btn btn-default">
            <i class="fas fa-times"></i> Hủy
          </a>
        </div>
      </form>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js"></script>
<script>
  $(function () {
    // Xử lý nút lưu thay đổi
    $('#save-button').on('click', function(e) {
      e.preventDefault();

      // Kiểm tra mật khẩu trùng khớp
      var password1 = $('#id_new_password1').val();
      var password2 = $('#id_new_password2').val();
      var oldPasswordField = $('#id_old_password');
      var needOldPassword = oldPasswordField.length > 0;

      // Kiểm tra mật khẩu cũ nếu cần
      if (needOldPassword && !oldPasswordField.val()) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng nhập mật khẩu cũ',
        });
        return;
      }

      if (!password1 || !password2) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng nhập đầy đủ mật khẩu mới và xác nhận mật khẩu mới',
        });
        return;
      }

      if (password1 !== password2) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Mật khẩu xác nhận không khớp với mật khẩu mới',
        });
        return;
      }

      // Hiển thị thông báo xác nhận
      Swal.fire({
        title: 'Xác nhận đổi mật khẩu',
        text: 'Bạn có chắc chắn muốn đổi mật khẩu cho người dùng này?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Đổi mật khẩu',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Hiển thị loading
          Swal.fire({
            title: 'Đang xử lý...',
            html: 'Vui lòng đợi trong giây lát',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });

          // Submit form
          $(this).closest('form').submit();
        }
      });
    });
  });
</script>
{% endblock %}