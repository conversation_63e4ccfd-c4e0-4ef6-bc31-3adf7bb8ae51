{% extends 'danhmuckhac/list_base.html' %}
{% load static %}

{% block title %}Danh mục phạm vi chuyên môn{% endblock %}

{% block page_title %}Phạm vi chuyên môn{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'danhmuckhac:index' %}">Danh mục khác</a></li>
<li class="breadcrumb-item active">Phạm vi chuyên môn</li>
{% endblock %}

{% block card_title %}Danh mục phạm vi chuyên môn{% endblock %}

{% block table_id %}pvcmTable{% endblock %}

{% block config_type %}pvcm{% endblock %}

{# Define form fields for Create Modal #}
{% block create_modal_title %}Thêm Phạm vi chuyên môn mới{% endblock %}

{% block create_form_fields %}
<div class="form-group">
  <label for="id_ten_chuyen_khoa">Tên chuyên khoa <span class="text-danger">*</span></label>
  <input type="text" class="form-control" id="id_ten_chuyen_khoa" name="ten_chuyen_khoa" required>
  <div class="invalid-feedback"></div>
</div>
<div class="form-group">
  <label for="id_ma_pham_vi">Mã phạm vi <span class="text-danger">*</span></label>
  <input type="text" class="form-control" id="id_ma_pham_vi" name="ma_pham_vi" required>
  <div class="invalid-feedback"></div>
</div>
<div class="form-group">
  <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
    <input type="checkbox" class="custom-control-input" id="id_hieu_luc" name="hieu_luc" value="1" checked>
    <label class="custom-control-label" for="id_hieu_luc">
      <strong>Hiệu lực</strong>
      <small class="text-muted d-block">Cho phép sử dụng trong hệ thống</small>
    </label>
  </div>
</div>
{% endblock %}

{# Define form fields for Edit Modal #}
{% block edit_modal_title %}Chỉnh sửa Phạm vi chuyên môn{% endblock %}

{% block edit_form_fields %}
<div class="form-group">
  <label for="edit_ten_chuyen_khoa">Tên PVCM <span class="text-danger">*</span></label>
  <input type="text" class="form-control" id="edit_ten_chuyen_khoa" name="ten_chuyen_khoa" required>
  <div class="invalid-feedback"></div>
</div>
<div class="form-group">
  <label for="edit_ma_pham_vi">Mã PVCM <span class="text-danger">*</span></label>
  <input type="text" class="form-control" id="edit_ma_pham_vi" name="ma_pham_vi" required>
  <div class="invalid-feedback"></div>
</div>
<div class="form-group">
  <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
    <input type="checkbox" class="custom-control-input" id="edit_hieu_luc" name="hieu_luc" value="1">
    <label class="custom-control-label" for="edit_hieu_luc">
      <strong>Hiệu lực</strong>
      <small class="text-muted d-block">Cho phép sử dụng trong hệ thống</small>
    </label>
  </div>
</div>
{% endblock %}

{# Define import instructions #}
{% block import_modal_title %}Import PVCM từ Excel{% endblock %}

{% block import_instructions %}
<p class="mb-2">File Excel cần có các cột sau:</p>
<ul class="mb-2">
  <li><strong>Cột tên PVCM</strong> (bắt buộc)</li>
  <li><strong>Cột mã PVCM</strong> (bắt buộc)</li>
  <li><strong>Cột hiệu lực</strong> (tùy chọn)</li>
</ul>
{% endblock %}

{% block preview_headers %}
<th>Mã PVCM</th>
<th>Tên phạm vi chuyên môn</th>
<th>Hiệu lực</th>
{% endblock %}

{% block custom_js %}
// Modal events
$('#createModal').on('shown.bs.modal', function() {
  console.log('📝 Create modal opened - initializing general Select2');
  const $generalSelects = $(this).find('.select2-general');
});

// Cleanup khi đóng modal
$('#createModal, #editModal').on('hidden.bs.modal', function() {
  // Reset form khi modal đóng
  $(this).find('form')[0].reset();
});

// Override loadEditData
if (window.manager) {
  window.manager.loadEditData = function(id) {
    const self = this;
    let itemData = null;

    // Cố gắng lấy dữ liệu từ Tabulator trước
    if (window.manager && window.manager.table) {
      const row = window.manager.table.getRow(id);
      if (row) {
        itemData = row.getData();
        console.log('📝 Edit data loaded from Tabulator row:', itemData);
      }
    }

    if (itemData) {
      // Nếu có dữ liệu từ Tabulator, sử dụng nó
      showEditModalWithData(itemData);
    } else {
      // Nếu không có dữ liệu từ Tabulator (hoặc để dự phòng), gọi API
      console.warn(`⚠️ Could not find data for id ${id} in Tabulator. Falling back to API call.`);
      const pvcmConfig = window.getDanhMucConfig('pham_vi_chuyen_mon'); // Sử dụng config của pham_vi_chuyen_mon
      if (!pvcmConfig || !pvcmConfig.getDataUrl) { // getDataUrl nên được định nghĩa trong NOI_DKBD_CONFIG
          console.error('❌ pham_vi_chuyen_mon or pham_vi_chuyen_mon_config.getDataUrl not found!');
          Swal.fire({ icon: 'error', title: 'Lỗi cấu hình!', text: 'Không tìm thấy cấu hình URL để tải dữ liệu ĐKBĐ.' });
          return;
      }
      const detailUrl = pvcmConfig.getDataUrl + (pvcmConfig.getDataUrl.includes('?') ? '&' : '?') + 'id=' + id;
      
      $.ajax({
        url: detailUrl,
        type: 'GET',
        success: function(response) {
          let apiItemData = null;
          if (response.success && response.data) {
            if (Array.isArray(response.data) && response.data.length > 0) {
                apiItemData = response.data[0];
            } else if (typeof response.data === 'object' && !Array.isArray(response.data)) {
                apiItemData = response.data; 
            }
          }

          if (apiItemData) {
            showEditModalWithData(apiItemData);
          } else {
            Swal.fire({ icon: 'warning', title: 'Không tìm thấy!', text: 'Không tìm thấy dữ liệu cho mục được chọn từ API.' });
          }
        },
        error: function(xhr, status, error) {
          console.error('Error loading edit data from API:', error);
          Swal.fire({ icon: 'error', title: 'Lỗi tải dữ liệu!', text: 'Có lỗi xảy ra khi tải dữ liệu ĐKBĐ để chỉnh sửa từ API: ' + error });
        }
      });
    }

    function showEditModalWithData(dataToLoad) {
        $(self.config.editFormId).attr('data-id', id);
        self.populateEditForm(dataToLoad);
        $(self.config.editModalId).modal('show');
    }
  };

  window.manager.populateEditForm = function(data) {
    const formId = this.config.editFormId; // Should be '#editForm'
    $(formId).find('[name="ma_pham_vi"]').val(data.ma_pham_vi);
    $(formId).find('[name="ten_chuyen_khoa"]').val(data.ten_chuyen_khoa);
    $(formId).find('[name="hieu_luc"]').prop('checked', data.hieu_luc);
  };
}

// Refresh cache function (nếu cần)
window.refreshdataCache = function() {
  window.dataCache.loaded = false;
};

// Debug functions
window.getdataCache = function() {
  return window.dataCache;
};
{% endblock %}
