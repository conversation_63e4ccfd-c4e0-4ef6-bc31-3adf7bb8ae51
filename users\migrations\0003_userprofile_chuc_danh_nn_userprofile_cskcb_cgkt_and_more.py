# Generated by Django 4.2.7 on 2025-07-16 07:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_userprofile_practice_certificate_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='chuc_danh_nn',
            field=models.IntegerField(blank=True, null=True, verbose_name='Chức danh nghề nghiệp'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='cskcb_cgkt',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Cơ sở khám chữa bệnh chuyển giao kĩ thuật'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='cskcb_khac',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='C<PERSON> sở khám chữa bệnh khác'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='den_ngay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ng<PERSON>y kết thúc làm việc của người hành nghề tại cơ sở KCB'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='dvkt_khac',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Dịch vụ kĩ thuật khác'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gioi_tinh',
            field=models.IntegerField(blank=True, null=True, verbose_name='Giới tính'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='ma_bhxh',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã BHXH'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='ma_loai_kcb',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã loại khám chữa bệnh'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='ngay_cap_cchn',
            field=models.DateField(blank=True, null=True, verbose_name='Ngày cấp Chứng chỉ hành nghề'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='noi_cap_cchn',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Nơi cấp Chứng chỉ hành nghề'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pham_vi_cm',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Phạm vi chuyên môn'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pham_vi_cm_bs',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Phạm vi chuyên môn bác sĩ'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='qd_cgkt',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Quyết định chuyển giao kĩ thuật'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='thoi_gian_dk',
            field=models.IntegerField(blank=True, null=True, verbose_name='Thời gian đăng ký'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='thoi_gian_ngay',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Thời gian ngày'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='thoi_gian_tuan',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Thời gian tuần'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='tu_ngay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu làm việc của người hành nghề tại cơ sở KCB'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='vb_phan_cong',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Văn bản phân công'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='vi_tri',
            field=models.IntegerField(blank=True, null=True, verbose_name='Vị trí công tác'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='practice_certificate',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Chứng chỉ hành nghề'),
        ),
    ]
