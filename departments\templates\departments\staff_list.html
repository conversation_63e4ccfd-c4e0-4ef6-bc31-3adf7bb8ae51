{% extends 'layouts/base.html' %}

{% block title %}Quản lý nhân viên | LAN Insight Guardian{% endblock %}

{% block page_title %}Quản lý nhân viên{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'departments:department_list' %}">Quản lý khoa phòng</a></li>
<li class="breadcrumb-item active">Quản lý nhân viên</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Danh sách nhân viên</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-12">
            <a href="{% url 'departments:create_staff' %}" class="btn btn-primary">
              <i class="fas fa-user-plus mr-1"></i> Thêm nhân viên mới
            </a>
          </div>
        </div>
        
        <!-- Search and filter form -->
        <form method="get" action="{% url 'departments:staff_list' %}">
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="form-group">
                <input type="text" class="form-control" name="search" placeholder="Tìm kiếm..." value="{{ request.GET.search|default:'' }}">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select class="form-control" name="department">
                  <option value="">-- Tất cả khoa phòng --</option>
                  {% for dept in departments %}
                  <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"i" %}selected{% endif %}>{{ dept.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select class="form-control" name="position">
                  <option value="">-- Tất cả chức danh --</option>
                  {% for pos in positions %}
                  <option value="{{ pos.id }}" {% if request.GET.position == pos.id|stringformat:"i" %}selected{% endif %}>{{ pos.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search mr-1"></i> Tìm kiếm
              </button>
              <a href="{% url 'departments:staff_list' %}" class="btn btn-default">
                <i class="fas fa-sync-alt mr-1"></i> Làm mới
              </a>
            </div>
          </div>
        </form>
        
        <div class="table-responsive">
          <table class="table table-hover text-nowrap">
            <thead>
              <tr>
                <th>ID</th>
                <th>Họ tên</th>
                <th>Tên đăng nhập</th>
                <th>Email</th>
                <th>Khoa phòng</th>
                <th>Chức danh</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {% for staff in staff_list %}
              <tr>
                <td>{{ staff.id }}</td>
                <td>{{ staff.user.get_full_name }}</td>
                <td>{{ staff.user.username }}</td>
                <td>{{ staff.user.email|default:"-" }}</td>
                <td>{{ staff.department.name|default:"-" }}</td>
                <td>{{ staff.position.name|default:"-" }}</td>
                <td>
                  <a href="{% url 'departments:staff_detail' staff.id %}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye"></i>
                  </a>
                  <a href="{% url 'departments:edit_staff' staff.id %}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i>
                  </a>
                  <button type="button" class="btn btn-danger btn-sm delete-staff" data-id="{{ staff.id }}" data-name="{{ staff.user.get_full_name }}">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="7" class="text-center">Không có nhân viên nào.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      <!-- /.card-body -->
      <div class="card-footer clearfix">
        {% if staff_list.has_other_pages %}
        <ul class="pagination pagination-sm m-0 float-right">
          {% if staff_list.has_previous %}
          <li class="page-item"><a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.position %}&position={{ request.GET.position }}{% endif %}">&laquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ staff_list.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.position %}&position={{ request.GET.position }}{% endif %}">&lsaquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&lsaquo;</span></li>
          {% endif %}
          
          {% for i in staff_list.paginator.page_range %}
            {% if staff_list.number == i %}
              <li class="page-item active"><span class="page-link">{{ i }}</span></li>
            {% elif i > staff_list.number|add:'-3' and i < staff_list.number|add:'3' %}
              <li class="page-item"><a class="page-link" href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.position %}&position={{ request.GET.position }}{% endif %}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}
          
          {% if staff_list.has_next %}
          <li class="page-item"><a class="page-link" href="?page={{ staff_list.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.position %}&position={{ request.GET.position }}{% endif %}">&rsaquo;</a></li>
          <li class="page-item"><a class="page-link" href="?page={{ staff_list.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.position %}&position={{ request.GET.position }}{% endif %}">&raquo;</a></li>
          {% else %}
          <li class="page-item disabled"><span class="page-link">&rsaquo;</span></li>
          <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
          {% endif %}
        </ul>
        {% endif %}
      </div>
    </div>
    <!-- /.card -->
  </div>
</div>

<!-- Delete Staff Modal -->
<div class="modal fade" id="deleteStaffModal" tabindex="-1" role="dialog" aria-labelledby="deleteStaffModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteStaffModalLabel">Xác nhận xóa nhân viên</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa nhân viên <strong id="delete_staff_name"></strong>?</p>
        <p class="text-danger">Lưu ý: Hành động này không thể hoàn tác.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
        <form method="post" action="" id="deleteStaffForm">
          {% csrf_token %}
          <button type="submit" class="btn btn-danger">Xóa nhân viên</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    // Delete staff
    $('.delete-staff').click(function() {
      var staffId = $(this).data('id');
      var staffName = $(this).data('name');
      
      $('#delete_staff_name').text(staffName);
      $('#deleteStaffForm').attr('action', '{% url "departments:staff_list" %}' + staffId + '/delete/');
      $('#deleteStaffModal').modal('show');
    });
  });
</script>
{% endblock %}
