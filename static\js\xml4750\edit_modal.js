// C<PERSON>u hình biến đổi trường dữ liệu cho modal
const fieldTransformers = {
    'XML0': {
        'ngaySinh': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'gtTheTu': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'gtTheDen': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayVao': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayVaoNoiTru': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayYL': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        }
    },
    'XML1': { // xmlTypeKey
        'ngaySinh': { // fieldName
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'gtTheTu': { // Đã có
            toModal: toDate,
            fromModal: fromDate,
        },
        'gtTheDen': { 
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayMienCCT': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayVao': { 
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayVaoNoiTru': { 
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayRa': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayTToan': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayTaiKham': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'namNamLienTuc': {
            toModal: toDate,
            fromModal: fromDate,
        },
    },
    'XML7': {
        'ngayVao': { // fieldName
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayRa': { // fieldName
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayCT': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngoaiTruTuNgay': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngoaiTruDenNgay': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
    },
    'XML8': {
        'ngayVao': { // fieldName
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayRa': { // fieldName
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayCT': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngaySinhCon': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayConChet': { // fieldName
            toModal: toDate,
            fromModal: fromDate,
        },
    },
    'XML10': {
        'tuNgay': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'denNgay': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayCT': {
            toModal: toDate,
            fromModal: fromDate,
        }
    },
    'XML11': {
        'tuNgay': { // Đã có, đảm bảo type là date
            toModal: toDate,
            fromModal: fromDate,
        },
        'denNgay': { // Thêm mới
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayCT': { // Thêm mới
            toModal: toDate,
            fromModal: fromDate,
        }
    },
    'XML12': {
        'ngayHop': {
            toModal: toMonth, // Hàm mới
            fromModal: fromMonth, // Hàm mới
        },
        'ngaySinh': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayCapCCCD': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayChungTu': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayDeNghi': {
            toModal: toDate,
            fromModal: fromDate,
        }
    },
    'XML13': {
        'ngaySinh': {
            toModal: toDate, // Should be date (YYYYMMDD)
            fromModal: fromDate, // Should be date (YYYYMMDD)
        },
        'gtTheDen': {
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayVao': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayVaoNoiTru': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayRa': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        }
    },
    'XML14': {
        'ngaySinh': {
            toModal: toDate, // Should be date (YYYYMMDD)
            fromModal: fromDate, // Should be date (YYYYMMDD)
        },
        'gtTheDen': { // GT Thẻ Đến (yyyyMMdd)
            toModal: toDate, 
            fromModal: fromDate,
        },
        'ngayVao': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayVaoNoiTru': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayRa': {
            toModal: toDateTime,
            fromModal: fromDateTime,
        },
        'ngayHenKL': { // Ngày hẹn khám lại (yyyyMMdd)
            toModal: toDate,
            fromModal: fromDate,
        },
        'ngayCT': { // Ngày chứng từ (yyyyMMdd)
            toModal: toDate,
            fromModal: fromDate,
        }
    },
};

// Cấu hình chung cho việc khởi tạo các select trong modal
const modalSelectConfigs = [
    {
        selectId: 'modal_xml0_gioiTinh',
        dataSource: gioi_tinh, // Biến gioi_tinh từ tabulator_xml_func.js
        valueField: 'id',       // Tên trường cho value của option
        labelField: 'name',     // Tên trường cho text của option
        isObjectSource: true,   // Đánh dấu dataSource là object {key: value} cần chuyển đổi
        emptyOptionText: "Chọn giới tính"
    },
    {
        selectId: 'modal_xml1_gioiTinh',
        dataSource: gioi_tinh,
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn giới tính"
    },
    {
        selectId: 'modal_xml1_maDanToc',
        dataSource: ma_dan_toc, // Biến ma_dan_toc từ tabulator_xml_func.js
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn dân tộc"
    },
    {
        selectId: 'modal_xml1_maTinhCuTru',
        dataSourceName: 'tinh', // Key trong categoryData
        valueField: 'value',    // categoryData['tinh'] là [{value: '01', label: '01 - Hà Nội'}, ...]
        labelField: 'label',
        isObjectSource: false,  // categoryData đã là mảng các object
        emptyOptionText: "Chọn Tỉnh/TP"
    },
    {
        selectId: 'modal_xml1_maHuyenCuTru',
        dataSourceName: 'quanhuyen', // Sẽ được lọc động, nhưng có thể khởi tạo rỗng hoặc với tất cả
        valueField: 'value',
        labelField: 'label',
        isObjectSource: false,
        emptyOptionText: "Chọn Quận/Huyện"
        
    },
    {
        selectId: 'modal_xml1_maXaCuTru', // Giả sử ID của select xã là modal_xml1_maXaCuTru
        dataSourceName: 'xaphuong',      // Key trong categoryData
        valueField: 'value',
        labelField: 'label',
        isObjectSource: false,
        emptyOptionText: "Chọn Phường/Xã"
    },
    {
        selectId: 'modal_xml1_maTaiNan',
        dataSource: ma_tai_nan,
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn mã tai nạn"
    },
    {
        selectId: 'modal_xml1_ketQuaDtri',
        dataSource: ket_qua_dtri,
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn KQ điều trị"
    },
    {
        selectId: 'modal_xml1_maLoaiRV',
        dataSource: ma_loai_rv,
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn loại ra viện"
    },
    {
        selectId: 'modal_xml1_maLoaiKCB',
        dataSource: ma_loai_kcb,
        valueField: 'id',
        labelField: 'name',
        isObjectSource: true,
        emptyOptionText: "Chọn loại kcb"
    },
];

// Hàm helper để điền options vào select
function populateSelectWithOptions(selectId, optionsArray, valueField, labelField, selectedValue = null, addEmptyOption = true, emptyOptionText = "--- Chọn ---") {
    const $select = $('#' + selectId);
    $select.empty(); // Clear existing options

    if (addEmptyOption) {
        $select.append($('<option>', { value: '', text: emptyOptionText }));
    }

    optionsArray.forEach(item => {
        $select.append($('<option>', {
            value: item[valueField],
            text: item[labelField]
        }));
    });

    if (selectedValue === '' && addEmptyOption) {
        $select.val('');
    } else {
        $select.val(selectedValue);
    }
    if ($select.data('select2')) { // Nếu dùng select2
        $select.trigger('change.select2');
    }
}

// Hàm khởi tạo chung cho các select trong modal dựa trên cấu hình
function initializeConfiguredModalSelects() {
    modalSelectConfigs.forEach(config => {
        let optionsArray;
        let dataSource = config.dataSource; // Ưu tiên dataSource trực tiếp

        // Nếu dataSource không được cung cấp trực tiếp, thử lấy từ categoryData
        if (!dataSource && config.dataSourceName && typeof categoryData === 'object' && categoryData !== null) {
            dataSource = categoryData[config.dataSourceName];
        }

        if (!dataSource) {
            // console.warn(`Data source không tìm thấy cho select ${config.selectId}. Có thể categoryData chưa tải xong hoặc biến không tồn tại.`);
            // Vẫn tạo select rỗng với empty option nếu cần
            if (config.selectId && document.getElementById(config.selectId)) {
                 populateSelectWithOptions(config.selectId, [], '', '', null, true, config.emptyOptionText || "--- Chọn ---");
            }
            return; // Bỏ qua nếu không có dataSource
        }

        if (config.isObjectSource) { // Nếu dataSource là object dạng {key: value}
            // Nếu dataSource được cung cấp trực tiếp (ví dụ từ getCategoryValues),
            // thì `val` đã là label đầy đủ (ví dụ: '01 - Hà Nội').
            // Nếu dataSource là object thô (ví dụ `gioi_tinh = {1: "Nam"}`), thì cần tạo label `key - val`.
            if (config.dataSource && !config.dataSourceName && typeof dataSource === 'object' && dataSource !== null) {
                const firstKey = Object.keys(dataSource)[0];
                // Nếu giá trị của dataSource đã chứa key (ví dụ '01 - Hà Nội' chứa '01'), thì dùng trực tiếp val.
                // Ngược lại (ví dụ gioi_tinh: {'1': 'Nam'}), thì ghép key và val.
                const valIsFullLabel = typeof dataSource[firstKey] === 'string' && dataSource[firstKey].includes(firstKey + ' - ');
                optionsArray = Object.entries(dataSource).map(([key, val]) => ({
                    [config.valueField]: key,
                    [config.labelField]: valIsFullLabel ? val : `${key} - ${val}`
                }));
            } else { // Fallback hoặc cho trường hợp dataSourceName nhưng isObjectSource (ít xảy ra)
                optionsArray = Object.entries(dataSource || {}).map(([key, val]) => ({
                    [config.valueField]: key,
                    [config.labelField]: `${key} - ${val}`
                }));
            }
        } else { // Nếu dataSource đã là một mảng các object
            optionsArray = Array.isArray(dataSource) ? dataSource : [];
        }

        populateSelectWithOptions(
            config.selectId, optionsArray, config.valueField, config.labelField,
            null, true, config.emptyOptionText || "--- Chọn ---"
        );
    });
}

function toDateTime(valueFromRecord) {
    if (!valueFromRecord) return "";
    const dt = luxon.DateTime.fromFormat(String(valueFromRecord), "yyyyMMddHHmm");
    return dt.isValid ? dt.toFormat("yyyy-MM-dd'T'HH:mm") : "";
}

function fromDateTime(valueFromInput) {
    if (!valueFromInput) return "";
    const dt = luxon.DateTime.fromISO(String(valueFromInput));
    return dt.isValid ? dt.toFormat("yyyyMMddHHmm") : "";
}

function toDate(valueFromRecord) {
    if (!valueFromRecord) return "";
    const dt = luxon.DateTime.fromFormat(String(valueFromRecord), "yyyyMMdd");
    return dt.isValid ? dt.toFormat("yyyy-MM-dd") : "";
}

function fromDate(valueFromInput) {
    if (!valueFromInput) return "";
    const dt = luxon.DateTime.fromISO(String(valueFromInput)); // Hoặc fromFormat("yyyy-MM-dd")
    return dt.isValid ? dt.toFormat("yyyyMMdd") : "";
}

// Hàm ví dụ (cần thêm vào edit_modal.js)
function toMonth(valueFromRecord) { // YYYYMM -> YYYY-MM
    if (!valueFromRecord || String(valueFromRecord).length !== 6) return "";
    const year = String(valueFromRecord).substring(0, 4);
    const month = String(valueFromRecord).substring(4, 6);
    return `${year}-${month}`;
}

function fromMonth(valueFromInput) { // YYYY-MM -> YYYYMM
    if (!valueFromInput) return "";
    return String(valueFromInput).replace('-', '');
}


// Thêm hàm setupModalTables riêng cho modal
function setupModalTables(xmlTypeToSetup, records = []) {
    if (!xmlTypeToSetup) {
        return;
    }

    var modalTableId = 'modal_' + xmlTypeToSetup.toLowerCase() + '-edit-table';
    var modalTableElement = document.getElementById(modalTableId);

    if (modalTableElement && !modalTableElement._tabulator) {
        var columns = getColumnsConfig(xmlTypeToSetup);
        
        if (!columns || columns.length === 0) {
            console.warn("No columns config found for " + xmlTypeToSetup);
            return;
        }

        try {
            // Khởi tạo Tabulator cho modal với config đơn giản hơn
            modalTableElement._tabulator = new Tabulator(modalTableElement, {
                columns: columns,
                layout: "fitDataFill",
                data: records, // Set data trực tiếp khi khởi tạo
                pagination: true,
                paginationMode: "local", // Luôn dùng local cho modal
                paginationSize: 10,
                paginationSizeSelector: [5, 10, 20, 50, 100],
                index: "_tempModalId",
                editable: true,
                scrollX: true,
                autoResize: true,
                movableColumns: true,
                selectable: true,
                height: "calc(100vh - 320px)",
                placeholder: "Không có dữ liệu",
                tooltips: true,
                headerFilterLiveFilterDelay: 300,
                resizableColumnFit: true,
                langs: {
                    "vi-vn": {
                        "columns": {
                            "name": "Tên",
                            "progress": "Tiến độ", 
                            "gender": "Giới tính",
                            "rating": "Đánh giá",
                            "col": "Cột"
                        },
                        "ajax": {
                            "loading": "Đang tải",
                            "error": "Lỗi"
                        },
                        "pagination": {
                            "page_size": "Kích thước trang",
                            "page_title": "Hiển thị trang",
                            "first": "Đầu",
                            "first_title": "Trang đầu",
                            "last": "Cuối", 
                            "last_title": "Trang cuối",
                            "prev": "Trước",
                            "prev_title": "Trang trước",
                            "next": "Tiếp",
                            "next_title": "Trang tiếp theo"
                        },
                        "headerFilters": {
                            "default": "Tìm kiếm...",
                        }
                    }
                }
            });
            setTimeout(function() {
                if (modalTableElement && modalTableElement._tabulator) {
                    modalTableElement._tabulator.redraw(true);
                }
            }, 500);
        } catch (error) {
            console.error("Error initializing modal Tabulator for " + xmlTypeToSetup + ":", error);
        }
    } else if (modalTableElement && modalTableElement._tabulator) {
        // Nếu đã có Tabulator, chỉ cần set data
        modalTableElement._tabulator.setData(records);
        console.log("Modal Tabulator data updated for " + xmlTypeToSetup + " with " + records.length + " records");
    } else {
        console.warn("Modal table element not found: " + modalTableId);
    }
}

// Hàm thiết lập cascading cho các select Tỉnh/Huyện/Xã trong modal XML1
function setupCascadingAddressSelects() {
    const $tinhSelect = $('#modal_xml1_maTinhCuTru');
    const $huyenSelect = $('#modal_xml1_maHuyenCuTru');
    const $xaSelect = $('#modal_xml1_maXaCuTru');

    // Event listener for Tỉnh/TP change
    $tinhSelect.on('change', function() {
        const selectedTinhValue = $(this).val();
        let huyenOptions = [];

        if (selectedTinhValue && typeof categoryData === 'object' && categoryData && categoryData.quanhuyen) {
            // Lọc danh sách quận/huyện dựa trên mã tỉnh
            // Giả định mã huyện (huyen.value) bắt đầu bằng mã tỉnh (selectedTinhValue)
            // Ví dụ: mã tỉnh "01", mã huyện "01234"\
            huyenOptions = categoryData.quanhuyen.filter(huyen =>
                huyen.value && String(huyen.tinh_id).startsWith(selectedTinhValue)
            );
        }

        const huyenConfig = modalSelectConfigs.find(c => c.selectId === 'modal_xml1_maHuyenCuTru');
        if (huyenConfig) {
            populateSelectWithOptions(
                huyenConfig.selectId,
                huyenOptions,
                huyenConfig.valueField,
                huyenConfig.labelField,
                null, // Reset selected value for Huyen
                true,
                huyenConfig.emptyOptionText
            );
        }

        // Clear and reset Xã select as Huyện has changed
        const xaConfig = modalSelectConfigs.find(c => c.selectId === 'modal_xml1_maXaCuTru');
        if (xaConfig) {
            populateSelectWithOptions(
                xaConfig.selectId,
                [], // Empty options for Xa
                xaConfig.valueField,
                xaConfig.labelField,
                null, // Reset selected value for Xa
                true,
                xaConfig.emptyOptionText
            );
        }
    });

    // Event listener for Quận/Huyện change
    $huyenSelect.on('change', function() {
        const selectedHuyenValue = $(this).val();
        let xaOptions = [];

        if (selectedHuyenValue && typeof categoryData === 'object' && categoryData && categoryData.xaphuong) {
            // Lọc danh sách phường/xã dựa trên mã huyện
            // Giả định mã xã (xa.value) bắt đầu bằng mã huyện (selectedHuyenValue)
            xaOptions = categoryData.xaphuong.filter(xa =>
                xa.value && String(xa.huyen_id).startsWith(selectedHuyenValue)
            );
        }
        const xaConfig = modalSelectConfigs.find(c => c.selectId === 'modal_xml1_maXaCuTru');
        if (xaConfig) {
            populateSelectWithOptions(
                xaConfig.selectId,
                xaOptions,
                xaConfig.valueField,
                xaConfig.labelField,
                null, // Reset selected value for Xa
                true,
                xaConfig.emptyOptionText
            );
        }
    });
}

// Thực hiện việc tính toán lại dữ liệu trong chỉnh sửa modal
function round2(value) {
    return Math.round(parseFloat(value) * 100) / 100;
}

function reCalcualteOneXML(xml1, lstXML2, lstXML3, tuyenBV) {
    let thanhTienBV_total = 0;
    let thanhTienBH_total = 0;
    let bhtt_total = 0;
    let bncct_total = 0;
    let bntt_total = 0; // tBNTT
    let nguonKhac_total = 0;
    let tThuoc_total = 0;
    let tVTYT_total = 0;
    // Thêm các biến tổng khác nếu cần, ví dụ tBHTTGDV

    function isTraiTuyenTrungUong() {
        // Kiểm tra xml1 có tồn tại và có các thuộc tính cần thiết không
        return xml1 && xml1.maLoaiKCB === "3.1" && xml1.maKhuVuc === "" && tuyenBV === "1";
    }

    function calcNguonKhac(item) {
        return parseFloat(item.tNguonKhacNSNN || 0)
            + parseFloat(item.tNguonKhacVTNN || 0)
            + parseFloat(item.tNguonKhacVTTN || 0)
            + parseFloat(item.tNguonKhacCL || 0);
    }

    function processItem(item, isXML3 = false) {
        let phamVi = parseInt(item.phamVi);
        let soLuong = parseFloat(item.soLuong || 0);
        let mucHuong = parseFloat(item.mucHuong || 0);
        let tyLeTTBH = parseFloat(item.tyLeTTBH || 0); // Tỷ lệ thanh toán BH cho DV/Thuốc/VTYT đó
        let donGia = parseFloat(isXML3 ? (item.donGiaBV || 0) : (item.donGia || 0) ); // Đơn giá BV cho XML3, đơn giá chung cho XML2

        if (phamVi === 1) { // Trong phạm vi hưởng BHYT
            item.thanhTienBV = round2(soLuong * donGia);
            let donGiaBH = isXML3 ? parseFloat(item.donGiaBH || 0) : donGia;
            item.thanhTienBH = round2(soLuong * donGiaBH * tyLeTTBH / 100);

            item.tNguonKhac = round2(calcNguonKhac(item));

            if (isTraiTuyenTrungUong()) {
                item.tBHTT = round2(item.thanhTienBH * (mucHuong / 100) * 0.4); // BH thanh toán (sau khi đã nhân tỷ lệ hưởng)
            } else {
                item.tBHTT = round2(item.thanhTienBH * (mucHuong / 100));
            }

            item.tBNCCT = round2(item.thanhTienBH - item.tBHTT);
            item.tBNTT = round2(item.thanhTienBV - item.thanhTienBH);

            // Trừ nguồn khác
            if (item.tNguonKhac > 0) {
                if (item.tNguonKhac < item.tBNTT) {
                    item.tBNTT = round2(item.tBNTT - item.tNguonKhac);
                } else if (item.tNguonKhac <= (item.tBNTT + item.tBNCCT)) {
                    let nguonThua = item.tNguonKhac - item.tBNTT;
                    item.tBNTT = 0;
                    item.tBNCCT = round2(Math.max(0, item.tBNCCT - nguonThua));
                } else {
                    item.tBNTT = 0;
                    item.tBNCCT = 0;
                    item.tBHTT = round2(Math.max(0, item.thanhTienBH - item.tNguonKhac));
                }
            }

        } else if (phamVi === 2) {
            item.thanhTienBV = round2(soLuong * donGia);
            item.thanhTienBH = 0;
            item.tBHTT = 0;
            item.tBNCCT = 0;
            item.tNguonKhac = round2(calcNguonKhac(item));
            item.tBNTT = round2(Math.max(0, item.thanhTienBV - item.tNguonKhac));
        } else {
            console.warn("Phạm vi không hợp lệ:", phamVi);
        }

        // Cộng tổng
        thanhTienBV_total += item.thanhTienBV;
        thanhTienBH_total += item.thanhTienBH;
        bhtt_total += item.tBHTT;
        bncct_total += item.tBNCCT;
        bntt_total += item.tBNTT; // Đây là tBNTT của từng dòng chi tiết
        nguonKhac_total += item.tNguonKhac;

        // Tính tổng tThuoc và tVTYT dựa trên maNhom và loại item
        if (!isXML3) { // Nếu là XML2 (thuốc)
            // Giả sử maNhom cho thuốc là một giá trị cụ thể, ví dụ '4'
            // Hoặc nếu tất cả XML2 đều là thuốc thì cộng dồn trực tiếp
            tThuoc_total += item.tBHTT; // Hoặc item.thanhTienBH tùy theo logic bạn muốn tThuoc là gì
        } else { // Nếu là XML3 (DVKT/VTYT)
            // Phân biệt DVKT và VTYT dựa trên maNhom hoặc trường khác nếu có
            // Ví dụ: nếu maNhom '10' hoặc '11' là VTYT
            if (item.maNhom && (String(item.maNhom) === '10' || String(item.maNhom) === '11')) {
                tVTYT_total += item.tBHTT; // Hoặc item.thanhTienBH
            }
            // Các maNhom khác có thể là DVKT
        }
    }

    // Duyệt qua XML2 và XML3
    lstXML2.forEach(item => processItem(item, false));
    lstXML3.forEach(item => processItem(item, true));

    // Tính toán các tổng cuối cùng cho XML1
    // tTongChiBV là tổng của tất cả thanhTienBV
    // tTongChiBH là tổng của tất cả thanhTienBH
    // tBHTT là tổng của tất cả tBHTT chi tiết
    // tBNCCT là tổng của tất cả tBNCCT chi tiết
    // tBNTT là tổng của tất cả tBNTT chi tiết
    return {
        tTongChiBV: round2(thanhTienBV_total),
        tTongChiBH: round2(thanhTienBH_total),
        tBHTT: round2(bhtt_total),
        tBNCCT: round2(bncct_total),
        tBNTT: round2(bntt_total), // Đây là tổng tBNTT của XML1
        tNguonKhac: round2(nguonKhac_total),
        tThuoc: round2(tThuoc_total),
        tVTYT: round2(tVTYT_total)
        // tBHTTGDV: tính toán nếu cần
    };
}

// 1. Cập nhật hàm handleRecalculateForModal để nhận xmlType
function handleRecalculateForModal(xmlType) {
    console.log(`Recalculating for ${xmlType}...`);
    
    // Lấy dữ liệu XML1 (giả sử chỉ có 1 record trong modal)
    const xml1Data = $('#modal_xml1-edit-table').get(0)?._tabulator?.getData() || [];
    const xml1 = xml1Data[0] || {};

    // Lấy dữ liệu XML2 và XML3
    const xml2Data = $('#modal_xml2-edit-table').get(0)?._tabulator?.getData() || [];
    const xml3Data = $('#modal_xml3-edit-table').get(0)?._tabulator?.getData() || [];

    console.log('Data for recalculation:', { xml1, xml2Data: xml2Data.length, xml3Data: xml3Data.length });

    // Gọi hàm tính toán lại
    const result = reCalcualteOneXML(xml1, xml2Data, xml3Data, tuyenbv);
    
    console.log('Recalculation result:', result);

    // Cập nhật lại các trường tổng trên giao diện modal
    if (result) {
        $('#modalEditTongChiBV').text(result.thanhTienBV?.toLocaleString('vi-VN') || '0');
        $('#modalEditTongChiBH').text(result.thanhTienBH?.toLocaleString('vi-VN') || '0');
        $('#modalEditTongBHTT').text(result.bhtt?.toLocaleString('vi-VN') || '0');
        $('#modalEditTongBNCCT').text(result.bncct?.toLocaleString('vi-VN') || '0');
        $('#modalEditTongBNTT').text(result.bntt?.toLocaleString('vi-VN') || '0');
        
        // Cập nhật thêm các trường khác nếu có
        if (result.tongTienThuoc !== undefined) {
            $('#modalEditTongTienThuoc').text(result.tongTienThuoc.toLocaleString('vi-VN'));
        }
        if (result.tongTienVTYT !== undefined) {
            $('#modalEditTongTienVTYT').text(result.tongTienVTYT.toLocaleString('vi-VN'));
        }
    }
}

// 2. Tạo callback function cho từng loại XML
function createRecalculateCallback(xmlType) {
    return function(cell) {
        // Delay một chút để đảm bảo dữ liệu đã được cập nhật
        setTimeout(() => {
            handleRecalculateForModal(xmlType);
        }, 100);
    };
}

// 3. Khi khởi tạo các bảng trong modal, truyền callback
function initializeModalTables() {
    // Khởi tạo bảng XML1 với callback
    if (typeof createCellEventHandlers === 'function') {
        const xml1Handlers = createCellEventHandlers(createRecalculateCallback('XML1'));
        
        // Áp dụng cho bảng XML1
        $('#modal_xml1-edit-table').get(0)?._tabulator?.on('cellEdited', xml1Handlers.cellEdited);
        $('#modal_xml1-edit-table').get(0)?._tabulator?.on('cellEditing', xml1Handlers.cellEditing);
        $('#modal_xml1-edit-table').get(0)?._tabulator?.on('cellEditCancelled', xml1Handlers.cellEditCancelled);
    }

    // Khởi tạo bảng XML2 với callback
    if (typeof createCellEventHandlers === 'function') {
        const xml2Handlers = createCellEventHandlers(createRecalculateCallback('XML2'));
        
        // Áp dụng cho bảng XML2
        $('#modal_xml2-edit-table').get(0)?._tabulator?.on('cellEdited', xml2Handlers.cellEdited);
        $('#modal_xml2-edit-table').get(0)?._tabulator?.on('cellEditing', xml2Handlers.cellEditing);
        $('#modal_xml2-edit-table').get(0)?._tabulator?.on('cellEditCancelled', xml2Handlers.cellEditCancelled);
    }

    // Khởi tạo bảng XML3 với callback
    if (typeof createCellEventHandlers === 'function') {
        const xml3Handlers = createCellEventHandlers(createRecalculateCallback('XML3'));
        
        // Áp dụng cho bảng XML3
        $('#modal_xml3-edit-table').get(0)?._tabulator?.on('cellEdited', xml3Handlers.cellEdited);
        $('#modal_xml3-edit-table').get(0)?._tabulator?.on('cellEditing', xml3Handlers.cellEditing);
        $('#modal_xml3-edit-table').get(0)?._tabulator?.on('cellEditCancelled', xml3Handlers.cellEditCancelled);
    }
}

// ===== QUẢN LÝ MÃ THẺ BHYT - UPDATED WITH yyyyMMdd FORMAT =====

// Biến global cho quản lý mã thẻ BHYT
let bhytCards = []; // Mảng lưu trữ các mã thẻ
let editingIndex = -1; // Index của mã thẻ đang được sửa

// ===== HÀM CHUYỂN ĐỔI FORMAT NGÀY THÁNG =====

// Chuyển từ yyyyMMdd sang dd/MM/yyyy
function convertFromYyyyMMddToDisplay(dateString) {
    if (!dateString) return '';
    
    // Nếu đã là định dạng dd/MM/yyyy thì trả về luôn
    if (dateString.includes('/')) return dateString;
    
    // Nếu là định dạng yyyy-MM-dd thì chuyển đổi
    if (dateString.includes('-')) {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        
        return `${day}/${month}/${year}`;
    }
    
    // Nếu là định dạng yyyyMMdd (8 ký tự)
    if (dateString.length === 8 && /^\d{8}$/.test(dateString)) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        
        return `${day}/${month}/${year}`;
    }
    
    return dateString;
}

// Chuyển từ dd/MM/yyyy hoặc yyyy-MM-dd sang yyyyMMdd
function convertToYyyyMMdd(dateString) {
    if (!dateString) return '';
    
    let date;
    
    // Nếu là định dạng dd/MM/yyyy
    if (dateString.includes('/')) {
        const parts = dateString.split('/');
        if (parts.length === 3) {
            const day = parts[0].padStart(2, '0');
            const month = parts[1].padStart(2, '0');
            const year = parts[2];
            date = new Date(year, month - 1, day);
        }
    }
    // Nếu là định dạng yyyy-MM-dd
    else if (dateString.includes('-')) {
        date = new Date(dateString);
    }
    // Nếu đã là định dạng yyyyMMdd
    else if (dateString.length === 8 && /^\d{8}$/.test(dateString)) {
        return dateString;
    }
    
    if (date && !isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}${month}${day}`;
    }
    
    return dateString;
}

// Chuyển từ yyyy-MM-dd sang yyyyMMdd
function convertDateInputToYyyyMMdd(dateInputValue) {
    if (!dateInputValue) return '';
    
    const date = new Date(dateInputValue);
    if (isNaN(date.getTime())) return dateInputValue;
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}${month}${day}`;
}

// Chuyển từ yyyyMMdd sang yyyy-MM-dd (cho input date)
function convertYyyyMMddToDateInput(yyyyMMdd) {
    if (!yyyyMMdd) return '';
    
    // Nếu đã là định dạng yyyy-MM-dd thì trả về luôn
    if (yyyyMMdd.includes('-')) return yyyyMMdd;
    
    // Nếu là định dạng yyyyMMdd (8 ký tự)
    if (yyyyMMdd.length === 8 && /^\d{8}$/.test(yyyyMMdd)) {
        const year = yyyyMMdd.substring(0, 4);
        const month = yyyyMMdd.substring(4, 6);
        const day = yyyyMMdd.substring(6, 8);
        
        return `${year}-${month}-${day}`;
    }
    
    return yyyyMMdd;
}

// Format ngày từ YYYY-MM-DD sang DD/MM/YYYY (giữ nguyên hàm cũ để tương thích)
function formatDateForDisplay(dateString) {
    return convertFromYyyyMMddToDisplay(dateString);
}

// Format nhiều ngày thành chuỗi hiển thị - UPDATED
function formatMultipleDatesForDisplay(datesString) {
    if (!datesString) return '';
    
    // Kiểm tra nếu chỉ có 1 ngày (không có dấu ;)
    if (!datesString.includes(';')) {
        return convertFromYyyyMMddToDisplay(datesString);
    }
    
    // Xử lý nhiều ngày
    const dates = datesString.split(';');
    const formattedDates = [];
    
    dates.forEach(date => {
        const trimmedDate = date.trim();
        if (trimmedDate) {
            const formatted = convertFromYyyyMMddToDisplay(trimmedDate);
            if (formatted) {
                formattedDates.push(formatted);
            }
        }
    });
    
    return formattedDates.length > 0 ? formattedDates.join(';') : '';
}

// ===== XỬ LÝ CHUYỂN ĐỔI INPUT DATE/TEXT CHO MÃ THẺ BHYT =====

// Chuyển đổi hiển thị input dựa trên số lượng mã thẻ - UPDATED
function switchBHYTInputDisplay() {
    const maTheBHYT = $('#modal_xml1_maTheBHYT').val();
    
    // Lấy dữ liệu từ hidden fields thay vì input date (vì input date không thể chứa nhiều giá trị)
    const gtTheTu = $('#modal_xml1_gtTheTu').data('raw-value') || $('#modal_xml1_gtTheTu').val();
    const gtTheDen = $('#modal_xml1_gtTheDen').data('raw-value') || $('#modal_xml1_gtTheDen').val();

    // Kiểm tra xem có nhiều mã thẻ không
    const hasMultipleCards = maTheBHYT && maTheBHYT.includes(';');
    console.log('Has multiple cards:', hasMultipleCards);
    
    if (hasMultipleCards) {
        // Nhiều mã thẻ - hiển thị input text
        showTextInputs(gtTheTu, gtTheDen);
    } else {
        // Một mã thẻ hoặc không có - hiển thị input date
        showDateInputs(gtTheTu, gtTheDen);
    }
}

// Hiển thị input text cho nhiều mã thẻ - UPDATED
function showTextInputs(gtTheTu, gtTheDen) {   
    // Ẩn input date
    $('#modal_xml1_gtTheTu').hide();
    $('#modal_xml1_gtTheDen').hide();
    
    // Hiển thị input text
    $('#modal_xml1_gtTheTu_text').show();
    $('#modal_xml1_gtTheDen_text').show();
    
    // Set giá trị cho input text với format dễ đọc (dd/MM/yyyy)
    const formattedGtTheTu = formatMultipleDatesForDisplay(gtTheTu);
    const formattedGtTheDen = formatMultipleDatesForDisplay(gtTheDen);
    
    $('#modal_xml1_gtTheTu_text').val(formattedGtTheTu);
    $('#modal_xml1_gtTheDen_text').val(formattedGtTheDen);
    
    // Cập nhật placeholder
    const cardCount = gtTheTu ? gtTheTu.split(';').length : 0;
    $('#modal_xml1_gtTheTu_text').attr('placeholder', cardCount > 0 ? `${cardCount} khoảng thời gian` : 'Nhiều khoảng thời gian...');
    $('#modal_xml1_gtTheDen_text').attr('placeholder', cardCount > 0 ? `${cardCount} khoảng thời gian` : 'Nhiều khoảng thời gian...');
}

// Hiển thị input date cho một mã thẻ - UPDATED
function showDateInputs(gtTheTu, gtTheDen) {
    // Hiển thị input date
    $('#modal_xml1_gtTheTu').show();
    $('#modal_xml1_gtTheDen').show();
    
    // Ẩn input text
    $('#modal_xml1_gtTheTu_text').hide();
    $('#modal_xml1_gtTheDen_text').hide();
    
    // Set giá trị cho input date (chuyển đổi từ yyyyMMdd sang yyyy-MM-dd)
    const firstGtTheTu = gtTheTu ? gtTheTu.split(';')[0].trim() : '';
    const firstGtTheDen = gtTheDen ? gtTheDen.split(';')[0].trim() : '';

    const dateInputGtTheTu = convertYyyyMMddToDateInput(firstGtTheTu);
    const dateInputGtTheDen = convertYyyyMMddToDateInput(firstGtTheDen);

    $('#modal_xml1_gtTheTu').val(dateInputGtTheTu);
    $('#modal_xml1_gtTheDen').val(dateInputGtTheDen);
}

// Khởi tạo xử lý chuyển đổi input - UPDATED
function initBHYTInputSwitcher() {
    // Chuyển đổi input khi modal được mở
    $(document).on('shown.bs.modal', '#editPreviewModal', function() {
        switchBHYTInputDisplay();
    });
    
    // Chuyển đổi input khi dữ liệu thay đổi
    $(document).on('change', '#modal_xml1_maTheBHYT', function() {
        switchBHYTInputDisplay();
    });
    
    // Xử lý khi người dùng thay đổi input date (chỉ khi có 1 mã thẻ) - UPDATED
    $(document).on('change', '#modal_xml1_gtTheTu, #modal_xml1_gtTheDen', function() {
        const maTheBHYT = $('#modal_xml1_maTheBHYT').val();
        
        // Chỉ xử lý khi có 1 mã thẻ
        if (!maTheBHYT || !maTheBHYT.includes(';')) {
            const gtTheTu = $('#modal_xml1_gtTheTu').val();
            const gtTheDen = $('#modal_xml1_gtTheDen').val();
            
            // Validation ngày tháng
            if (gtTheTu && gtTheDen) {
                const fromDate = new Date(gtTheTu);
                const toDate = new Date(gtTheDen);
                
                if (fromDate >= toDate) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi ngày tháng!',
                        text: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc!'
                    });
                    $(this).focus();
                    return;
                }
            }
            
            // Lưu raw value dạng yyyyMMdd
            if (gtTheTu) {
                const yyyyMMddFrom = convertDateInputToYyyyMMdd(gtTheTu);
                $('#modal_xml1_gtTheTu').data('raw-value', yyyyMMddFrom);
            }
            if (gtTheDen) {
                const yyyyMMddTo = convertDateInputToYyyyMMdd(gtTheDen);
                $('#modal_xml1_gtTheDen').data('raw-value', yyyyMMddTo);
            }
        }
    });
    
    // Thêm tooltip cho input text khi có nhiều mã thẻ
    $(document).on('mouseenter', '#modal_xml1_gtTheTu_text, #modal_xml1_gtTheDen_text', function() {
        const value = $(this).val();
        if (value) {
            $(this).attr('title', 'Nhiều khoảng thời gian: ' + value);
        }
    });
    
    // Click vào input text sẽ mở dialog quản lý mã thẻ
    $(document).on('click', '#modal_xml1_gtTheTu_text, #modal_xml1_gtTheDen_text', function() {
        $('#manageBHYTCardsBtn').click();
    });
    
    // Thêm cursor pointer cho input text
    $(document).on('focus', '#modal_xml1_gtTheTu_text, #modal_xml1_gtTheDen_text', function() {
        $(this).css('cursor', 'pointer');
        $(this).attr('title', 'Click để mở quản lý mã thẻ BHYT');
    });
}

// Khởi tạo quản lý mã thẻ BHYT - UPDATED
function initBHYTCardManagement() {
    // Khởi tạo chuyển đổi input
    initBHYTInputSwitcher();
    
    // Mở modal quản lý mã thẻ BHYT - UPDATED
    $(document).on('click', '#manageBHYTCardsBtn', function() {
        // Load dữ liệu hiện tại từ input
        const currentValue = $('#modal_xml1_maTheBHYT').val();
        const currentGtTheTu = $('#modal_xml1_gtTheTu').data('raw-value') || convertDateInputToYyyyMMdd($('#modal_xml1_gtTheTu').val());
        const currentGtTheDen = $('#modal_xml1_gtTheDen').data('raw-value') || convertDateInputToYyyyMMdd($('#modal_xml1_gtTheDen').val());

        // Parse dữ liệu hiện tại - UPDATED: Xử lý format yyyyMMdd
        bhytCards = [];
        if (currentValue && currentValue.includes(';')) {
            // Nhiều mã thẻ - parse khoảng thời gian riêng biệt
            const cardNumbers = currentValue.split(';');
            const fromDates = currentGtTheTu ? currentGtTheTu.split(';') : [];
            const toDates = currentGtTheDen ? currentGtTheDen.split(';') : [];
            
            cardNumbers.forEach((cardNumber, index) => {
                if (cardNumber.trim()) {
                    bhytCards.push({
                        number: cardNumber.trim(),
                        from: fromDates[index] || currentGtTheTu,
                        to: toDates[index] || currentGtTheDen
                    });
                }
            });
        } else if (currentValue && currentValue.trim()) {
            // Một mã thẻ
            bhytCards.push({
                number: currentValue.trim(),
                from: currentGtTheTu,
                to: currentGtTheDen
            });
        }
        showBHYTCardManagementDialog();
    });

    // Kiểm tra và enable/disable input khi modal mở
    $(document).on('shown.bs.modal', '#editPreviewModal', function() {
        const maTheBHYT = $('#modal_xml1_maTheBHYT').val();
        if (maTheBHYT && maTheBHYT.includes(';')) {
            // Có nhiều mã thẻ - disable input
            $('#modal_xml1_maTheBHYT').prop('readonly', true);
            $('#modal_xml1_gtTheTu').prop('readonly', true);
            $('#modal_xml1_gtTheDen').prop('readonly', true);
        } else {
            // Một mã thẻ hoặc không có - enable input
            $('#modal_xml1_maTheBHYT').prop('readonly', false);
            $('#modal_xml1_gtTheTu').prop('readonly', false);
            $('#modal_xml1_gtTheDen').prop('readonly', false);
        }
    });

    // Validation khi người dùng thay đổi trực tiếp input mã thẻ
    $(document).on('change', '#modal_xml1_maTheBHYT, #modal_xml1_gtTheTu, #modal_xml1_gtTheDen', function() {
        const maTheBHYT = $('#modal_xml1_maTheBHYT').val();
        const gtTheTu = $('#modal_xml1_gtTheTu').val();
        const gtTheDen = $('#modal_xml1_gtTheDen').val();

        // Nếu có đầy đủ thông tin, validate
        if (maTheBHYT && gtTheTu && gtTheDen) {
            const fromDate = new Date(gtTheTu);
            const toDate = new Date(gtTheDen);

            if (fromDate >= toDate) {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi ngày tháng!',
                    text: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc!'
                });
                $(this).focus();
                return;
            }
        }
    });
}

// Lấy dữ liệu mã thẻ hiện tại - UPDATED
function getCurrentCardData() {
    const gtTheTu = $('#modal_xml1_gtTheTu').data('raw-value') || convertDateInputToYyyyMMdd($('#modal_xml1_gtTheTu').val());
    const gtTheDen = $('#modal_xml1_gtTheDen').data('raw-value') || convertDateInputToYyyyMMdd($('#modal_xml1_gtTheDen').val());
    
    return {
        number: $('#modal_xml1_maTheBHYT').val() || '',
        from: gtTheTu || '',
        to: gtTheDen || ''
    };
}

// Tạo HTML cho danh sách mã thẻ với nút copy - UPDATED
function generateBHYTCardsListHtml() {
    if (bhytCards.length === 0) {
        return '<em class="text-muted">Chưa có mã thẻ nào</em>';
    }

    let html = '';
    bhytCards.forEach((card, index) => {
        // Format ngày tháng cho hiển thị (dd/MM/yyyy)
        const formattedFrom = convertFromYyyyMMddToDisplay(card.from);
        const formattedTo = convertFromYyyyMMddToDisplay(card.to);
        
        html += `
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <strong>Mã thẻ:</strong> 
                            <span class="card-number-text" style="cursor: pointer; color: #007bff;" onclick="copyCardNumber('${card.number}')" title="Click để copy mã thẻ">
                                ${card.number} <i class="fas fa-copy fa-xs"></i>
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Từ:</strong> <span class="text-primary">${formattedFrom}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Đến:</strong> <span class="text-primary">${formattedTo}</span>
                        </div>
                        <div class="col-md-2 text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editBHYTCard(${index})" title="Sửa">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${bhytCards.length > 1 ? 
                                `<button type="button" class="btn btn-sm btn-outline-danger ml-1" onclick="deleteBHYTCard(${index})" title="Xóa">
                                    <i class="fas fa-trash"></i>
                                </button>` : 
                                `<button type="button" class="btn btn-sm btn-outline-secondary ml-1" disabled title="Không thể xóa mã thẻ duy nhất">
                                    <i class="fas fa-trash"></i>
                                </button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    return html;
}

// Copy mã thẻ vào input
window.copyCardNumber = function(cardNumber) {
    $('#swal-bhyt-number').val(cardNumber);
    $('#swal-bhyt-number').focus();
    
    // Hiển thị thông báo ngắn
    const originalTitle = Swal.getTitle().innerHTML;
    Swal.getTitle().innerHTML = `<span class="text-success"><i class="fas fa-check"></i> Đã copy mã thẻ: ${cardNumber}</span>`;
    setTimeout(() => {
        Swal.getTitle().innerHTML = originalTitle;
    }, 2000);
};

// Tự động cộng 12 tháng cho ngày kết thúc - UPDATED
function addMonthsToDate(dateString, months) {
    const date = new Date(dateString);
    // Cộng thêm số tháng
    date.setMonth(date.getMonth() + months);
    // Trừ 1 ngày để có ngày cuối của khoảng thời gian
    date.setDate(date.getDate() - 1);
    return date.toISOString().split('T')[0];
}

// Kiểm tra khoảng thời gian có trùng lặp không - UPDATED
function isDateRangeOverlapForSameCard(cardNumber, newFrom, newTo, existingCards, excludeIndex = -1) {
    const newFromDate = new Date(convertYyyyMMddToDateInput(newFrom));
    const newToDate = new Date(convertYyyyMMddToDateInput(newTo));

    // Chỉ kiểm tra với các thẻ có cùng mã số
    for (let i = 0; i < existingCards.length; i++) {
        if (i === excludeIndex) continue; // Bỏ qua thẻ đang sửa
        
        const existingCard = existingCards[i];
        
        // Chỉ kiểm tra nếu cùng mã thẻ
        if (existingCard.number === cardNumber) {
            const existingFromDate = new Date(convertYyyyMMddToDateInput(existingCard.from));
            const existingToDate = new Date(convertYyyyMMddToDateInput(existingCard.to));

            // Kiểm tra trùng lặp khoảng thời gian
            if (newFromDate < existingToDate && newToDate > existingFromDate) {
                return {
                    isOverlap: true,
                    conflictCard: existingCard,
                    conflictIndex: i
                };
            }
        }
    }

    return { isOverlap: false };
}

// Setup event handlers cho dialog - UPDATED
function setupBHYTCardDialogEvents() {
    // Đảm bảo inputs có thể hoạt động
    const popup = Swal.getPopup();
    if (popup) {
        // Force enable tất cả inputs
        const inputs = popup.querySelectorAll('input');
        inputs.forEach(input => {
            input.readOnly = false;
            input.disabled = false;
            input.removeAttribute('readonly');
            input.removeAttribute('disabled');
        });
    }

    // Tự động điền mã thẻ hiện tại - UPDATED
    $('#swal-auto-fill-current').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const currentData = getCurrentCardData();
        if (currentData.number) {
            $('#swal-bhyt-number').val(currentData.number);
            if (currentData.from) {
                const dateInputFrom = convertYyyyMMddToDateInput(currentData.from);
                $('#swal-bhyt-from').val(dateInputFrom);
            }
            if (currentData.to) {
                const dateInputTo = convertYyyyMMddToDateInput(currentData.to);
                $('#swal-bhyt-to').val(dateInputTo);
            }
            
            // Focus vào input mã thẻ
            $('#swal-bhyt-number').focus();
            
            // Hiển thị thông báo
            const originalTitle = Swal.getTitle().innerHTML;
            Swal.getTitle().innerHTML = `<span class="text-success"><i class="fas fa-check"></i> Đã tự động điền thông tin mã thẻ hiện tại</span>`;
            setTimeout(() => {
                Swal.getTitle().innerHTML = originalTitle;
            }, 2000);
        } else {
            Swal.showValidationMessage('Không có mã thẻ hiện tại để tự động điền!');
        }
    });

    // Tự động cộng 12 tháng trừ 1 ngày khi nhập ngày bắt đầu
    $('#swal-bhyt-from').off('change').on('change', function(e) {
        const fromDate = $(this).val();
        if (fromDate) {
            const toDate = addMonthsToDate(fromDate, 12);
            $('#swal-bhyt-to').val(toDate);
        }
    });

    // Thêm mã thẻ - UPDATED
    $('#swal-add-bhyt-card').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const cardNumber = $('#swal-bhyt-number').val().trim();
        const cardFrom = $('#swal-bhyt-from').val();
        const cardTo = $('#swal-bhyt-to').val();

        if (!cardNumber || !cardFrom || !cardTo) {
            Swal.showValidationMessage('Vui lòng điền đầy đủ thông tin mã thẻ!');
            return;
        }

        // Kiểm tra ngày từ phải nhỏ hơn ngày đến
        const newFromDate = new Date(cardFrom);
        const newToDate = new Date(cardTo);
        if (newFromDate >= newToDate) {
            Swal.showValidationMessage('Ngày bắt đầu phải nhỏ hơn ngày kết thúc!');
            return;
        }

        // Chuyển đổi sang format yyyyMMdd để lưu trữ
        const yyyyMMddFrom = convertDateInputToYyyyMMdd(cardFrom);
        const yyyyMMddTo = convertDateInputToYyyyMMdd(cardTo);

        // Kiểm tra trùng lặp khoảng thời gian với cùng mã thẻ
        const overlapCheck = isDateRangeOverlapForSameCard(cardNumber, yyyyMMddFrom, yyyyMMddTo, bhytCards, editingIndex);
        if (overlapCheck.isOverlap) {
            const formatDate = (yyyyMMdd) => convertFromYyyyMMddToDisplay(yyyyMMdd);
            Swal.showValidationMessage(
                `Mã thẻ "${cardNumber}" đã có khoảng thời gian ${formatDate(overlapCheck.conflictCard.from)} - ${formatDate(overlapCheck.conflictCard.to)} ` +
                `trùng với khoảng thời gian mới ${formatDate(yyyyMMddFrom)} - ${formatDate(yyyyMMddTo)}. ` +
                `Vui lòng chọn khoảng thời gian khác!`
            );
            return;
        }

        const cardData = {
            number: cardNumber,
            from: yyyyMMddFrom,
            to: yyyyMMddTo
        };

        if (editingIndex >= 0) {
            // Sửa mã thẻ
            bhytCards[editingIndex] = cardData;
            editingIndex = -1;
            $('#swal-add-btn-text').text('Thêm mã thẻ');
            $('#swal-cancel-edit-bhyt').hide();
        } else {
            // Thêm mã thẻ mới
            bhytCards.push(cardData);
        }

        // Reset form
        $('#swal-bhyt-number').val('');
        $('#swal-bhyt-from').val('');
        $('#swal-bhyt-to').val('');

        // Cập nhật danh sách
        $('#swal-bhyt-cards-list').html(generateBHYTCardsListHtml());
        
        // Đảm bảo inputs vẫn có thể hoạt động sau khi cập nhật
        setTimeout(() => {
            const inputs = Swal.getPopup().querySelectorAll('input');
            inputs.forEach(input => {
                input.readOnly = false;
                input.disabled = false;
                input.removeAttribute('readonly');
                input.removeAttribute('disabled');
            });
        }, 50);
    });

    // Hủy sửa
    $('#swal-cancel-edit-bhyt').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        editingIndex = -1;
        $('#swal-bhyt-number').val('');
        $('#swal-bhyt-from').val('');
        $('#swal-bhyt-to').val('');
        $('#swal-add-btn-text').text('Thêm mã thẻ');
        $(this).hide();
    });
}

// Sửa mã thẻ - UPDATED
window.editBHYTCard = function(index) {
    const card = bhytCards[index];
    $('#swal-bhyt-number').val(card.number);
    
    // Chuyển đổi từ yyyyMMdd sang yyyy-MM-dd cho input date
    const dateInputFrom = convertYyyyMMddToDateInput(card.from);
    const dateInputTo = convertYyyyMMddToDateInput(card.to);
    
    $('#swal-bhyt-from').val(dateInputFrom);
    $('#swal-bhyt-to').val(dateInputTo);

    editingIndex = index;
    $('#swal-add-btn-text').text('Cập nhật mã thẻ');
    $('#swal-cancel-edit-bhyt').show();
    
    // Focus vào input mã thẻ
    setTimeout(() => {
        $('#swal-bhyt-number').focus();
    }, 100);
};

// Xóa mã thẻ - giữ nguyên
window.deleteBHYTCard = function(index) {
    // Kiểm tra nếu chỉ có 1 mã thẻ thì không cho xóa
    if (bhytCards.length <= 1) {
        const originalTitle = Swal.getTitle().innerHTML;
        Swal.getTitle().innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Không thể xóa mã thẻ duy nhất!</span>`;
        setTimeout(() => {
            Swal.getTitle().innerHTML = originalTitle;
        }, 2000);
        return;
    }

    // Xóa trực tiếp
    bhytCards.splice(index, 1);

    // Reset form nếu đang sửa mã thẻ bị xóa
    if (editingIndex === index) {
        $('#swal-cancel-edit-bhyt').click();
    } else if (editingIndex > index) {
        editingIndex--;
    }

    // Cập nhật danh sách
    $('#swal-bhyt-cards-list').html(generateBHYTCardsListHtml());
    
    // Hiển thị thông báo xóa thành công
    const originalTitle = Swal.getTitle().innerHTML;
    Swal.getTitle().innerHTML = `<span class="text-success"><i class="fas fa-check"></i> Đã xóa mã thẻ thành công!</span>`;
    setTimeout(() => {
        Swal.getTitle().innerHTML = originalTitle;
    }, 2000);
};

// Lưu mã thẻ vào modal - UPDATED: Lưu dạng yyyyMMdd
function saveBHYTCardsToModal() {
    console.log('=== saveBHYTCardsToModal DEBUG ===');
    console.log('bhytCards:', bhytCards);
    
    if (bhytCards.length === 0) {
        // Xóa tất cả dữ liệu
        $('#modal_xml1_maTheBHYT').val('');
        $('#modal_xml1_gtTheTu').val('').removeData('raw-value');
        $('#modal_xml1_gtTheDen').val('').removeData('raw-value');
        
        // Chuyển đổi hiển thị input
        switchBHYTInputDisplay();
        
        // Enable input
        $('#modal_xml1_maTheBHYT').prop('readonly', false);
        $('#modal_xml1_gtTheTu').prop('readonly', false);
        $('#modal_xml1_gtTheDen').prop('readonly', false);
        
    } else if (bhytCards.length === 1) {
        // Một mã thẻ - lưu dạng yyyyMMdd
        const card = bhytCards[0];
        console.log('Single card:', card);
        
        $('#modal_xml1_maTheBHYT').val(card.number);
        
        // Lưu raw data dạng yyyyMMdd
        $('#modal_xml1_gtTheTu').data('raw-value', card.from);
        $('#modal_xml1_gtTheDen').data('raw-value', card.to);
        
        // Set giá trị cho input date (yyyy-MM-dd)
        const dateInputFrom = convertYyyyMMddToDateInput(card.from);
        const dateInputTo = convertYyyyMMddToDateInput(card.to);
        $('#modal_xml1_gtTheTu').val(dateInputFrom);
        $('#modal_xml1_gtTheDen').val(dateInputTo);
        
        // Chuyển đổi hiển thị input
        switchBHYTInputDisplay();
        
        // Enable input
        $('#modal_xml1_maTheBHYT').prop('readonly', false);
        $('#modal_xml1_gtTheTu').prop('readonly', false);
        $('#modal_xml1_gtTheDen').prop('readonly', false);
        
    } else {
        // Nhiều mã thẻ - lưu dạng yyyyMMdd;yyyyMMdd
        const cardNumbers = bhytCards.map(card => card.number).join(';');
        const fromDates = bhytCards.map(card => card.from).join(';'); // yyyyMMdd;yyyyMMdd
        const toDates = bhytCards.map(card => card.to).join(';'); // yyyyMMdd;yyyyMMdd
        
        console.log('Multiple cards data:');
        console.log('- cardNumbers:', cardNumbers);
        console.log('- fromDates (yyyyMMdd):', fromDates);
        console.log('- toDates (yyyyMMdd):', toDates);
        
        // Set mã thẻ vào input text
        $('#modal_xml1_maTheBHYT').val(cardNumbers);
        
        // Lưu raw data dạng yyyyMMdd;yyyyMMdd vào data attributes
        $('#modal_xml1_gtTheTu').val('').data('raw-value', fromDates);
        $('#modal_xml1_gtTheDen').val('').data('raw-value', toDates);
        
        console.log('Values set to inputs:');
        console.log('- maTheBHYT:', $('#modal_xml1_maTheBHYT').val());
        console.log('- gtTheTu raw-value:', $('#modal_xml1_gtTheTu').data('raw-value'));
        console.log('- gtTheDen raw-value:', $('#modal_xml1_gtTheDen').data('raw-value'));
        
        // Đợi một chút để DOM cập nhật trước khi chuyển đổi hiển thị
        setTimeout(() => {
            switchBHYTInputDisplay();
        }, 100);
        
        // Disable input khi có nhiều mã thẻ
        $('#modal_xml1_maTheBHYT').prop('readonly', true);
        $('#modal_xml1_gtTheTu').prop('readonly', true);
        $('#modal_xml1_gtTheDen').prop('readonly', true);

        // Hiển thị thông báo
        const message = `Đã lưu ${bhytCards.length} mã thẻ BHYT với khoảng thời gian riêng biệt. Các trường sẽ bị khóa chỉnh sửa, vui lòng sử dụng nút quản lý để thay đổi.`;

        Swal.fire({
            icon: 'success',
            title: 'Thành công!',
            text: message,
            timer: 4000
        });
    }
    
    console.log('=== End saveBHYTCardsToModal DEBUG ===');
}

// Hiển thị dialog quản lý mã thẻ BHYT bằng SweetAlert - giữ nguyên
function showBHYTCardManagementDialog() {
    const currentCardsHtml = generateBHYTCardsListHtml();
    const currentCardData = getCurrentCardData();

    Swal.fire({
        title: 'Quản lý mã thẻ BHYT',
        html: `
            <div class="text-left">
                <div class="mb-3">
                    <label class="font-weight-bold">Danh sách mã thẻ hiện tại:</label>
                    <div id="swal-bhyt-cards-list" class="border p-2 bg-light" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                        ${currentCardsHtml}
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Thêm/Sửa mã thẻ BHYT</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 form-group">
                                <label for="swal-bhyt-number">Mã thẻ BHYT <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" id="swal-bhyt-number" placeholder="VD: HT2646421975323" autocomplete="off">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="swal-auto-fill-current" title="Tự động điền mã thẻ hiện tại">
                                            <i class="fas fa-magic"></i> Tự động
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                <label for="swal-bhyt-from">Giá trị từ ngày <span class="text-danger">*</span></label>
                                <input type="date" class="form-control form-control-sm" id="swal-bhyt-from" autocomplete="off">
                            </div>
                            <div class="col-md-6 form-group">
                                <label for="swal-bhyt-to">Giá trị đến ngày <span class="text-danger">*</span></label>
                                <input type="date" class="form-control form-control-sm" id="swal-bhyt-to" autocomplete="off">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-primary btn-sm" id="swal-add-bhyt-card">
                                    <i class="fas fa-plus"></i> <span id="swal-add-btn-text">Thêm mã thẻ</span>
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm ml-2" id="swal-cancel-edit-bhyt" style="display: none;">
                                    <i class="fas fa-times"></i> Hủy sửa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `,
        width: '800px',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-save"></i> Lưu thay đổi',
        cancelButtonText: 'Đóng',
        allowOutsideClick: false,
        allowEscapeKey: false,
        focusConfirm: false,
        focusTrap: false,
        didOpen: () => {
            const popup = Swal.getPopup();
            
            if (popup) {
                popup.addEventListener('click', function(e) {
                    e.stopPropagation();
                });

                const inputs = popup.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.removeAttribute('readonly');
                    input.removeAttribute('disabled');
                    input.removeAttribute('tabindex');
                    
                    input.style.pointerEvents = 'auto';
                    input.style.userSelect = 'auto';
                    
                    input.addEventListener('focus', function(e) {
                        e.stopPropagation();
                        this.focus();
                    });
                    
                    input.addEventListener('click', function(e) {
                        e.stopPropagation();
                        this.focus();
                    });
                });

                const bhytNumberInput = popup.querySelector('#swal-bhyt-number');
                if (bhytNumberInput) {
                    bhytNumberInput.readOnly = false;
                    bhytNumberInput.disabled = false;
                    
                    bhytNumberInput.addEventListener('mousedown', function(e) {
                        e.stopPropagation();
                    });
                    
                    bhytNumberInput.addEventListener('mouseup', function(e) {
                        e.stopPropagation();
                        this.focus();
                    });
                    
                    bhytNumberInput.addEventListener('keydown', function(e) {
                        e.stopPropagation();
                    });
                    
                    bhytNumberInput.addEventListener('keyup', function(e) {
                        e.stopPropagation();
                    });
                    
                    bhytNumberInput.addEventListener('input', function(e) {
                        e.stopPropagation();
                    });
                }

                setTimeout(() => {
                    inputs.forEach(input => {
                        input.tabIndex = 0;
                    });
                }, 100);
            }

            setupBHYTCardDialogEvents();
        },
        didRender: () => {
            const popup = Swal.getPopup();
            if (popup) {
                const inputs = popup.querySelectorAll('input');
                inputs.forEach(input => {
                    input.removeAttribute('readonly');
                    input.removeAttribute('disabled');
                });
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            saveBHYTCardsToModal();
        }
    });
}

// ===== FIXED BHYT DATA CONVERSION LOGIC =====

// Chuyển đổi dữ liệu BHYT từ text inputs sang format chuẩn trước khi lưu - FIXED

// Chuyển đổi nhiều ngày từ dd/MM/yyyy;dd/MM/yyyy sang yyyyMMdd;yyyyMMdd
function convertMultipleDatesFromDisplayToYyyyMMdd(datesString) {
    if (!datesString) return '';
    
    console.log('convertMultipleDatesFromDisplayToYyyyMMdd input:', datesString);
    
    // Tách các ngày bằng dấu ;
    const dates = datesString.split(';');
    const convertedDates = [];
    
    dates.forEach(dateStr => {
        const trimmedDate = dateStr.trim();
        if (trimmedDate) {
            // Chuyển từ dd/MM/yyyy sang yyyyMMdd
            const yyyyMMdd = convertToYyyyMMdd(trimmedDate);
            if (yyyyMMdd) {
                convertedDates.push(yyyyMMdd);
            }
        }
    });
    
    const result = convertedDates.join(';');
    console.log('convertMultipleDatesFromDisplayToYyyyMMdd output:', result);
    
    return result;
}

function convertBHYTDataBeforeSave() {
    console.log('=== convertBHYTDataBeforeSave START ===');
    
    const maTheBHYT = $('#modal_xml1_maTheBHYT').val();
    console.log('maTheBHYT:', maTheBHYT);
    
    // Kiểm tra xem có nhiều mã thẻ không
    const hasMultipleCards = maTheBHYT && maTheBHYT.includes(';');
    console.log('hasMultipleCards:', hasMultipleCards);
    
    if (hasMultipleCards) {
        // Có nhiều mã thẻ - lấy dữ liệu từ text inputs và chuyển đổi
        const gtTheTuText = $('#modal_xml1_gtTheTu_text').val();
        const gtTheDenText = $('#modal_xml1_gtTheDen_text').val();
        
        console.log('Text inputs values:');
        console.log('- gtTheTuText:', gtTheTuText);
        console.log('- gtTheDenText:', gtTheDenText);
        
        // Chuyển đổi từ dd/MM/yyyy;dd/MM/yyyy sang yyyyMMdd;yyyyMMdd
        const convertedGtTheTu = convertMultipleDatesFromDisplayToYyyyMMdd(gtTheTuText);
        const convertedGtTheDen = convertMultipleDatesFromDisplayToYyyyMMdd(gtTheDenText);
        
        console.log('Converted values:');
        console.log('- convertedGtTheTu:', convertedGtTheTu);
        console.log('- convertedGtTheDen:', convertedGtTheDen);
        
        // *** FIX: Set both raw-value and actual value ***
        $('#modal_xml1_gtTheTu').data('raw-value', convertedGtTheTu).val(convertedGtTheTu);
        $('#modal_xml1_gtTheDen').data('raw-value', convertedGtTheDen).val(convertedGtTheDen);
        
        console.log('Data set to main inputs:');
        console.log('- gtTheTu raw-value:', $('#modal_xml1_gtTheTu').data('raw-value'));
        console.log('- gtTheTu val:', $('#modal_xml1_gtTheTu').val());
        console.log('- gtTheDen raw-value:', $('#modal_xml1_gtTheDen').data('raw-value'));
        console.log('- gtTheDen val:', $('#modal_xml1_gtTheDen').val());
        
    } else {
        // Chỉ có 1 mã thẻ hoặc không có - lấy từ input date và chuyển đổi
        const gtTheTu = $('#modal_xml1_gtTheTu').val();
        const gtTheDen = $('#modal_xml1_gtTheDen').val();
        
        console.log('Single card date inputs:');
        console.log('- gtTheTu (yyyy-MM-dd):', gtTheTu);
        console.log('- gtTheDen (yyyy-MM-dd):', gtTheDen);
        
        // Chuyển đổi từ yyyy-MM-dd sang yyyyMMdd
        const convertedGtTheTu = gtTheTu ? convertDateInputToYyyyMMdd(gtTheTu) : '';
        const convertedGtTheDen = gtTheDen ? convertDateInputToYyyyMMdd(gtTheDen) : '';
        
        console.log('Converted single values:');
        console.log('- convertedGtTheTu:', convertedGtTheTu);
        console.log('- convertedGtTheDen:', convertedGtTheDen);
        
        // Lưu vào raw-value và cập nhật value
        $('#modal_xml1_gtTheTu').data('raw-value', convertedGtTheTu);
        $('#modal_xml1_gtTheDen').data('raw-value', convertedGtTheDen);
        
        console.log('Single card data set to raw-value');
    }
    
    console.log('=== convertBHYTDataBeforeSave END ===');
}

// Cập nhật field transformer để xử lý raw-value - FIXED
function updateBHYTFieldTransformers() {
    // Đảm bảo fieldTransformers tồn tại
    if (typeof fieldTransformers === 'undefined') {
        window.fieldTransformers = {};
    }
    
    if (!fieldTransformers['XML1']) {
        fieldTransformers['XML1'] = {};
    }
    
    // Transformer cho gtTheTu - FIXED
    fieldTransformers['XML1']['gtTheTu'] = {
        fromModal: function(value) {
            console.log('gtTheTu transformer - input value:', value);
            
            // Lấy element để kiểm tra raw-value
            const element = $('#modal_xml1_gtTheTu');
            const rawValue = element.data('raw-value');
            
            console.log('gtTheTu transformer - raw-value from data:', rawValue);
            console.log('gtTheTu transformer - element val:', element.val());
            
            // Ưu tiên raw-value nếu có và không rỗng
            if (rawValue !== undefined && rawValue !== null && rawValue !== '') {
                console.log('gtTheTu transformer - using raw-value:', rawValue);
                return rawValue;
            }
            
            // Nếu không có raw-value, xử lý value thông thường
            if (!value || value === '') {
                console.log('gtTheTu transformer - no value, returning null');
                return null;
            }
            
            // Nếu là định dạng yyyy-MM-dd (từ input date)
            if (value.includes('-')) {
                const converted = convertDateInputToYyyyMMdd(value);
                console.log('gtTheTu transformer - converted from date input:', converted);
                return converted;
            }
            
            console.log('gtTheTu transformer - returning original value:', value);
            return value;
        }
    };
    
    // Transformer cho gtTheDen - FIXED
    fieldTransformers['XML1']['gtTheDen'] = {
        fromModal: function(value) {
            console.log('gtTheDen transformer - input value:', value);
            
            // Lấy element để kiểm tra raw-value
            const element = $('#modal_xml1_gtTheDen');
            const rawValue = element.data('raw-value');
            
            console.log('gtTheDen transformer - raw-value from data:', rawValue);
            console.log('gtTheDen transformer - element val:', element.val());
            
            // Ưu tiên raw-value nếu có và không rỗng
            if (rawValue !== undefined && rawValue !== null && rawValue !== '') {
                console.log('gtTheDen transformer - using raw-value:', rawValue);
                return rawValue;
            }
            
            // Nếu không có raw-value, xử lý value thông thường
            if (!value || value === '') {
                console.log('gtTheDen transformer - no value, returning null');
                return null;
            }
            
            // Nếu là định dạng yyyy-MM-dd (từ input date)
            if (value.includes('-')) {
                const converted = convertDateInputToYyyyMMdd(value);
                console.log('gtTheDen transformer - converted from date input:', converted);
                return converted;
            }
            
            console.log('gtTheDen transformer - returning original value:', value);
            return value;
        }
    };
    
    console.log('BHYT field transformers updated');
}

// Alternative approach: Directly modify the data collection logic
function getBHYTFieldValue(fieldName) {
    const element = $(`#modal_xml1_${fieldName}`);
    const rawValue = element.data('raw-value');
    const inputValue = element.val();
    
    console.log(`getBHYTFieldValue(${fieldName}):`, {
        rawValue: rawValue,
        inputValue: inputValue,
        hasRawValue: rawValue !== undefined && rawValue !== null && rawValue !== ''
    });
    
    // Ưu tiên raw-value nếu có
    if (rawValue !== undefined && rawValue !== null && rawValue !== '') {
        return rawValue;
    }
    
    // Nếu không có raw-value, xử lý input value
    if (!inputValue || inputValue === '') {
        return null;
    }
    
    // Nếu là định dạng yyyy-MM-dd (từ input date)
    if (inputValue.includes('-')) {
        return convertDateInputToYyyyMMdd(inputValue);
    }
    
    return inputValue;
}

// Format date for display
function formatDisplayDate(dateStr) {
    if (!dateStr) return '-';
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('vi-VN');
    } catch (e) {
        return dateStr;
    }
}

// Function to update modal tab titles
function updateModalTabTitles() {
    $('#editPreviewModal .nav-link').each(function() {
        var $this = $(this);
        if ($this.hasClass('active')) {
            $this.find('.tab-short-title').hide();
            $this.find('.tab-full-title').show();
        } else {
            $this.find('.tab-short-title').show();
            $this.find('.tab-full-title').hide();
        }
    });
}

// Modal tab switching events
$('#editPreviewModal .nav-tabs a').on('shown.bs.tab', function (e) {
    console.log('Modal tab switched:', e.target.id);
    updateModalTabTitles();
});

// Also update on modal opening
$('#editPreviewModal').on('show.bs.modal', function() {
    console.log('Modal showing, updating tab titles');
    $('#modal-edit-xml1-tab').click();
});

function handleDeleteModalAction(buttonElement, cell){
    const button = $(buttonElement);
    let row, rowData, id, xmlType, maLK, ngayTao, rowIndex;

    if (cell) {
        // Được gọi từ cellClick - cách đáng tin cậy nhất
        row = cell.getRow();
        rowData = row.getData();
        id = rowData.id;
        xmlType = button.data('type');
        maLK = rowData.maLK || rowData.ma_lk;
        ngayTao = rowData.ngayTao || rowData.ngay_tao;
        rowIndex = row.getPosition() - 1; // Lấy vị trí hiện tại
    }

    // Tìm instance Tabulator của bảng trong modal
    var modalTableId = 'modal_' + xmlType.toLowerCase() + '-edit-table';
    var modalTableElement = document.getElementById(modalTableId);

    if (!modalTableElement || !modalTableElement._tabulator) {
        console.error('Modal Tabulator instance not found for:', modalTableId);
        Swal.fire('Lỗi', 'Không tìm thấy bảng dữ liệu trong modal để xóa.', 'error');
        return;
    }
    var modalTabulatorInstance = modalTableElement._tabulator;

    Swal.fire({
        title: 'Xác nhận xóa',
        text: "Bạn có chắc chắn muốn xóa dòng này không?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy'
    }).then((result) => {
        if (result.isConfirmed) {
            row.delete();
            // Cập nhật lại STT cho các dòng còn lại
            modalTabulatorInstance.getRows().forEach((row, index) => {
                row.update({ stt: index + 1 }); // Cập nhật trường STT
            });
            // Nếu việc xóa XML2, XML3, XML4, XML5 ảnh hưởng đến tổng XML1, hãy gọi tính toán lại
            if (['XML2', 'XML3', 'XML4', 'XML5'].includes(xmlType)) {
                $('#recalculatorBtn').trigger('click');
            }
        }
    });
}

// Xử lý tab switching trong modal để không ảnh hưởng đến tab chính
$('#modalEditXmlTabs a[data-toggle="tab"]').on('click', function (e) {
    e.preventDefault();
    e.stopPropagation();
    
    // Remove active class from all modal tabs
    $('#modalEditXmlTabs .nav-link').removeClass('active');
    $('#modalEditXmlTabsContent .tab-pane').removeClass('show active');
    
    // Add active class to clicked tab
    $(this).addClass('active');
    var targetTab = $(this).attr('href');
    $(targetTab).addClass('show active');
    
    // Redraw Tabulator table ở tab vừa được show (nếu có)
    var tabName = targetTab.replace('#', '');
    var xmlType = tabName.replace('modal-edit-', '');
    var modalTableId = 'modal_' + xmlType + '-edit-table';
    var modalTableElement = document.getElementById(modalTableId);
    if (modalTableElement && modalTableElement._tabulator) {
        modalTableElement._tabulator.redraw(true);
    }
});

// Xử lý thay đổi maLK - đồng bộ với tất cả XML types
$(document).on('change', '#modal_xml1_maLK', function() {
    const newMaLK = $(this).val();
    const oldMaLK = $('#modalEditMaLK').text().trim();

    console.log(`maLK changed from ${oldMaLK} to ${newMaLK}`);

    if (newMaLK && newMaLK !== oldMaLK) {
        // Cập nhật tiêu đề modal
        $('#modalEditMaLK').text(newMaLK);

        // Đồng bộ maLK trong tất cả các XML types trong modal
        syncMaLKInAllTabs(oldMaLK, newMaLK);

        // Hiển thị thông báo
        showNotification(`Đã cập nhật Mã Liên Kết từ ${oldMaLK} thành ${newMaLK}`, 'info');
    }
});

// Hàm đồng bộ maLK trong tất cả các tab
function syncMaLKInAllTabs(oldMaLK, newMaLK) {
    console.log(`syncMaLKInAllTabs: ${oldMaLK} -> ${newMaLK}`);

    // Đồng bộ trong các Tabulator tables trong modal (XML2, XML3, XML4, XML5, XML6, XML9, XML15)
    const modalTableXmlTypes = ['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML9', 'XML15'];

    modalTableXmlTypes.forEach(xmlType => {
        // Modal tables có ID pattern: modal_xml2-edit-table, modal_xml3-edit-table, etc.
        const modalTableElementId = `modal_${xmlType.toLowerCase()}-edit-table`;
        const modalTableElement = document.getElementById(modalTableElementId);

        if (modalTableElement && modalTableElement._tabulator) {
            try {
                const data = modalTableElement._tabulator.getData();
                const updatedData = data.map(row => {
                    if (row.maLK === oldMaLK) {
                        return { ...row, maLK: newMaLK };
                    }
                    return row;
                });

                modalTableElement._tabulator.setData(updatedData);
                console.log(`Updated maLK in modal ${xmlType} table: ${updatedData.length} rows`);
            } catch (error) {
                console.error(`Error updating maLK in modal ${xmlType} table:`, error);
            }
        } else {
            console.warn(`Modal table ${modalTableElementId} not found or not initialized`);
        }
    });

    // Đồng bộ trong các input fields khác (XML0, XML1, XML7, XML8, XML10, XML11, XML12, XML13, XML14)
    const inputXmlTypes = ['XML0', 'XML1', 'XML7', 'XML8', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14'];

    inputXmlTypes.forEach(xmlType => {
        const maLKInput = $(`#modal_${xmlType.toLowerCase()}_maLK`);
        if (maLKInput.length > 0) {
            const oldValue = maLKInput.val();
            if (oldValue === oldMaLK || oldValue === '') {
                maLKInput.val(newMaLK);
                console.log(`Updated maLK in ${xmlType} input field: ${oldValue} -> ${newMaLK}`);
            }
        } else {
            console.warn(`Input field #modal_${xmlType.toLowerCase()}_maLK not found`);
        }
    });

    // Đồng bộ trong main tables nếu đang ở preview mode
    if (window.isDataFromXML || window.currentDataSource === 'imported_xml') {
        console.log('Syncing maLK in main tables (preview mode)');

        for (let i = 0; i <= 15; i++) {
            const xmlType = `XML${i}`;
            const mainTableElementId = xmlType.toLowerCase() + '-table';
            const mainTableElement = document.getElementById(mainTableElementId);

            if (mainTableElement && mainTableElement._tabulator) {
                try {
                    const data = mainTableElement._tabulator.getData();
                    const updatedData = data.map(row => {
                        if (row.maLK === oldMaLK) {
                            return { ...row, maLK: newMaLK };
                        }
                        return row;
                    });

                    if (updatedData.some(row => row.maLK === newMaLK)) {
                        mainTableElement._tabulator.setData(updatedData);
                        console.log(`Updated maLK in main ${xmlType} table`);
                    }
                } catch (error) {
                    console.error(`Error updating maLK in main ${xmlType} table:`, error);
                }
            }
        }
    }
}