<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">

  <title>LAN Insight Guardian | Quên mật khẩu</title>

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="/static/AdminLTE-3.0.1/plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="/static/AdminLTE-3.0.1/dist/css/adminlte.min.css">
  <!-- Google Font: Source Sans Pro -->
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    <a href="{% url 'home' %}"><b>LAN</b> Insight Guardian</a>
  </div>
  <!-- /.login-logo -->
  <div class="card">
    <div class="card-body login-card-body">
      <p class="login-box-msg">Quên mật khẩu? Nhập email của bạn để đặt lại mật khẩu.</p>

      <form action="{% url 'users:password_reset' %}" method="post">
        {% csrf_token %}
        {% if form.non_field_errors %}
        <div class="alert alert-danger">
          {% for error in form.non_field_errors %}
            {{ error }}
          {% endfor %}
        </div>
        {% endif %}
        
        {% if messages %}
        <div class="alert alert-success">
          {% for message in messages %}
            {{ message }}
          {% endfor %}
        </div>
        {% endif %}
        
        <div class="input-group mb-3">
          <input type="email" name="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" placeholder="Email" required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-envelope"></span>
            </div>
          </div>
          {% if form.email.errors %}
          <div class="invalid-feedback">
            {% for error in form.email.errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
        <div class="row">
          <div class="col-12">
            <button type="submit" class="btn btn-primary btn-block">Yêu cầu mật khẩu mới</button>
          </div>
          <!-- /.col -->
        </div>
      </form>

      <p class="mt-3 mb-1">
        <a href="{% url 'users:login' %}">Đăng nhập</a>
      </p>
    </div>
    <!-- /.login-card-body -->
  </div>
</div>
<!-- /.login-box -->

<!-- jQuery -->
<script src="/static/AdminLTE-3.0.1/plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="/static/AdminLTE-3.0.1/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="/static/AdminLTE-3.0.1/dist/js/adminlte.min.js"></script>

</body>
</html>
