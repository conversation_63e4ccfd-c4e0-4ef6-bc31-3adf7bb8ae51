from datetime import datetime, date
import openpyxl
from django.contrib.auth.models import User
import re
import unicodedata

def parse_excel_short_date(value):
    # Nếu đã là datetime/date → chuyển thành chuỗi yyyyMMdd
    if isinstance(value, (datetime, date)):
        return value.strftime("%Y%m%d")

    # Nếu là string hợp lệ
    if isinstance(value, str):
        try:
            return datetime.fromisoformat(value).strftime("%Y%m%d")
        except ValueError:
            try:
                return datetime.strptime(value, "%d/%m/%Y").strftime("%Y%m%d")
            except:
                return None

    # Nếu là float → Excel date (số ngày từ 1900)
    if isinstance(value, (int, float)):
        try:
            date_obj = datetime.fromordinal(datetime(1900, 1, 1).toordinal() + int(value) - 2)
            return date_obj.strftime("%Y%m%d")
        except:
            return None

    return None

def parse_to_short_date(value):
    if not value:
        return ""
    try:
        date_obj = datetime.strptime(value, "%Y%m%d")
        return date_obj.strftime("%d/%m/%Y")
    except (ValueError, TypeError):
        return ""
    
def parse_to_yyyyMMdd(value):
    if not value:
        return ""
    try:
        return datetime.strptime(value, "%d/%m/%Y").strftime("%Y%m%d")
    except (ValueError, TypeError):
        return ""

def generate_username_from_name(full_name: str) -> str:
    # 1. Chuẩn hóa: bỏ dấu, chuyển về thường, xóa ký tự lạ
    normalized = unicodedata.normalize('NFKD', full_name).encode('ascii', 'ignore').decode('utf-8').lower()
    parts = re.findall(r'\w+', normalized)

    if not parts:
        return 'user'

    # 2. Gộp: tên + viết tắt họ lót
    last_name = parts[-1]
    initials = ''.join(p[0] for p in parts[:-1])
    base_username = f"{last_name}{initials}"

    # 3. Kiểm tra trùng username
    username = base_username
    count = 2
    while User.objects.filter(username=username).exists():
        username = f"{base_username}{count}"
        count += 1

    return username