from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.contrib import messages
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST, require_GET, require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import FieldDoesNotExist
import pandas as pd
import logging
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from io import BytesIO
from datetime import datetime

from .models import (
    Tinh, QuanHuyen, XaPhuong, NoiDangKiBanDau, PhamViChuyenMon
)

from .forms import (
    TinhForm, QuanHuyenForm, XaPhuongForm, NoiDangKiBanDauForm, PhamViChuyenMonForm
)

logger = logging.getLogger(__name__)

# Dictionary ánh xạ tên danh mục với model tương ứng
CATEGORY_MODEL_MAP = {
    'tinh': Tinh,
    'quanhuyen': QuanHuyen,
    'xaphuong': XaPhuong,
    'noidkbd': NoiDangKiBanDau,
    'pvcm': PhamViChuyenMon,
}

@login_required
@require_GET
def get_category_data(request):
    """
    API endpoint chung để lấy dữ liệu từ các model danh mục
    """
    try:
        # Lấy tên danh mục từ tham số
        category = request.GET.get('category')
        active_only = request.GET.get('active_only', 'true').lower() == 'true'
        item_id = request.GET.get('id')

        # Kiểm tra xem danh mục có tồn tại không
        if not category or category not in CATEGORY_MODEL_MAP:
            return JsonResponse({
                'success': False,
                'error': f'Danh mục không hợp lệ. Các danh mục hợp lệ: {", ".join(CATEGORY_MODEL_MAP.keys())}'
            }, status=400)

        # Lấy model tương ứng với danh mục
        model = CATEGORY_MODEL_MAP[category]

        # Lấy dữ liệu từ model
        if category == 'tinh':
            queryset = model.objects.all().order_by('ma_tinh')
        elif category == 'quanhuyen':
            queryset = model.objects.all().order_by('ma_quan_huyen')
        elif category == 'xaphuong':
            queryset = model.objects.all().order_by('ma_xa_phuong')
        else:
            queryset = model.objects.all()

        # Lọc theo ID nếu có
        if item_id:
            queryset = queryset.filter(id=item_id)

        # Lọc theo trạng thái hiệu lực nếu cần
        if active_only:
            queryset = queryset.filter(hieu_luc=True)

        # Lọc theo mã tỉnh nếu có
        ma_tinh = request.GET.get('ma_tinh')
        if ma_tinh:
            queryset = queryset.filter(ma_tinh=ma_tinh)

        # Chuyển đổi thành danh sách các dict với mã và tên/diễn giải tùy theo format
        result = []
        for item in queryset:
            if category == 'tinh':
                result.append({
                    'id': item.id,
                    'ma_tinh': item.ma_tinh,
                    'ten_tinh': item.ten_tinh,
                    'hieu_luc': item.hieu_luc,
                })
            elif category == 'quanhuyen':
                result.append({
                    'id': item.id,
                    'ma_quan_huyen': item.ma_quan_huyen,
                    'ten_quan_huyen': item.ten_quan_huyen,
                    'ma_tinh': item.ma_tinh.ma_tinh if hasattr(item, 'ma_tinh') else None,
                    'hieu_luc': item.hieu_luc,
                })
            elif category == 'xaphuong':
                result.append({
                    'id': item.id,
                    'ma_xa_phuong': item.ma_xa_phuong,
                    'ten_xa_phuong': item.ten_xa_phuong,
                    'ma_quan_huyen': item.ma_quan_huyen.ma_quan_huyen if hasattr(item, 'ma_quan_huyen') else None,
                    'ma_tinh': item.ma_tinh.ma_tinh if hasattr(item, 'ma_tinh') else None,
                    'hieu_luc': item.hieu_luc,
                })
            else:
                result.append({
                    'id': item.id,
                    'ma': item.ma,
                    'ten': item.ten,
                    'hieu_luc': item.hieu_luc,
                })

        return JsonResponse({'success': True, 'data': result})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

def base_list_view(request, model, form_class, template_name, title,
                   ordering_fields=None, searchable_fields=None, permission_required=None):
    """Base view for listing and managing model data"""
    if permission_required and not request.user.has_perm(permission_required):
        messages.error(request, _('Bạn không có quyền truy cập trang này.'))
        return redirect('home') # Hoặc một trang lỗi chung

    current_form = None
    if form_class:
        if request.method == 'POST':
            current_form = form_class(request.POST)
            if current_form.is_valid():
                instance = current_form.save()
                messages.success(request, _(f'{model._meta.verbose_name.capitalize()} "{instance}" đã được lưu thành công.'))
                return redirect(request.path)
        else:
            current_form = form_class()

    # Get list data
    queryset = model.objects.all()

    # Apply ordering
    if ordering_fields:
        queryset = queryset.order_by(*ordering_fields)
    else:
        # Default ordering by 'id' or 'pk' if available, or model's Meta.ordering
        default_order_field = None
        if hasattr(model._meta, 'ordering') and model._meta.ordering:
            pass # Use model's default ordering
        else:
            try:
                model._meta.get_field('id')
                default_order_field = 'id'
            except FieldDoesNotExist:
                try:
                    model._meta.get_field('pk')
                    default_order_field = 'pk'
                except FieldDoesNotExist:
                    pass 
            if default_order_field:
                queryset = queryset.order_by(default_order_field)

    # Handle search
    search_query = request.GET.get('search', '')
    if search_query and searchable_fields:
        q_objects = Q()
        for field_name_query in searchable_fields:
            actual_field_name_on_model = field_name_query.split('__')[0]
            try:
                model_field = model._meta.get_field(actual_field_name_on_model)
                # Only search text-based fields with icontains
                if model_field.get_internal_type() in ['CharField', 'TextField', 'SlugField']:
                    q_objects |= Q(**{f"{field_name_query}__icontains": search_query})
                # Example: Search exact match for IntegerField if query is a digit
                # elif model_field.get_internal_type() in ['IntegerField', 'PositiveIntegerField'] and search_query.isdigit():
                #     q_objects |= Q(**{f"{field_name_query}": int(search_query)})
            except FieldDoesNotExist:
                logger.warning(f"Search field '{actual_field_name_on_model}' (from '{field_name_query}') not found in model {model.__name__}")
        
        if q_objects: # Apply filter only if q_objects is not empty
            queryset = queryset.filter(q_objects)

    # Handle pagination
    paginator = Paginator(queryset, 10) # Number of items per page can be configurable
    page_number = request.GET.get('page')
    items_page = paginator.get_page(page_number)

    context = {
        'title': title,
        'form': current_form,
        'items': items_page,
        'search': search_query,
        'model_name_plural': model._meta.verbose_name_plural.capitalize()
    }
    return render(request, template_name, context)


@login_required
def tinh_list(request):
    """Danh sách Tỉnh"""
    return base_list_view(
        request=request,
        model=Tinh,
        form_class=TinhForm,
        template_name='danhmuckhac/tinh_list.html',
        title=_('Danh mục tỉnh'),
        ordering_fields=['ma_tinh'],
        searchable_fields=['ma_tinh', 'ten_tinh'],
        permission_required='danhmuckhac.view_tinh'
    )

@login_required
@require_GET
def tinh_list_api(request):
    """API để lấy danh sách tỉnh cho Tabulator"""
    queryset = Tinh.objects.all().order_by('ma_tinh')
    data = []
    for tinh in queryset:
        data.append({
            'id': tinh.id,
            'ma_tinh': tinh.ma_tinh,
            'ten_tinh': tinh.ten_tinh,
            'hieu_luc': tinh.hieu_luc,
        })
    return JsonResponse({'data': data})


@login_required
@require_POST
def tinh_create(request):
    """Tạo mới tỉnh"""
    try:
        # Log dữ liệu nhận được để debug
        logger.info(f"Creating new tinh, POST data: {request.POST}")
        
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = TinhForm(post_data)
        
        if form.is_valid():
            tinh = form.save()
            logger.info(f"User {request.user.username} đã tạo tỉnh mới: {tinh.ma_tinh}-{tinh.ten_tinh} (ID: {tinh.id})")
            
            return JsonResponse({
                'success': True,
                'message': f'Đã thêm tỉnh/thành phố "{tinh.ten_tinh}" thành công',
                'data': {
                    'id': tinh.id,
                    'ma_tinh': tinh.ma_tinh,
                    'ten_tinh': tinh.ten_tinh,
                    'hieu_luc': tinh.hieu_luc
                }
            })
        else:
            # Log lỗi validation để debug
            logger.error(f"Form validation errors for new tinh: {form.errors}")
            
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'success': False,
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except Exception as e:
        logger.error(f"Lỗi khi tạo tỉnh: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def tinh_edit(request, pk):
    """Chỉnh sửa tỉnh"""
    try:
        tinh = get_object_or_404(Tinh, pk=pk)
        
        # Log dữ liệu nhận được để debug
        logger.info(f"Editing tinh ID {pk}, POST data: {request.POST}")
        
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = TinhForm(post_data, instance=tinh)
        
        if form.is_valid():
            old_name = tinh.ten_tinh
            old_ma = tinh.ma_tinh
            
            # Lưu form
            updated_tinh = form.save()
            
            logger.info(f"User {request.user.username} đã cập nhật tỉnh: {old_ma}-{old_name} -> {updated_tinh.ma_tinh}-{updated_tinh.ten_tinh} (ID: {updated_tinh.id})")
            
            return JsonResponse({
                'status': 'success',
                'message': f'Đã cập nhật tỉnh/thành phố "{updated_tinh.ten_tinh}" thành công',
                'data': {
                    'id': updated_tinh.id,
                    'ma_tinh': updated_tinh.ma_tinh,
                    'ten_tinh': updated_tinh.ten_tinh,
                    'hieu_luc': updated_tinh.hieu_luc
                }
            })
        else:
            # Log lỗi validation để debug
            logger.error(f"Form validation errors for tinh ID {pk}: {form.errors}")
            
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'status': 'error',
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except Tinh.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng cập nhật tỉnh không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy tỉnh/thành phố'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật tỉnh ID {pk}: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def tinh_delete(request, pk):
    """Xóa tỉnh"""
    try:
        tinh = get_object_or_404(Tinh, pk=pk)
        ten_tinh = tinh.ten_tinh
        
        # Kiểm tra xem tỉnh có đang được sử dụng không
        # Ví dụ: kiểm tra trong QuanHuyen
        if hasattr(tinh, 'quanhuyen_set') and tinh.quanhuyen_set.exists():
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể xóa tỉnh "{ten_tinh}" vì đang có quận/huyện thuộc tỉnh này'
            }, status=400)
        
        tinh.delete()
        logger.info(f"User {request.user.username} đã xóa tỉnh: {ten_tinh} (ID: {pk})")
        
        return JsonResponse({
            'status': 'success',
            'message': f'Đã xóa tỉnh/thành phố "{ten_tinh}" thành công'
        })
        
    except Tinh.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng xóa tỉnh không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy tỉnh/thành phố'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi xóa tỉnh ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def tinh_toggle_hieu_luc(request, pk):
    """Toggle hiệu lực của tỉnh"""
    try:
        tinh = Tinh.objects.get(pk=pk)
        hieu_luc = request.POST.get('hieu_luc') == 'true'
        
        # Lưu trạng thái cũ để log
        old_status = tinh.hieu_luc
        tinh.hieu_luc = hieu_luc
        tinh.save()
        
        # Log thay đổi
        action = 'kích hoạt' if hieu_luc else 'hủy kích hoạt'
        logger.info(f"User {request.user.username} đã {action} hiệu lực cho tỉnh {tinh.ten_tinh} (ID: {tinh.id})")
        
        message = f"Đã {action} hiệu lực cho tỉnh/thành phố '{tinh.ten_tinh}'"
        
        return JsonResponse({
            'status': 'success',
            'message': message,
            'hieu_luc': hieu_luc,
            'data': {
                'id': tinh.id,
                'ma_tinh': tinh.ma_tinh,
                'ten_tinh': tinh.ten_tinh,
                'hieu_luc': tinh.hieu_luc
            }
        })
        
    except Tinh.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng toggle hiệu lực cho tỉnh không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy tỉnh/thành phố'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi toggle hiệu lực tỉnh ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
def tinh_export(request):
    """Xuất danh sách tỉnh ra Excel"""
    try:
        # Lấy dữ liệu
        queryset = Tinh.objects.all().order_by('ma_tinh')
        
        # Tạo workbook và worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        # Sửa tên sheet - loại bỏ ký tự đặc biệt
        ws.title = "Danh sach Tinh Thanh pho"  # Không dùng dấu / và ký tự đặc biệt
        
        # Định nghĩa style
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Header
        headers = ['STT', 'Mã tỉnh', 'Tên Tỉnh/Thành phố', 'Hiệu lực']  # Loại bỏ dấu
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Dữ liệu
        for row, tinh in enumerate(queryset, 2):
            ws.cell(row=row, column=1, value=row-1).border = border  # STT
            ws.cell(row=row, column=2, value=tinh.ma_tinh).border = border
            ws.cell(row=row, column=3, value=tinh.ten_tinh).border = border
            ws.cell(row=row, column=4, value="Có" if tinh.hieu_luc else "Không").border = border  # Loại bỏ dấu
        
        # Điều chỉnh độ rộng cột
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 40
        ws.column_dimensions['D'].width = 12
        
        # Tạo response
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        filename = f"danh_sach_tinh_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        logger.info(f"User {request.user.username} đã xuất danh sách tỉnh ra Excel")
        
        return response
        
    except Exception as e:
        logger.error(f"Lỗi khi xuất Excel: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Có lỗi xảy ra khi xuất Excel: {str(e)}'
        }, status=500)
@login_required
@require_POST
def tinh_import(request):
    """Import danh sách tỉnh từ Excel với bulk operations"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'status': 'error',
                'message': 'Vui lòng chọn file Excel'
            }, status=400)

        file = request.FILES['file']
        
        # Kiểm tra định dạng file
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({
                'status': 'error',
                'message': 'File phải có định dạng .xlsx, .xls hoặc .csv'
            }, status=400)

        # Đọc file Excel với dtype=str để giữ nguyên format
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file, dtype=str)
            else:
                df = pd.read_excel(file, dtype=str)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể đọc file: {str(e)}'
            }, status=400)

        # Tìm cột phù hợp
        available_columns = df.columns.tolist()
        
        ma_tinh_columns = ['Mã', 'ma', 'Ma', 'MA', 'mã', 'ma_tinh', 'Ma_tinh', 'MA_TINH', 'Mã tỉnh', 'Ma tinh']
        ten_tinh_columns = ['Tên', 'ten', 'Ten', 'TEN', 'tên', 'ten_tinh', 'Ten_tinh', 'TEN_TINH', 'Tên tỉnh', 'Ten tinh', 'Tên Tỉnh']
        hieu_luc_columns = ['Hiệu lực', 'hieu_luc', 'Hieu_luc', 'HIEU_LUC', 'hiệu lực', 'Hieu luc', 'Status', 'Active']
        
        ma_column = next((col for col in ma_tinh_columns if col in available_columns), None)
        ten_column = next((col for col in ten_tinh_columns if col in available_columns), None)
        hieu_luc_column = next((col for col in hieu_luc_columns if col in available_columns), None)
        
        if not ma_column or not ten_column:
            missing = []
            if not ma_column:
                missing.append(f'Cột mã tỉnh (một trong: {", ".join(ma_tinh_columns[:5])}...)')
            if not ten_column:
                missing.append(f'Cột tên tỉnh (một trong: {", ".join(ten_tinh_columns[:5])}...)')
            
            return JsonResponse({
                'status': 'error',
                'message': f'File thiếu các cột bắt buộc: {"; ".join(missing)}'
            }, status=400)

        # BƯỚC 1: Validate và chuẩn hóa dữ liệu
        valid_records = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Xử lý mã tỉnh
                ma_tinh_raw = row[ma_column]
                if pd.isna(ma_tinh_raw) or str(ma_tinh_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã tỉnh không được để trống')
                    continue
                
                ma_tinh = str(ma_tinh_raw).strip()
                if ma_tinh.replace('.', '').replace(',', '').isdigit():
                    ma_tinh = str(int(float(ma_tinh))).zfill(2)
                
                # Xử lý tên tỉnh
                ten_tinh = str(row[ten_column]).strip() if pd.notna(row[ten_column]) else ''
                if not ten_tinh or ten_tinh.lower() in ['nan', 'null']:
                    errors.append(f'Dòng {index + 2}: Tên tỉnh không được để trống')
                    continue
                
                # Xử lý hiệu lực
                hieu_luc = True
                if hieu_luc_column and pd.notna(row[hieu_luc_column]):
                    hieu_luc_value = str(row[hieu_luc_column]).strip().lower()
                    hieu_luc = hieu_luc_value in ['true', '1', 'có', 'yes', 'y', 'active', 'hiệu lực', 'hieu luc']
                
                # Validate định dạng mã tỉnh
                if not ma_tinh.isdigit() or len(ma_tinh) != 2:
                    errors.append(f'Dòng {index + 2}: Mã tỉnh phải là 2 chữ số (hiện tại: "{ma_tinh_raw}" -> "{ma_tinh}")')
                    continue
                
                valid_records.append({
                    'ma_tinh': ma_tinh,
                    'ten_tinh': ten_tinh,
                    'hieu_luc': hieu_luc,
                    'row_number': index + 2
                })
                
            except Exception as e:
                errors.append(f'Dòng {index + 2}: {str(e)}')

        if not valid_records:
            return JsonResponse({
                'status': 'error',
                'message': 'Không có dữ liệu hợp lệ để import',
                'errors': errors[:10]
            }, status=400)

        # BƯỚC 2: Lấy danh sách mã tỉnh đã tồn tại
        existing_ma_tinh = set(
            Tinh.objects.filter(
                ma_tinh__in=[record['ma_tinh'] for record in valid_records]
            ).values_list('ma_tinh', flat=True)
        )

        # BƯỚC 3: Phân loại records thành create và update
        records_to_create = []
        records_to_update = []
        
        for record in valid_records:
            if record['ma_tinh'] in existing_ma_tinh:
                records_to_update.append(record)
            else:
                records_to_create.append(record)

        success_count = 0
        
        # BƯỚC 4: Bulk create cho records mới
        if records_to_create:
            try:
                new_objects = [
                    Tinh(
                        ma_tinh=record['ma_tinh'],
                        ten_tinh=record['ten_tinh'],
                        hieu_luc=record['hieu_luc']
                    )
                    for record in records_to_create
                ]
                
                # Sử dụng bulk_create với batch_size để tối ưu memory
                batch_size = 1000
                for i in range(0, len(new_objects), batch_size):
                    batch = new_objects[i:i + batch_size]
                    Tinh.objects.bulk_create(batch, ignore_conflicts=True)
                
                success_count += len(records_to_create)
                logger.info(f"Bulk created {len(records_to_create)} new provinces")
                
            except Exception as e:
                logger.error(f"Bulk create error: {str(e)}")
                errors.append(f"Lỗi khi tạo mới hàng loạt: {str(e)}")

        # BƯỚC 5: Bulk update cho records đã tồn tại
        if records_to_update:
            try:
                # Lấy tất cả objects cần update
                existing_objects = {
                    obj.ma_tinh: obj 
                    for obj in Tinh.objects.filter(
                        ma_tinh__in=[record['ma_tinh'] for record in records_to_update]
                    )
                }
                
                # Cập nhật thông tin
                objects_to_update = []
                for record in records_to_update:
                    if record['ma_tinh'] in existing_objects:
                        obj = existing_objects[record['ma_tinh']]
                        obj.ten_tinh = record['ten_tinh']
                        obj.hieu_luc = record['hieu_luc']
                        objects_to_update.append(obj)
                
                # Bulk update với batch_size
                if objects_to_update:
                    batch_size = 1000
                    for i in range(0, len(objects_to_update), batch_size):
                        batch = objects_to_update[i:i + batch_size]
                        Tinh.objects.bulk_update(
                            batch, 
                            ['ten_tinh', 'hieu_luc'],
                            batch_size=len(batch)
                        )
                    
                    success_count += len(objects_to_update)
                    logger.info(f"Bulk updated {len(objects_to_update)} existing provinces")
                
            except Exception as e:
                logger.error(f"Bulk update error: {str(e)}")
                errors.append(f"Lỗi khi cập nhật hàng loạt: {str(e)}")

        error_count = len(errors)
        message = f'Import thành công {success_count} bản ghi'
        if error_count > 0:
            message += f', thất bại {error_count} bản ghi'

        logger.info(f"User {request.user.username} đã import tỉnh: {success_count} thành công, {error_count} lỗi")

        return JsonResponse({
            'status': 'success',
            'message': message,
            'success_count': success_count,
            'error_count': error_count,
            'created_count': len(records_to_create),
            'updated_count': len(records_to_update),
            'errors': errors[:10],
            'columns_used': {
                'ma_tinh': ma_column,
                'ten_tinh': ten_column,
                'hieu_luc': hieu_luc_column
            }
        })

    except Exception as e:
        logger.error(f"Lỗi khi import Excel: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

# Views cho QuanHuyen và XaPhuong tương tự...
@login_required
def quanhuyen_list(request):
    """Danh sách Quận huyện"""
    return base_list_view(
        request=request,
        model=QuanHuyen,
        form_class=QuanHuyenForm,
        template_name='danhmuckhac/quanhuyen_list.html',
        title=_('Danh mục quận huyện'),
        ordering_fields=['ma_tinh__ma_tinh', 'ma_quan_huyen'],
        searchable_fields=['ma_quan_huyen', 'ten_quan_huyen', 'ma_tinh__ma_tinh', 'ma_tinh__ten_tinh'],
        permission_required='danhmuckhac.view_quanhuyen'
    )

@login_required
def xaphuong_list(request):
    """Danh sách Xã phường"""
    return base_list_view(
        request=request,
        model=XaPhuong,
        form_class=XaPhuongForm,
        template_name='danhmuckhac/xaphuong_list.html',
        title=_('Danh mục xã phường'),
        ordering_fields=['ma_tinh__ma_tinh', 'ma_quan_huyen__ma_quan_huyen', 'ma_xa_phuong'],
        searchable_fields=['ma_xa_phuong', 'ten_xa_phuong', 'ma_quan_huyen__ma_quan_huyen', 'ma_quan_huyen__ten_quan_huyen', 'ma_tinh__ma_tinh', 'ma_tinh__ten_tinh'],
        permission_required='danhmuckhac.view_xaphuong'
    )

# Index view
@login_required
def index(request):
    """Trang chủ danh mục khác"""
    context = {
        'title': 'Danh mục khác',
        'tinh_count': Tinh.objects.count(),
        'quanhuyen_count': QuanHuyen.objects.count(),
        'xaphuong_count': XaPhuong.objects.count(),
        'tinh_active_count': Tinh.objects.filter(hieu_luc=True).count(),
        'quanhuyen_active_count': QuanHuyen.objects.filter(hieu_luc=True).count(),
        'xaphuong_active_count': XaPhuong.objects.filter(hieu_luc=True).count(),
        'inactive_count': (
            Tinh.objects.filter(hieu_luc=False).count() +
            QuanHuyen.objects.filter(hieu_luc=False).count() +
            XaPhuong.objects.filter(hieu_luc=False).count()
        ),
    }
    return render(request, 'danhmuckhac/index.html', context)

@login_required
def quanhuyen_list_api(request):
    """API để lấy danh sách quận huyện cho Tabulator"""
    queryset = QuanHuyen.objects.select_related('ma_tinh').all().order_by('ma_quan_huyen')
    data = []
    for qh in queryset:
        data.append({
            'id': qh.id,
            'ma_tinh': qh.ma_tinh.ma_tinh if qh.ma_tinh else '',
            'ten_tinh': qh.ma_tinh.ten_tinh if qh.ma_tinh else '',
            'ma_tinh_id': qh.ma_tinh.id if qh.ma_tinh else '',
            'ma_quan_huyen': qh.ma_quan_huyen,
            'ten_quan_huyen': qh.ten_quan_huyen,
            'hieu_luc': qh.hieu_luc,
        })
    return JsonResponse({'data': data})

@login_required
def xaphuong_list_api(request):
    """API để lấy danh sách xã phường cho Tabulator"""
    queryset = XaPhuong.objects.select_related('ma_tinh', 'ma_quan_huyen').all().order_by('ma_xa_phuong')
    data = []
    for xp in queryset:
        data.append({
            'id': xp.id,
            'ma_tinh': xp.ma_tinh.ma_tinh if xp.ma_tinh else '',
            'ten_tinh': xp.ma_tinh.ten_tinh if xp.ma_tinh else '',
            'ma_tinh_id': xp.ma_tinh.id if xp.ma_tinh else '',
            'ma_quan_huyen': xp.ma_quan_huyen.ma_quan_huyen if xp.ma_quan_huyen else '',
            'ten_quan_huyen': xp.ma_quan_huyen.ten_quan_huyen if xp.ma_quan_huyen else '',
            'ma_quan_huyen_id': xp.ma_quan_huyen.id if xp.ma_quan_huyen else '',
            'ma_xa_phuong': xp.ma_xa_phuong,
            'ten_xa_phuong': xp.ten_xa_phuong,
            'hieu_luc': xp.hieu_luc,
        })
    return JsonResponse({'data': data})
# CRUD operations for QuanHuyen
@login_required
@require_POST
def quanhuyen_create(request):
    """Tạo mới quận huyện"""
    try:
        form = QuanHuyenForm(request.POST)
        if form.is_valid():
            qh = form.save()
            logger.info(f"User {request.user.username} đã tạo quận huyện mới: {qh.ten_quan_huyen} (ID: {qh.id})")
            return JsonResponse({
                'success': True,
                'message': f'Đã thêm quận/huyện "{qh.ten_quan_huyen}" thành công'
            })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            }, status=400)
    except Exception as e:
        logger.error(f"Lỗi khi tạo quận huyện: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
@login_required
@require_POST
def quanhuyen_edit(request, pk):
    """Chỉnh sửa quận huyện"""
    try:
        qh = get_object_or_404(QuanHuyen, pk=pk)
        form = QuanHuyenForm(request.POST, instance=qh)
        if form.is_valid():
            old_name = qh.ten_quan_huyen
            qh = form.save()
            logger.info(f"User {request.user.username} đã cập nhật quận huyện: {old_name} -> {qh.ten_quan_huyen} (ID: {qh.id})")
            return JsonResponse({
                'status': 'success',
                'message': f'Đã cập nhật quận/huyện "{qh.ten_quan_huyen}" thành công'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'errors': form.errors
            }, status=400)
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật quận huyện ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def quanhuyen_delete(request, pk):
    """Xóa quận huyện"""
    try:
        qh = get_object_or_404(QuanHuyen, pk=pk)
        ten_qh = qh.ten_quan_huyen
        
        # Kiểm tra xem quận huyện có đang được sử dụng không
        if hasattr(qh, 'xaphuong_set') and qh.xaphuong_set.exists():
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể xóa quận/huyện "{ten_qh}" vì đang có xã/phường thuộc quận/huyện này'
            }, status=400)
        
        qh.delete()
        logger.info(f"User {request.user.username} đã xóa quận huyện: {ten_qh} (ID: {pk})")
        
        return JsonResponse({
            'status': 'success',
            'message': f'Đã xóa quận/huyện "{ten_qh}" thành công'
        })
        
    except QuanHuyen.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng xóa quận huyện không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy quận/huyện'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi xóa quận huyện ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def quanhuyen_toggle_hieu_luc(request, pk):
    """Toggle hiệu lực của quận huyện"""
    try:
        qh = QuanHuyen.objects.get(pk=pk)
        hieu_luc = request.POST.get('hieu_luc') == 'true'
        
        # Lưu trạng thái cũ để log
        old_status = qh.hieu_luc
        qh.hieu_luc = hieu_luc
        qh.save()
        
        # Log thay đổi
        action = 'kích hoạt' if hieu_luc else 'hủy kích hoạt'
        logger.info(f"User {request.user.username} đã {action} hiệu lực cho quận huyện {qh.ten_quan_huyen} (ID: {qh.id})")
        
        message = f"Đã {action} hiệu lực cho quận/huyện '{qh.ten_quan_huyen}'"
        
        return JsonResponse({
            'status': 'success',
            'message': message,
            'hieu_luc': hieu_luc,
            'data': {
                'id': qh.id,
                'ma_quan_huyen': qh.ma_quan_huyen,
                'ten_quan_huyen': qh.ten_quan_huyen,
                'hieu_luc': qh.hieu_luc
            }
        })
        
    except QuanHuyen.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng toggle hiệu lực cho quận huyện không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy quận/huyện'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi toggle hiệu lực quận huyện ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
@login_required
def quanhuyen_export(request):
    """Xuất danh sách quận huyện ra Excel"""
    try:
        # Lấy dữ liệu
        queryset = QuanHuyen.objects.select_related('ma_tinh').all().order_by('ma_quan_huyen')
        
        # Tạo workbook và worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Danh sách Quận Huyện"
        
        # Định nghĩa style
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Header
        headers = ['STT', 'Mã tỉnh', 'Mã Q/H', 'Tên quận/huyện', 'Hiệu lực', 'Ngày tạo']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Dữ liệu
        for row, qh in enumerate(queryset, 2):
            ws.cell(row=row, column=1, value=row-1).border = border  # STT
            ws.cell(row=row, column=2, value=qh.ma_tinh.ma_tinh if qh.ma_tinh else '').border = border
            ws.cell(row=row, column=3, value=qh.ma_quan_huyen).border = border
            ws.cell(row=row, column=4, value=qh.ten_quan_huyen).border = border
            ws.cell(row=row, column=5, value="Có" if qh.hieu_luc else "Không").border = border
            ws.cell(row=row, column=6, value=qh.created_at.strftime("%d/%m/%Y %H:%M") if hasattr(qh, 'created_at') else "").border = border
        
        # Điều chỉnh độ rộng cột
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 10
        ws.column_dimensions['C'].width = 25
        ws.column_dimensions['D'].width = 10
        ws.column_dimensions['E'].width = 25
        ws.column_dimensions['F'].width = 12
        
        # Tạo response
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        filename = f"danh_sach_quan_huyen_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        logger.info(f"User {request.user.username} đã xuất danh sách quận huyện ra Excel")
        
        return response
        
    except Exception as e:
        logger.error(f"Lỗi khi xuất Excel: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Có lỗi xảy ra khi xuất Excel: {str(e)}'
        }, status=500)

# CRUD operations for XaPhuong
@login_required
@require_POST
def xaphuong_create(request):
    """Tạo mới xã phường"""
    try:
        form = XaPhuongForm(request.POST)
        if form.is_valid():
            xp = form.save()
            logger.info(f"User {request.user.username} đã tạo xã phường mới: {xp.ten_xa_phuong} (ID: {xp.id})")
            return JsonResponse({
                'success': True,
                'message': f'Đã thêm xã/phường "{xp.ten_xa_phuong}" thành công'
            })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            }, status=400)
    except Exception as e:
        logger.error(f"Lỗi khi tạo xã phường: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def xaphuong_edit(request, pk):
    """Chỉnh sửa xã phường"""
    try:
        xp = get_object_or_404(XaPhuong, pk=pk)
        form = XaPhuongForm(request.POST, instance=xp)
        if form.is_valid():
            old_name = xp.ten_xa_phuong
            xp = form.save()
            logger.info(f"User {request.user.username} đã cập nhật xã phường: {old_name} -> {xp.ten_xa_phuong} (ID: {xp.id})")
            return JsonResponse({
                'status': 'success',
                'message': f'Đã cập nhật xã/phường "{xp.ten_xa_phuong}" thành công'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'errors': form.errors
            }, status=400)
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật xã phường ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    

@login_required
@require_POST
def xaphuong_delete(request, pk):
    """Xóa xã phường"""
    try:
        xp = get_object_or_404(XaPhuong, pk=pk)
        ten_xp = xp.ten_xa_phuong
        
        xp.delete()
        logger.info(f"User {request.user.username} đã xóa xã phường: {ten_xp} (ID: {pk})")
        
        return JsonResponse({
            'status': 'success',
            'message': f'Đã xóa xã/phường "{ten_xp}" thành công'
        })
        
    except XaPhuong.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng xóa xã phường không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy xã/phường'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi xóa xã phường ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def xaphuong_toggle_hieu_luc(request, pk):
    """Toggle hiệu lực của xã phường"""
    try:
        xp = XaPhuong.objects.get(pk=pk)
        hieu_luc = request.POST.get('hieu_luc') == 'true'
        
        # Lưu trạng thái cũ để log
        old_status = xp.hieu_luc
        xp.hieu_luc = hieu_luc
        xp.save()
        
        # Log thay đổi
        action = 'kích hoạt' if hieu_luc else 'hủy kích hoạt'
        logger.info(f"User {request.user.username} đã {action} hiệu lực cho xã phường {xp.ten_xa_phuong} (ID: {xp.id})")
        
        message = f"Đã {action} hiệu lực cho xã/phường '{xp.ten_xa_phuong}'"
        
        return JsonResponse({
            'status': 'success',
            'message': message,
            'hieu_luc': hieu_luc,
            'data': {
                'id': xp.id,
                'ma_xa_phuong': xp.ma_xa_phuong,
                'ten_xa_phuong': xp.ten_xa_phuong,
                'hieu_luc': xp.hieu_luc
            }
        })
        
    except XaPhuong.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng toggle hiệu lực cho xã phường không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy xã/phường'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi toggle hiệu lực xã phường ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
# Export functions
@login_required
def xaphuong_export(request):
    """Xuất danh sách xã/phường ra Excel"""
    try:
        queryset = XaPhuong.objects.select_related('ma_tinh', 'ma_quan_huyen').all().order_by('ma_xa_phuong')
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Danh sách Xã Phường"
        
        # Định nghĩa style
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Header
        headers = ['STT', 'Mã X/P', 'Tên Xã/Phường', 'Mã Q/H', 'Tên Q/H', 'Mã tỉnh', 'Tên tỉnh', 'Hiệu lực', 'Ngày tạo']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Dữ liệu
        for row, xp in enumerate(queryset, 2):
            ws.cell(row=row, column=1, value=row-1).border = border
            ws.cell(row=row, column=2, value=xp.ma_xa_phuong).border = border
            ws.cell(row=row, column=3, value=xp.ten_xa_phuong).border = border
            ws.cell(row=row, column=4, value=xp.ma_quan_huyen.ma_quan_huyen if xp.ma_quan_huyen else "").border = border
            ws.cell(row=row, column=5, value=xp.ma_quan_huyen.ten_quan_huyen if xp.ma_quan_huyen else "").border = border
            ws.cell(row=row, column=6, value=xp.ma_tinh.ma_tinh if xp.ma_tinh else "").border = border
            ws.cell(row=row, column=7, value=xp.ma_tinh.ten_tinh if xp.ma_tinh else "").border = border
            ws.cell(row=row, column=8, value="Có" if xp.hieu_luc else "Không").border = border
            ws.cell(row=row, column=9, value=xp.created_at.strftime("%d/%m/%Y %H:%M") if hasattr(xp, 'created_at') else "").border = border
        
        # Điều chỉnh độ rộng cột
        column_widths = [8, 12, 25, 12, 20, 12, 20, 12, 20]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width
        
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        filename = f"danh_sach_xa_phuong_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        logger.info(f"User {request.user.username} đã xuất danh sách xã/phường ra Excel")
        return response
        
    except Exception as e:
        logger.error(f"Lỗi khi xuất Excel xã/phường: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Có lỗi xảy ra khi xuất Excel: {str(e)}'
        }, status=500)

# Import functions
@login_required
@require_POST
def quanhuyen_import(request):
    """Import danh sách quận huyện từ Excel với bulk operations"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'status': 'error',
                'message': 'Vui lòng chọn file Excel'
            }, status=400)

        file = request.FILES['file']
        
        # Kiểm tra định dạng file
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({
                'status': 'error',
                'message': 'File phải có định dạng .xlsx, .xls hoặc .csv'
            }, status=400)

        # Đọc file Excel với dtype=str để giữ nguyên format
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file, dtype=str)
            else:
                df = pd.read_excel(file, dtype=str)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể đọc file: {str(e)}'
            }, status=400)

        # Tìm cột phù hợp
        available_columns = df.columns.tolist()
        
        ma_tinh_columns = ['Mã tỉnh', 'ma_tinh', 'Ma_tinh', 'MA_TINH', 'Mã Tỉnh', 'Ma Tinh']
        ma_qh_columns = ['Mã', 'ma', 'Ma', 'MA', 'mã', 'ma_quan_huyen', 'Ma_quan_huyen', 'MA_QUAN_HUYEN', 'Mã quận huyện', 'Ma quan huyen']
        ten_qh_columns = ['Tên', 'ten', 'Ten', 'TEN', 'tên', 'ten_quan_huyen', 'Ten_quan_huyen', 'TEN_QUAN_HUYEN', 'Tên quận huyện', 'Ten quan huyen']
        hieu_luc_columns = ['Hiệu lực', 'hieu_luc', 'Hieu_luc', 'HIEU_LUC', 'hiệu lực', 'Hieu luc', 'Status', 'Active']
        
        ma_tinh_column = next((col for col in ma_tinh_columns if col in available_columns), None)
        ma_qh_column = next((col for col in ma_qh_columns if col in available_columns), None)
        ten_qh_column = next((col for col in ten_qh_columns if col in available_columns), None)
        hieu_luc_column = next((col for col in hieu_luc_columns if col in available_columns), None)
        
        if not ma_tinh_column or not ma_qh_column or not ten_qh_column:
            missing = []
            if not ma_tinh_column:
                missing.append(f'Cột mã tỉnh (một trong: {", ".join(ma_tinh_columns[:5])}...)')
            if not ma_qh_column:
                missing.append(f'Cột mã quận huyện (một trong: {", ".join(ma_qh_columns[:5])}...)')
            if not ten_qh_column:
                missing.append(f'Cột tên quận huyện (một trong: {", ".join(ten_qh_columns[:5])}...)')
            
            return JsonResponse({
                'status': 'error',
                'message': f'File thiếu các cột bắt buộc: {"; ".join(missing)}'
            }, status=400)

        # BƯỚC 1: Validate và chuẩn hóa dữ liệu
        valid_records = []
        errors = []
        
        # Lấy danh sách tỉnh để validate - QUAN TRỌNG: lấy cả ma_tinh dạng string
        tinh_dict = {}
        for tinh in Tinh.objects.all():
            # Lưu cả dạng có số 0 đầu và không có số 0 đầu
            tinh_dict[tinh.ma_tinh] = tinh
            tinh_dict[str(int(tinh.ma_tinh))] = tinh  # Ví dụ: "01" -> "1"
        
        logger.info(f"Available tinh codes: {list(tinh_dict.keys())}")
        
        for index, row in df.iterrows():
            try:
                # Xử lý mã tỉnh - CẢI TIẾN XỬ LÝ
                ma_tinh_raw = row[ma_tinh_column]
                if pd.isna(ma_tinh_raw) or str(ma_tinh_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã tỉnh không được để trống')
                    continue
                
                ma_tinh_str = str(ma_tinh_raw).strip()
                
                # Xử lý các trường hợp khác nhau của mã tỉnh
                if ma_tinh_str.replace('.', '').replace(',', '').isdigit():
                    # Nếu là số, chuyển về dạng integer rồi format lại
                    ma_tinh_int = int(float(ma_tinh_str))
                    ma_tinh = str(ma_tinh_int).zfill(2)  # "1" -> "01", "10" -> "10"
                else:
                    # Nếu không phải số, giữ nguyên
                    ma_tinh = ma_tinh_str
                
                logger.info(f"Row {index + 2}: ma_tinh_raw='{ma_tinh_raw}' -> ma_tinh='{ma_tinh}'")
                
                # Xử lý mã quận huyện
                ma_qh_raw = row[ma_qh_column]
                if pd.isna(ma_qh_raw) or str(ma_qh_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã quận huyện không được để trống')
                    continue
                
                ma_quan_huyen_str = str(ma_qh_raw).strip()
                if ma_quan_huyen_str.replace('.', '').replace(',', '').isdigit():
                    ma_quan_huyen_int = int(float(ma_quan_huyen_str))
                    ma_quan_huyen = str(ma_quan_huyen_int).zfill(3)
                else:
                    ma_quan_huyen = ma_quan_huyen_str
                
                # Xử lý tên quận huyện
                ten_quan_huyen = str(row[ten_qh_column]).strip() if pd.notna(row[ten_qh_column]) else ''
                if not ten_quan_huyen or ten_quan_huyen.lower() in ['nan', 'null']:
                    errors.append(f'Dòng {index + 2}: Tên quận huyện không được để trống')
                    continue
                
                # Xử lý hiệu lực
                hieu_luc = True
                if hieu_luc_column and pd.notna(row[hieu_luc_column]):
                    hieu_luc_value = str(row[hieu_luc_column]).strip().lower()
                    hieu_luc = hieu_luc_value in ['true', '1', 'có', 'yes', 'y', 'active', 'hiệu lực', 'hieu luc']
                
                # Validate mã tỉnh tồn tại - KIỂM TRA CẢ HAI DẠNG
                tinh_obj = None
                if ma_tinh in tinh_dict:
                    tinh_obj = tinh_dict[ma_tinh]
                elif str(int(ma_tinh)) in tinh_dict:
                    tinh_obj = tinh_dict[str(int(ma_tinh))]
                
                if not tinh_obj:
                    errors.append(f'Dòng {index + 2}: Không tìm thấy tỉnh có mã "{ma_tinh}" (raw: "{ma_tinh_raw}")')
                    logger.error(f'Không tìm thấy tỉnh có mã "{ma_tinh}", available: {list(tinh_dict.keys())}')
                    continue
                
                # Validate định dạng mã quận huyện
                if not ma_quan_huyen.isdigit() or len(ma_quan_huyen) != 3:
                    errors.append(f'Dòng {index + 2}: Mã quận huyện phải là 3 chữ số (hiện tại: "{ma_qh_raw}" -> "{ma_quan_huyen}")')
                    continue
                
                valid_records.append({
                    'ma_tinh': tinh_obj.ma_tinh,  # Sử dụng mã tỉnh từ database
                    'ma_quan_huyen': ma_quan_huyen,
                    'ten_quan_huyen': ten_quan_huyen,
                    'hieu_luc': hieu_luc,
                    'tinh_obj': tinh_obj,
                    'row_number': index + 2
                })
                
            except Exception as e:
                errors.append(f'Dòng {index + 2}: {str(e)}')
                logger.error(f'Error processing row {index + 2}: {str(e)}')

        if not valid_records:
            return JsonResponse({
                'status': 'error',
                'message': 'Không có dữ liệu hợp lệ để import',
                'errors': errors[:10]
            }, status=400)

        # BƯỚC 2: Lấy danh sách mã quận huyện đã tồn tại
        existing_ma_qh = set(
            QuanHuyen.objects.filter(
                ma_quan_huyen__in=[record['ma_quan_huyen'] for record in valid_records]
            ).values_list('ma_quan_huyen', flat=True)
        )

        # BƯỚC 3: Phân loại records thành create và update
        records_to_create = []
        records_to_update = []
        
        for record in valid_records:
            if record['ma_quan_huyen'] in existing_ma_qh:
                records_to_update.append(record)
            else:
                records_to_create.append(record)

        success_count = 0
        
        # BƯỚC 4: Bulk create cho records mới
        if records_to_create:
            try:
                new_objects = [
                    QuanHuyen(
                        ma_tinh=record['tinh_obj'],
                        ma_quan_huyen=record['ma_quan_huyen'],
                        ten_quan_huyen=record['ten_quan_huyen'],
                        hieu_luc=record['hieu_luc']
                    )
                    for record in records_to_create
                ]
                
                # Sử dụng bulk_create với batch_size để tối ưu memory
                batch_size = 1000
                for i in range(0, len(new_objects), batch_size):
                    batch = new_objects[i:i + batch_size]
                    QuanHuyen.objects.bulk_create(batch, ignore_conflicts=True)
                
                success_count += len(records_to_create)
                logger.info(f"Bulk created {len(records_to_create)} new districts")
                
            except Exception as e:
                logger.error(f"Bulk create error: {str(e)}")
                errors.append(f"Lỗi khi tạo mới hàng loạt: {str(e)}")

        # BƯỚC 5: Bulk update cho records đã tồn tại
        if records_to_update:
            try:
                # Lấy tất cả objects cần update
                existing_objects = {
                    obj.ma_quan_huyen: obj 
                    for obj in QuanHuyen.objects.filter(
                        ma_quan_huyen__in=[record['ma_quan_huyen'] for record in records_to_update]
                    )
                }
                
                # Cập nhật thông tin
                objects_to_update = []
                for record in records_to_update:
                    if record['ma_quan_huyen'] in existing_objects:
                        obj = existing_objects[record['ma_quan_huyen']]
                        obj.ma_tinh = record['tinh_obj']
                        obj.ten_quan_huyen = record['ten_quan_huyen']
                        obj.hieu_luc = record['hieu_luc']
                        objects_to_update.append(obj)
                
                # Bulk update với batch_size
                if objects_to_update:
                    batch_size = 1000
                    for i in range(0, len(objects_to_update), batch_size):
                        batch = objects_to_update[i:i + batch_size]
                        QuanHuyen.objects.bulk_update(
                            batch, 
                            ['ma_tinh', 'ten_quan_huyen', 'hieu_luc'],
                            batch_size=len(batch)
                        )
                    
                    success_count += len(objects_to_update)
                    logger.info(f"Bulk updated {len(objects_to_update)} existing districts")
                
            except Exception as e:
                logger.error(f"Bulk update error: {str(e)}")
                errors.append(f"Lỗi khi cập nhật hàng loạt: {str(e)}")

        error_count = len(errors)
        message = f'Import thành công {success_count} bản ghi'
        if error_count > 0:
            message += f', thất bại {error_count} bản ghi'

        logger.info(f"User {request.user.username} đã import quận huyện: {success_count} thành công, {error_count} lỗi")

        return JsonResponse({
            'status': 'success',
            'message': message,
            'success_count': success_count,
            'error_count': error_count,
            'created_count': len(records_to_create),
            'updated_count': len(records_to_update),
            'errors': errors[:10],
            'columns_used': {
                'ma_tinh': ma_tinh_column,
                                'ma_quan_huyen': ma_qh_column,
                'ten_quan_huyen': ten_qh_column,
                'hieu_luc': hieu_luc_column
            }
        })

    except Exception as e:
        logger.error(f"Lỗi khi import Excel: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def xaphuong_import(request):
    """Import danh sách xã phường từ Excel với bulk operations"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'status': 'error',
                'message': 'Vui lòng chọn file Excel'
            }, status=400)

        file = request.FILES['file']
        
        # Kiểm tra định dạng file
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({
                'status': 'error',
                'message': 'File phải có định dạng .xlsx, .xls hoặc .csv'
            }, status=400)

        # Đọc file Excel với dtype=str để giữ nguyên format
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file, dtype=str)
            else:
                df = pd.read_excel(file, dtype=str)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể đọc file: {str(e)}'
            }, status=400)

        # Tìm cột phù hợp
        available_columns = df.columns.tolist()
        
        ma_tinh_columns = ['Mã tỉnh', 'ma_tinh', 'Ma_tinh', 'MA_TINH', 'Mã Tỉnh', 'Ma Tinh']
        ma_qh_columns = ['Mã quận huyện', 'ma_quan_huyen', 'Ma_quan_huyen', 'MA_QUAN_HUYEN', 'Mã Q/H', 'Ma Q/H', 'Mã QH', 'Ma QH']
        ma_xp_columns = ['Mã', 'ma', 'Ma', 'MA', 'mã', 'ma_phuong_xa', 'Ma_phuong_xa', 'MA_PHUONG_XA', 'Mã xã phường', 'Ma xa phuong', 'Mã X/P', 'Ma X/P']
        ten_xp_columns = ['Tên', 'ten', 'Ten', 'TEN', 'tên', 'ten_phuong_xa', 'Ten_phuong_xa', 'TEN_PHUONG_XA', 'Tên xã phường', 'Ten xa phuong', 'Tên X/P', 'Ten X/P']
        hieu_luc_columns = ['Hiệu lực', 'hieu_luc', 'Hieu_luc', 'HIEU_LUC', 'hiệu lực', 'Hieu luc', 'Status', 'Active']
        
        ma_tinh_column = next((col for col in ma_tinh_columns if col in available_columns), None)
        ma_qh_column = next((col for col in ma_qh_columns if col in available_columns), None)
        ma_xp_column = next((col for col in ma_xp_columns if col in available_columns), None)
        ten_xp_column = next((col for col in ten_xp_columns if col in available_columns), None)
        hieu_luc_column = next((col for col in hieu_luc_columns if col in available_columns), None)
        
        if not ma_tinh_column or not ma_qh_column or not ma_xp_column or not ten_xp_column:
            missing = []
            if not ma_tinh_column:
                missing.append(f'Cột mã tỉnh (một trong: {", ".join(ma_tinh_columns[:5])}...)')
            if not ma_qh_column:
                missing.append(f'Cột mã quận huyện (một trong: {", ".join(ma_qh_columns[:5])}...)')
            if not ma_xp_column:
                missing.append(f'Cột mã xã phường (một trong: {", ".join(ma_xp_columns[:5])}...)')
            if not ten_xp_column:
                missing.append(f'Cột tên xã phường (một trong: {", ".join(ten_xp_columns[:5])}...)')
            
            return JsonResponse({
                'status': 'error',
                'message': f'File thiếu các cột bắt buộc: {"; ".join(missing)}'
            }, status=400)

        # BƯỚC 1: Validate và chuẩn hóa dữ liệu
        valid_records = []
        errors = []
        
        # Lấy danh sách tỉnh và quận huyện để validate - XỬ LÝ CẢI TIẾN
        tinh_dict = {}
        for tinh in Tinh.objects.all():
            # Lưu cả dạng có số 0 đầu và không có số 0 đầu
            tinh_dict[tinh.ma_tinh] = tinh
            tinh_dict[str(int(tinh.ma_tinh))] = tinh  # Ví dụ: "01" -> "1"
        
        quanhuyen_dict = {}
        for qh in QuanHuyen.objects.select_related('ma_tinh').all():
            # Tạo key với cả hai dạng mã tỉnh
            key1 = f"{qh.ma_tinh.ma_tinh}_{qh.ma_quan_huyen}"
            key2 = f"{str(int(qh.ma_tinh.ma_tinh))}_{qh.ma_quan_huyen}"
            quanhuyen_dict[key1] = qh
            quanhuyen_dict[key2] = qh
        
        logger.info(f"Available tinh codes: {list(set([k for k in tinh_dict.keys()]))}")
        logger.info(f"Sample quanhuyen keys: {list(quanhuyen_dict.keys())[:10]}")
        
        for index, row in df.iterrows():
            try:
                # Xử lý mã tỉnh - CẢI TIẾN XỬ LÝ
                ma_tinh_raw = row[ma_tinh_column]
                if pd.isna(ma_tinh_raw) or str(ma_tinh_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã tỉnh không được để trống')
                    continue
                
                ma_tinh_str = str(ma_tinh_raw).strip()
                
                # Xử lý các trường hợp khác nhau của mã tỉnh
                if ma_tinh_str.replace('.', '').replace(',', '').isdigit():
                    # Nếu là số, chuyển về dạng integer rồi format lại
                    ma_tinh_int = int(float(ma_tinh_str))
                    ma_tinh = str(ma_tinh_int).zfill(2)  # "1" -> "01", "10" -> "10"
                else:
                    # Nếu không phải số, giữ nguyên
                    ma_tinh = ma_tinh_str
                
                # Xử lý mã quận huyện
                ma_qh_raw = row[ma_qh_column]
                if pd.isna(ma_qh_raw) or str(ma_qh_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã quận huyện không được để trống')
                    continue
                
                ma_quan_huyen_str = str(ma_qh_raw).strip()
                if ma_quan_huyen_str.replace('.', '').replace(',', '').isdigit():
                    ma_quan_huyen_int = int(float(ma_quan_huyen_str))
                    ma_quan_huyen = str(ma_quan_huyen_int).zfill(3)
                else:
                    ma_quan_huyen = ma_quan_huyen_str
                
                # Xử lý mã xã phường
                ma_xp_raw = row[ma_xp_column]
                if pd.isna(ma_xp_raw) or str(ma_xp_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã xã phường không được để trống')
                    continue
                
                ma_xa_phuong_str = str(ma_xp_raw).strip()
                if ma_xa_phuong_str.replace('.', '').replace(',', '').isdigit():
                    ma_xa_phuong_int = int(float(ma_xa_phuong_str))
                    ma_xa_phuong = str(ma_xa_phuong_int).zfill(5)
                else:
                    ma_xa_phuong = ma_xa_phuong_str
                
                # Xử lý tên xã phường
                ten_xa_phuong = str(row[ten_xp_column]).strip() if pd.notna(row[ten_xp_column]) else ''
                if not ten_xa_phuong or ten_xa_phuong.lower() in ['nan', 'null']:
                    errors.append(f'Dòng {index + 2}: Tên xã phường không được để trống')
                    continue
                
                # Xử lý hiệu lực
                hieu_luc = True
                if hieu_luc_column and pd.notna(row[hieu_luc_column]):
                    hieu_luc_value = str(row[hieu_luc_column]).strip().lower()
                    hieu_luc = hieu_luc_value in ['true', '1', 'có', 'yes', 'y', 'active', 'hiệu lực', 'hieu luc']
                
                # Validate mã tỉnh tồn tại - KIỂM TRA CẢ HAI DẠNG
                tinh_obj = None
                if ma_tinh in tinh_dict:
                    tinh_obj = tinh_dict[ma_tinh]
                elif str(int(ma_tinh)) in tinh_dict:
                    tinh_obj = tinh_dict[str(int(ma_tinh))]
                
                if not tinh_obj:
                    errors.append(f'Dòng {index + 2}: Không tìm thấy tỉnh có mã "{ma_tinh}" (raw: "{ma_tinh_raw}")')
                    continue
                
                # Validate mã quận huyện tồn tại và thuộc tỉnh - KIỂM TRA CẢ HAI DẠNG
                qh_key1 = f"{ma_tinh}_{ma_quan_huyen}"
                qh_key2 = f"{str(int(ma_tinh))}_{ma_quan_huyen}"
                
                qh_obj = None
                if qh_key1 in quanhuyen_dict:
                    qh_obj = quanhuyen_dict[qh_key1]
                elif qh_key2 in quanhuyen_dict:
                    qh_obj = quanhuyen_dict[qh_key2]
                
                if not qh_obj:
                    errors.append(f'Dòng {index + 2}: Không tìm thấy quận/huyện có mã "{ma_quan_huyen}" thuộc tỉnh "{ma_tinh}" (keys tried: {qh_key1}, {qh_key2})')
                    continue
                
                # Validate định dạng mã xã phường
                if not ma_xa_phuong.isdigit() or len(ma_xa_phuong) != 5:
                    errors.append(f'Dòng {index + 2}: Mã xã phường phải là 5 chữ số (hiện tại: "{ma_xp_raw}" -> "{ma_xa_phuong}")')
                    continue
                
                valid_records.append({
                    'ma_tinh': tinh_obj.ma_tinh,  # Sử dụng mã tỉnh từ database
                    'ma_quan_huyen': qh_obj.ma_quan_huyen,  # Sử dụng mã quận huyện từ database
                    'ma_xa_phuong': ma_xa_phuong,
                    'ten_xa_phuong': ten_xa_phuong,
                    'hieu_luc': hieu_luc,
                    'tinh_obj': tinh_obj,
                    'qh_obj': qh_obj,
                    'row_number': index + 2
                })
                
            except Exception as e:
                errors.append(f'Dòng {index + 2}: {str(e)}')
                logger.error(f'Error processing row {index + 2}: {str(e)}')

        if not valid_records:
            return JsonResponse({
                'status': 'error',
                'message': 'Không có dữ liệu hợp lệ để import',
                'errors': errors[:10]
            }, status=400)

        # BƯỚC 2: Lấy danh sách mã xã phường đã tồn tại
        existing_ma_xp = set(
            XaPhuong.objects.filter(
                ma_xa_phuong__in=[record['ma_xa_phuong'] for record in valid_records]
            ).values_list('ma_xa_phuong', flat=True)
        )

        # BƯỚC 3: Phân loại records thành create và update
        records_to_create = []
        records_to_update = []
        
        for record in valid_records:
            if record['ma_xa_phuong'] in existing_ma_xp:
                records_to_update.append(record)
            else:
                records_to_create.append(record)

        success_count = 0
        
        # BƯỚC 4: Bulk create cho records mới
        if records_to_create:
            try:
                new_objects = [
                    XaPhuong(
                        ma_tinh=record['tinh_obj'],
                        ma_quan_huyen=record['qh_obj'],
                        ma_xa_phuong=record['ma_xa_phuong'],
                        ten_xa_phuong=record['ten_xa_phuong'],
                        hieu_luc=record['hieu_luc']
                    )
                    for record in records_to_create
                ]
                
                # Sử dụng bulk_create với batch_size để tối ưu memory
                if new_objects:
                    batch_size = 1000
                    for i in range(0, len(new_objects), batch_size):
                        batch = new_objects[i:i + batch_size]
                        XaPhuong.objects.bulk_create(batch, ignore_conflicts=True)
                    
                    success_count += len(new_objects)
                    logger.info(f"Bulk created {len(new_objects)} new wards")
                
            except Exception as e:
                logger.error(f"Bulk create error: {str(e)}")
                errors.append(f"Lỗi khi tạo mới hàng loạt: {str(e)}")

        # BƯỚC 5: Bulk update cho records đã tồn tại
        if records_to_update:
            try:
                # Lấy tất cả objects cần update
                existing_objects = {
                    obj.ma_xa_phuong: obj 
                    for obj in XaPhuong.objects.filter(
                        ma_xa_phuong__in=[record['ma_xa_phuong'] for record in records_to_update]
                    )
                }
                
                # Cập nhật thông tin
                objects_to_update = []
                for record in records_to_update:
                    if record['ma_xa_phuong'] in existing_objects:
                        obj = existing_objects[record['ma_xa_phuong']]
                        obj.ma_tinh = record['tinh_obj']
                        obj.ma_quan_huyen = record['qh_obj']
                        obj.ten_xa_phuong = record['ten_xa_phuong']
                        obj.hieu_luc = record['hieu_luc']
                        objects_to_update.append(obj)
                
                # Bulk update với batch_size
                if objects_to_update:
                    batch_size = 1000
                    for i in range(0, len(objects_to_update), batch_size):
                        batch = objects_to_update[i:i + batch_size]
                        XaPhuong.objects.bulk_update(
                            batch, 
                            ['ma_tinh', 'ma_quan_huyen', 'ten_xa_phuong', 'hieu_luc'],
                            batch_size=len(batch)
                        )
                    
                    success_count += len(objects_to_update)
                    logger.info(f"Bulk updated {len(objects_to_update)} existing wards")
                
            except Exception as e:
                logger.error(f"Bulk update error: {str(e)}")
                errors.append(f"Lỗi khi cập nhật hàng loạt: {str(e)}")

        error_count = len(errors)
        message = f'Import thành công {success_count} bản ghi'
        if error_count > 0:
            message += f', thất bại {error_count} bản ghi'

        logger.info(f"User {request.user.username} đã import xã phường: {success_count} thành công, {error_count} lỗi")

        return JsonResponse({
            'status': 'success',
            'message': message,
            'success_count': success_count,
            'error_count': error_count,
            'created_count': len(records_to_create),
            'updated_count': len(records_to_update),
            'errors': errors[:10],
            'columns_used': {
                'ma_tinh': ma_tinh_column,
                'ma_quan_huyen': ma_qh_column,
                'ma_xa_phuong': ma_xp_column,
                'ten_xa_phuong': ten_xp_column,
                'hieu_luc': hieu_luc_column
            }
        })

    except Exception as e:
        logger.error(f"Lỗi khi import Excel xã phường: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)


@login_required
@require_GET
def get_quanhuyen_by_tinh(request, ma_tinh):
    """API để lấy danh sách quận huyện theo tỉnh"""
    try:
        quanhuyen_list = QuanHuyen.objects.filter(
            ma_tinh__ma_tinh=ma_tinh,
            hieu_luc=True
        ).order_by('ma_quan_huyen')
        
        data = []
        for qh in quanhuyen_list:
            data.append({
                'value': qh.id,
                'label': f"{qh.ma_quan_huyen} - {qh.ten_quan_huyen}",
                'ma_quan_huyen': qh.ma_quan_huyen,
                'ten_quan_huyen': qh.ten_quan_huyen
            })
        
        return JsonResponse({'success': True, 'data': data})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
def get_xaphuong_by_quanhuyen(request, ma_quan_huyen):
    """API để lấy danh sách xã phường theo quận huyện"""
    try:
        xaphuong_list = XaPhuong.objects.filter(
            ma_quan_huyen__ma_quan_huyen=ma_quan_huyen,
            hieu_luc=True
        ).order_by('ma_xa_phuong')
        
        data = []
        for xp in xaphuong_list:
            data.append({
                'value': xp.id,
                'label': f"{xp.ma_xa_phuong} - {xp.ten_xa_phuong}",
                'ma_xa_phuong': xp.ma_xa_phuong,
                'ten_xa_phuong': xp.ten_xa_phuong
            })
        
        return JsonResponse({'success': True, 'data': data})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_POST
def tinh_bulk_delete(request):
    """Xóa nhiều tỉnh cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        deleted_count = Tinh.objects.filter(id__in=ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Đã xóa {deleted_count} tỉnh/thành phố'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST  
def tinh_bulk_toggle(request):
    """Bật/tắt hiệu lực nhiều tỉnh cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        action = request.POST.get('action')
        
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
            
        if action not in ['activate', 'deactivate']:
            return JsonResponse({
                'success': False,
                'message': 'Hành động không hợp lệ'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        hieu_luc = action == 'activate'
        updated_count = Tinh.objects.filter(id__in=ids).update(hieu_luc=hieu_luc)
        
        action_text = 'kích hoạt' if hieu_luc else 'vô hiệu hóa'
        
        return JsonResponse({
            'success': True,
            'message': f'Đã {action_text} {updated_count} tỉnh/thành phố'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })
    
@login_required
@require_POST
def quanhuyen_bulk_delete(request):
    """Xóa nhiều quận huyện cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        # Kiểm tra xem có quận huyện nào đang được sử dụng không
        qh_with_xp = QuanHuyen.objects.filter(
            id__in=ids,
            xaphuong__isnull=False
        ).distinct().values_list('ten_quan_huyen', flat=True)
        
        if qh_with_xp:
            return JsonResponse({
                'success': False,
                'message': f'Không thể xóa các quận/huyện sau vì đang có xã/phường: {", ".join(qh_with_xp[:5])}'
            })
        
        deleted_count = QuanHuyen.objects.filter(id__in=ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Đã xóa {deleted_count} quận/huyện'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST  
def quanhuyen_bulk_toggle(request):
    """Bật/tắt hiệu lực nhiều quận huyện cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        action = request.POST.get('action')
        
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
            
        if action not in ['activate', 'deactivate']:
            return JsonResponse({
                'success': False,
                'message': 'Hành động không hợp lệ'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        hieu_luc = action == 'activate'
        updated_count = QuanHuyen.objects.filter(id__in=ids).update(hieu_luc=hieu_luc)
        
        action_text = 'kích hoạt' if hieu_luc else 'vô hiệu hóa'
        
        return JsonResponse({
            'success': True,
            'message': f'Đã {action_text} {updated_count} quận/huyện'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST
def xaphuong_bulk_delete(request):
    """Xóa nhiều xã phường cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        deleted_count = XaPhuong.objects.filter(id__in=ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Đã xóa {deleted_count} xã/phường'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST  
def xaphuong_bulk_toggle(request):
    """Bật/tắt hiệu lực nhiều xã phường cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        action = request.POST.get('action')
        
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
            
        if action not in ['activate', 'deactivate']:
            return JsonResponse({
                'success': False,
                'message': 'Hành động không hợp lệ'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        hieu_luc = action == 'activate'
        updated_count = XaPhuong.objects.filter(id__in=ids).update(hieu_luc=hieu_luc)
        
        action_text = 'kích hoạt' if hieu_luc else 'vô hiệu hóa'
        
        return JsonResponse({
            'success': True,
            'message': f'Đã {action_text} {updated_count} xã/phường'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_GET
def noi_dkbd_list(request):
    """Thực hiện việc lấy danh sách nơi đăng kí ban đầu"""
    return base_list_view(
        request=request,
        model=NoiDangKiBanDau,
        form_class=NoiDangKiBanDauForm,
        template_name='danhmuckhac/noi_dkbd_list.html',
        title=_('Danh mục nơi ĐKBĐ'),
        ordering_fields=['ma'],
        searchable_fields=['ma', 'ten', 'tuyen_cmkt', 'hang_benh_vien', 'cap_cmkt', 'diem', 'dia_chi'],
        permission_required='danhmuckhac.view_noi_dkbd'
    )

@login_required
@require_GET
def noi_dkbd_list_api(request):
    """API để lấy danh sách nơi đăng kí ban đầu cho tabulator"""
    queryset = NoiDangKiBanDau.objects.all().order_by('ma')
    data = []
    for noi_dkbd in queryset:
        data.append({
            'id': noi_dkbd.id,
            'ma': noi_dkbd.ma,
            'ten': noi_dkbd.ten,
            'tuyen_cmkt': noi_dkbd.tuyen_cmkt,
            'hang_benh_vien': noi_dkbd.hang_benh_vien,
            'cap_cmkt': noi_dkbd.cap_cmkt,
            'diem': noi_dkbd.diem,
            'dia_chi': noi_dkbd.dia_chi,
            'hieu_luc': noi_dkbd.hieu_luc,
        })
    return JsonResponse({'data': data})

@login_required
@require_POST
def noi_dkbd_create(request):
    """Tạo mới nơi đăng kí ban đầu"""
    try:
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = NoiDangKiBanDauForm(post_data)
        
        if form.is_valid():
            noi_dkbd = form.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Đã thêm Nơi đăng kí ban đầu "{noi_dkbd.ten}" thành công',
                'data': {
                    'id': noi_dkbd.id,
                    'ma': noi_dkbd.ma,
                    'ten': noi_dkbd.ten,
                    'hieu_luc': noi_dkbd.hieu_luc
                }
            })
        else:           
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'success': False,
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
@login_required
@require_POST
def noi_dkbd_edit(request, pk):
    """Chỉnh sửa tỉnh"""
    try:
        noi_dkbd = get_object_or_404(NoiDangKiBanDau, pk=pk)
        
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = NoiDangKiBanDauForm(post_data, instance=noi_dkbd)
        
        if form.is_valid():
            old_name = noi_dkbd.ten
            old_ma = noi_dkbd.ma
            
            # Lưu form
            updated_dkbd = form.save()

            return JsonResponse({
                'status': 'success',
                'message': f'Đã cập nhật nơi ĐKBĐ "{updated_dkbd.ten}" thành công',
                'data': {
                    'id': updated_dkbd.id,
                    'ma': updated_dkbd.ma,
                    'ten': updated_dkbd.ten,
                    'hieu_luc': updated_dkbd.hieu_luc
                }
            })
        else:
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'status': 'error',
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except NoiDangKiBanDau.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy nơi đăng kí ban đầu'
        }, status=404)
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def noi_dkbd_delete(request, pk):
    """Xóa nơi đăng kí ban đầu"""
    try:
        noi_dkbd = get_object_or_404(NoiDangKiBanDau, pk=pk)
        ten = noi_dkbd.ten
        
        # Kiểm tra xem nơi đăng kí ban đầu có đang được sử dụng không
        if hasattr(noi_dkbd, 'noi_dkbd_set') and noi_dkbd.noi_dkbd_set.exists():
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể xóa tỉnh "{ten}" vì đang có quận/huyện thuộc tỉnh này'
            }, status=400)
        
        noi_dkbd.delete()
        
        return JsonResponse({
            'status': 'success',
            'message': f'Đã xóa nơi đăng kí ban đầu "{ten}" thành công'
        })
        
    except NoiDangKiBanDau.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy nơi đăng kí ban đầu'
        }, status=404)
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
@login_required
@require_POST
def noi_dkbd_toggle_hieu_luc(request, pk):
    """Toggle hiệu lực của nơi đăng kí ban đầu"""
    try:
        noi_dkbd = NoiDangKiBanDau.objects.get(pk=pk)
        hieu_luc = request.POST.get('hieu_luc') == 'true'
        
        # Lưu trạng thái cũ để log
        old_status = noi_dkbd.hieu_luc
        noi_dkbd.hieu_luc = hieu_luc
        noi_dkbd.save()
        
        # Log thay đổi
        action = 'kích hoạt' if hieu_luc else 'hủy kích hoạt'
        
        message = f"Đã {action} hiệu lực cho nơi đăng kí ban đầu '{noi_dkbd.ten}'"
        
        return JsonResponse({
            'status': 'success',
            'message': message,
            'hieu_luc': hieu_luc,
            'data': {
                'id': noi_dkbd.id,
                'ma': noi_dkbd.ma,
                'ten': noi_dkbd.ten,
                'hieu_luc': noi_dkbd.hieu_luc
            }
        })
        
    except NoiDangKiBanDau.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy nơi đăng kí ban đầu'
        }, status=404)
        
    except Exception as e:
         return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
def noi_dkbd_export(request):
    """Xuất danh sách tỉnh ra Excel"""
    try:
        # Lấy dữ liệu
        queryset = NoiDangKiBanDau.objects.all().order_by('ma')
        
        # Tạo workbook và worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        # Sửa tên sheet - loại bỏ ký tự đặc biệt
        ws.title = "Danh sách nơi ĐKBĐ"  # Không dùng dấu / và ký tự đặc biệt
        
        # Định nghĩa style
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Header
        headers = ['STT', 'Mã', 'Tên', 'Tuyến CMKT', 'Hạng bệnh viện', 'Cấp CMKT', 'Điểm', 'Địa chỉ', 'Hiệu lực']  # Loại bỏ dấu
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Dữ liệu
        for row, tinh in enumerate(queryset, 2):
            ws.cell(row=row, column=1, value=row-1).border = border  # STT
            ws.cell(row=row, column=2, value=tinh.ma).border = border
            ws.cell(row=row, column=3, value=tinh.ten).border = border
            ws.cell(row=row, column=4, value=tinh.ten).border = border
            ws.cell(row=row, column=5, value=tinh.tuyen_cmkt).border = border
            ws.cell(row=row, column=6, value=tinh.hang_benh_vien).border = border
            ws.cell(row=row, column=7, value=tinh.cap_cmkt).border = border
            ws.cell(row=row, column=8, value=tinh.diem).border = border
            ws.cell(row=row, column=9, value=tinh.dia_chi).border = border
            ws.cell(row=row, column=10, value="Có" if tinh.hieu_luc else "Không").border = border  # Loại bỏ dấu
        
        # Điều chỉnh độ rộng cột
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 12
        ws.column_dimensions['F'].width = 12
        ws.column_dimensions['G'].width = 12
        ws.column_dimensions['H'].width = 12
        ws.column_dimensions['I'].width = 12
        ws.column_dimensions['J'].width = 40
        
        # Tạo response
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        filename = f"danh_sach_noi_dkbd_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Có lỗi xảy ra khi xuất Excel: {str(e)}'
        }, status=500)


@login_required
@require_POST
def noi_dkbd_import(request):
    """Import danh sách nơi đăng kí ban đầu từ Excel với bulk operations"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'status': 'error',
                'message': 'Vui lòng chọn file Excel'
            }, status=400)

        file = request.FILES['file']
        
        # Kiểm tra định dạng file
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({
                'status': 'error',
                'message': 'File phải có định dạng .xlsx, .xls hoặc .csv'
            }, status=400)

        # Đọc file Excel với dtype=str để giữ nguyên format
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file, dtype=str)
            else:
                df = pd.read_excel(file, dtype=str)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể đọc file: {str(e)}'
            }, status=400)

        # Tìm cột phù hợp
        available_columns = df.columns.tolist()
        
        ma_columns = ['Mã', 'ma', 'Ma', 'MA', 'mã']
        ten_columns = ['Tên', 'ten', 'Ten', 'TEN', 'tên']
        tuyen_cmkt_columns = ['Tuyến CMKT', 'tuyen cmkt', 'Tuyen CMKT', 'TUYEN CMKT']
        hang_bv_columns = ['Hạng bệnh viện', 'hang bv', 'Hang BV', 'HANG BV']
        cap_cmkt_columns = ['Cấp CMKT', 'cap cmkt', 'Cap CMKT', 'CAP CMKT']
        diem_columns = ['Điểm', 'diem', 'Diem', 'DIEM']
        dia_chi_columns = ['Địa chỉ', 'dia chi', 'Dia chi', 'DIA CHI']
        hieu_luc_columns = ['Hiệu lực', 'hieu_luc', 'Hieu_luc', 'HIEU_LUC', 'hiệu lực', 'Hieu luc', 'Status', 'Active']
        
        ma_column = next((col for col in ma_columns if col in available_columns), None)
        ten_column = next((col for col in ten_columns if col in available_columns), None)
        tuyen_cmkt_columns = next((col for col in tuyen_cmkt_columns if col in available_columns), None)
        hang_bv_columns = next((col for col in hang_bv_columns if col in available_columns), None)
        cap_cmkt_columns = next((col for col in cap_cmkt_columns if col in available_columns), None)
        diem_columns = next((col for col in diem_columns if col in available_columns), None)
        dia_chi_columns = next((col for col in dia_chi_columns if col in available_columns), None)
        hieu_luc_column = next((col for col in hieu_luc_columns if col in available_columns), None)
        
        if not ma_column or not ten_column:
            missing = []
            if not ma_column:
                missing.append(f'Cột mã (một trong: {", ".join(ma_columns[:5])}...)')
            if not ten_column:
                missing.append(f'Cột tên (một trong: {", ".join(ten_columns[:5])}...)')
            
            return JsonResponse({
                'status': 'error',
                'message': f'File thiếu các cột bắt buộc: {"; ".join(missing)}'
            }, status=400)

        # BƯỚC 1: Validate và chuẩn hóa dữ liệu
        valid_records = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Xử lý mã đăng kí ban đầu
                ma_dkbd_raw = row[ma_column]
                if pd.isna(ma_dkbd_raw) or str(ma_dkbd_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã ĐKBĐ không được để trống')
                    continue
                
                ma_dkbd = str(ma_dkbd_raw).strip()
                if ma_dkbd.replace('.', '').replace(',', '').isdigit():
                    ma_dkbd = str(int(float(ma_dkbd))).zfill(2)
                
                # Xử lý tên đăng kí ban đầu
                ten_dkbd = str(row[ten_column]).strip() if pd.notna(row[ten_column]) else ''
                if not ten_dkbd or ten_dkbd.lower() in ['nan', 'null', 'na']:
                    errors.append(f'Dòng {index + 2}: Tên ĐKBĐ không được để trống')
                    continue
                
                # Xử lý hiệu lực
                hieu_luc = True
                if hieu_luc_column and pd.notna(row[hieu_luc_column]):
                    hieu_luc_value = str(row[hieu_luc_column]).strip().lower()
                    hieu_luc = hieu_luc_value in ['true', '1', 'có', 'yes', 'y', 'active', 'hiệu lực', 'hieu luc']

                # Lấy các trường khác nếu có
                tuyen_cmkt = str(row[tuyen_cmkt_columns]).strip() if tuyen_cmkt_columns and pd.notna(row[tuyen_cmkt_columns]) else None
                hang_benh_vien = str(row[hang_bv_columns]).strip() if hang_bv_columns and pd.notna(row[hang_bv_columns]) else None
                cap_cmkt = str(row[cap_cmkt_columns]).strip() if cap_cmkt_columns and pd.notna(row[cap_cmkt_columns]) else None
                diem = str(row[diem_columns]).strip() if diem_columns and pd.notna(row[diem_columns]) else None
                dia_chi = str(row[dia_chi_columns]).strip() if dia_chi_columns and pd.notna(row[dia_chi_columns]) else None

                
                valid_records.append({
                    'ma': ma_dkbd,
                    'ten': ten_dkbd,
                    'tuyen_cmkt': tuyen_cmkt,
                    'hang_benh_vien': hang_benh_vien,
                    'cap_cmkt': cap_cmkt,
                    'diem': diem,
                    'dia_chi': dia_chi,
                    'hieu_luc': hieu_luc,
                    'row_number': index + 2
                })
                
            except Exception as e:
                errors.append(f'Dòng {index + 2}: {str(e)}')

        if not valid_records:
            return JsonResponse({
                'status': 'error',
                'message': 'Không có dữ liệu hợp lệ để import',
                'errors': errors[:10]
            }, status=400)

        # BƯỚC 2: Lấy danh sách mã ĐKBĐ đã tồn tại
        existing_ma_dkbd = set(
            NoiDangKiBanDau.objects.filter(
                ma__in=[record['ma'] for record in valid_records]
            ).values_list('ma', flat=True)
        )

        # BƯỚC 3: Phân loại records thành create và update
        records_to_create = []
        records_to_update = []
        
        for record in valid_records:
            if record['ma'] in existing_ma_dkbd:
                records_to_update.append(record)
            else:
                records_to_create.append(record)

        success_count = 0
        
        # BƯỚC 4: Bulk create cho records mới
        if records_to_create:
            try:
                new_objects = [
                    NoiDangKiBanDau(
                        ma=record['ma'],
                        ten=record['ten'],
                        tuyen_cmkt=record.get('tuyen_cmkt'),
                        hang_benh_vien=record.get('hang_benh_vien'),
                        cap_cmkt=record.get('cap_cmkt'),
                        diem=record.get('diem'),
                        dia_chi=record.get('dia_chi'),
                        hieu_luc=record['hieu_luc'],
                    )
                    for record in records_to_create
                ]
                
                # Sử dụng bulk_create với batch_size để tối ưu memory
                batch_size = 1000
                for i in range(0, len(new_objects), batch_size):
                    batch = new_objects[i:i + batch_size]
                    NoiDangKiBanDau.objects.bulk_create(batch, ignore_conflicts=True)
                
                success_count += len(records_to_create)
                
            except Exception as e:
                errors.append(f"Lỗi khi tạo mới hàng loạt: {str(e)}")

        # BƯỚC 5: Bulk update cho records đã tồn tại
        if records_to_update:
            try:
                # Lấy tất cả objects cần update
                existing_objects = {
                    obj.ma: obj 
                    for obj in NoiDangKiBanDau.objects.filter(
                        ma__in=[record['ma'] for record in records_to_update] # Sửa lỗi ở đây
                    )
                }
                
                # Cập nhật thông tin
                objects_to_update = []
                for record in records_to_update:
                    if record['ma'] in existing_objects:
                        obj = existing_objects[record['ma']]
                        obj.ten = record['ten']
                        obj.hieu_luc = record['hieu_luc']
                        obj.tuyen_cmkt = record.get('tuyen_cmkt')
                        obj.hang_benh_vien = record.get('hang_benh_vien')
                        obj.cap_cmkt = record.get('cap_cmkt')
                        obj.diem = record.get('diem')
                        obj.dia_chi = record.get('dia_chi')
                        objects_to_update.append(obj)
                
                # Bulk update với batch_size
                if objects_to_update:
                    batch_size = 1000
                    for i in range(0, len(objects_to_update), batch_size):
                        batch = objects_to_update[i:i + batch_size]
                        NoiDangKiBanDau.objects.bulk_update(
                            batch, 
                            ['ten', 'tuyen_cmkt', 'hang_benh_vien', 'cap_cmkt', 'diem', 'dia_chi', 'hieu_luc'],
                            batch_size=len(batch)
                        )
                    
                    success_count += len(objects_to_update)
                    logger.info(f"Bulk updated {len(objects_to_update)} existing NoiDangKiBanDau records")
                
            except Exception as e:
                logger.error(f"Bulk update error: {str(e)}")
                errors.append(f"Lỗi khi cập nhật hàng loạt: {str(e)}")

        error_count = len(errors)
        message = f'Import thành công {success_count} bản ghi'
        if error_count > 0:
            message += f', thất bại {error_count} bản ghi'

        return JsonResponse({
            'status': 'success',
            'message': message,
            'success_count': success_count,
            'error_count': error_count,
            'created_count': len(records_to_create),
            'updated_count': len(records_to_update),
            'errors': errors[:10],
            'columns_used': {
                'ma': ma_column,
                'ten': ten_column,
                'tuyen_cmkt': tuyen_cmkt_columns,
                'hang_benh_vien': hang_bv_columns,
                'cap_cmkt': cap_cmkt_columns,
                'diem': diem_columns,
                'dia_chi': dia_chi_columns,
                'hieu_luc': hieu_luc_column
            }
        })

    except Exception as e:
        logger.error(f"Lỗi khi import Excel: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)
    
@login_required
@require_POST
def noi_dkbd_bulk_delete(request):
    """Xóa nhiều nơi đăng kí ban đầu cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        deleted_count = NoiDangKiBanDau.objects.filter(id__in=ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Đã xóa {deleted_count} nơi ĐKBĐ'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST  
def noi_dkbd_bulk_toggle(request):
    """Bật/tắt hiệu lực nhiều nơi đăng kí ban đầu cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        action = request.POST.get('action')
        
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
            
        if action not in ['activate', 'deactivate']:
            return JsonResponse({
                'success': False,
                'message': 'Hành động không hợp lệ'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        hieu_luc = action == 'activate'
        updated_count = NoiDangKiBanDau.objects.filter(id__in=ids).update(hieu_luc=hieu_luc)
        
        action_text = 'kích hoạt' if hieu_luc else 'vô hiệu hóa'
        
        return JsonResponse({
            'success': True,
            'message': f'Đã {action_text} {updated_count} nơi ĐKBD'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
def pvcm_list(request):
    """Danh sách Phạm vi chuyên môn"""
    return base_list_view(
        request=request,
        model=PhamViChuyenMon,
        form_class=PhamViChuyenMonForm,
        template_name='danhmuckhac/phamvichuyenmon_list.html',
        title=_('Danh mục Phạm vi chuyên môn'),
        ordering_fields=['ma_pham_vi'],
        searchable_fields=['ma_pham_vi', 'ten_chuyen_khoa'],
        permission_required='danhmuckhac.view_pvcm'
    )

@login_required
@require_GET
def pvcm_list_api(request):
    """API để lấy danh sách tỉnh cho Tabulator"""
    queryset = PhamViChuyenMon.objects.all().order_by('ma_pham_vi')
    data = []
    for pvcm in queryset:
        data.append({
            'id': pvcm.id,
            'ma_pham_vi': pvcm.ma_pham_vi,
            'ten_chuyen_khoa': pvcm.ten_chuyen_khoa,
            'hieu_luc': pvcm.hieu_luc,
        })
    return JsonResponse({'data': data})

@login_required
@require_POST
def pvcm_create(request):
    """Tạo mới tỉnh"""
    try:
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = PhamViChuyenMonForm(post_data)
        
        if form.is_valid():
            pvcm = form.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Đã thêm tỉnh/thành phố "{pvcm.ten_chuyen_khoa}" thành công',
                'data': {
                    'id': pvcm.id,
                    'ma_pham_vi': pvcm.ma_pham_vi,
                    'ten_chuyen_khoa': pvcm.ten_chuyen_khoa,
                    'hieu_luc': pvcm.hieu_luc
                }
            })
        else:
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'success': False,
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except Exception as e:
        logger.error(f"Lỗi khi tạo PVCM: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def pvcm_edit(request, pk):
    """Chỉnh sửa phạm vi chuyên môn"""
    try:
        pvcm = get_object_or_404(PhamViChuyenMon, pk=pk)
        
        # Xử lý checkbox hieu_luc
        post_data = request.POST.copy()
        if 'hieu_luc' not in post_data:
            post_data['hieu_luc'] = False
        else:
            # Chuyển đổi giá trị checkbox thành boolean
            hieu_luc_value = post_data.get('hieu_luc')
            if hieu_luc_value in ['on', '1', 'true', 'True']:
                post_data['hieu_luc'] = True
            else:
                post_data['hieu_luc'] = False
        
        form = PhamViChuyenMonForm(post_data, instance=pvcm)
        
        if form.is_valid():
            old_name = pvcm.ten_chuyen_khoa
            old_ma = pvcm.ma_pham_vi
            
            # Lưu form
            updated_pvcm = form.save()
   
            return JsonResponse({
                'status': 'success',
                'message': f'Đã cập nhật PVCM "{updated_pvcm.ten_chuyen_khoa}" thành công',
                'data': {
                    'id': updated_pvcm.id,
                    'ma_pham_vi': updated_pvcm.ma_pham_vi,
                    'ten_chuyen_khoa': updated_pvcm.ten_chuyen_khoa,
                    'hieu_luc': updated_pvcm.hieu_luc
                }
            })
        else:
            # Trả về lỗi validation chi tiết
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            
            return JsonResponse({
                'status': 'error',
                'message': 'Dữ liệu không hợp lệ',
                'errors': errors
            }, status=400)
            
    except PhamViChuyenMon.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng cập nhật PVCM không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy phạm vi chuyên môn'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật tỉnh ID {pk}: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def pvcm_delete(request, pk):
    """Xóa phạm vi chuyên môn"""
    try:
        pvcm = get_object_or_404(PhamViChuyenMon, pk=pk)
        ten_chuyen_khoa = pvcm.ten_chuyen_khoa
        
        pvcm.delete()
        
        return JsonResponse({
            'status': 'success',
            'message': f'Đã xóa PVCM: "{ten_chuyen_khoa}" thành công'
        })
        
    except PhamViChuyenMon.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng xóa PVCM không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy phạm vi chuyên môn'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Lỗi khi xóa PVCM ID {pk}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def pvcm_toggle_hieu_luc(request, pk):
    """Toggle hiệu lực của phạm vi chuyên môn"""
    try:
        pvcm = PhamViChuyenMon.objects.get(pk=pk)
        hieu_luc = request.POST.get('hieu_luc') == 'true'
        
        # Lưu trạng thái cũ để log
        old_status = pvcm.hieu_luc
        pvcm.hieu_luc = hieu_luc
        pvcm.save()
        
        # Log thay đổi
        action = 'kích hoạt' if hieu_luc else 'hủy kích hoạt'
        
        message = f"Đã {action} hiệu lực cho PVCM '{pvcm.ten_chuyen_khoa}'"
        
        return JsonResponse({
            'status': 'success',
            'message': message,
            'hieu_luc': hieu_luc,
            'data': {
                'id': pvcm.id,
                'ma_pham_vi': pvcm.ma_pham_vi,
                'ten_chuyen_khoa': pvcm.ten_chuyen_khoa,
                'hieu_luc': pvcm.hieu_luc
            }
        })
        
    except PhamViChuyenMon.DoesNotExist:
        logger.error(f"User {request.user.username} cố gắng toggle hiệu lực cho PVCM không tồn tại (ID: {pk})")
        return JsonResponse({
            'status': 'error',
            'message': 'Không tìm thấy phạm vi chuyên môn'
        }, status=404)
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
def pvcm_export(request):
    """Xuất danh sách phạm vi chuyên môn ra Excel"""
    try:
        # Lấy dữ liệu
        queryset = PhamViChuyenMon.objects.all().order_by('ma_pham_vi')
        
        # Tạo workbook và worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        # Sửa tên sheet - loại bỏ ký tự đặc biệt
        ws.title = "Danh sach PVCM"  # Không dùng dấu / và ký tự đặc biệt
        
        # Định nghĩa style
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Header
        headers = ['STT', 'Mã phạm vi', 'Tên chuyên khoa', 'Hiệu lực']  # Loại bỏ dấu
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Dữ liệu
        for row, pvcm in enumerate(queryset, 2):
            ws.cell(row=row, column=1, value=row-1).border = border  # STT
            ws.cell(row=row, column=2, value=pvcm.ma_pham_vi).border = border
            ws.cell(row=row, column=3, value=pvcm.ten_chuyen_khoa).border = border
            ws.cell(row=row, column=4, value="Có" if pvcm.hieu_luc else "Không").border = border  # Loại bỏ dấu
        
        # Điều chỉnh độ rộng cột
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 40
        ws.column_dimensions['D'].width = 12
        
        # Tạo response
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        filename = f"danh_sach_tpvcm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        logger.info(f"User {request.user.username} đã xuất danh sách phạm vi chuyên môn ra Excel")
        
        return response
        
    except Exception as e:
        logger.error(f"Lỗi khi xuất Excel: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Có lỗi xảy ra khi xuất Excel: {str(e)}'
        }, status=500)

@login_required
@require_POST
def pvcm_import(request):
    """Import danh sách phạm vi chuyên môn từ Excel với bulk operations"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'status': 'error',
                'message': 'Vui lòng chọn file Excel'
            }, status=400)

        file = request.FILES['file']
        
        # Kiểm tra định dạng file
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({
                'status': 'error',
                'message': 'File phải có định dạng .xlsx, .xls hoặc .csv'
            }, status=400)

        # Đọc file Excel với dtype=str để giữ nguyên format
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file, dtype=str)
            else:
                df = pd.read_excel(file, dtype=str)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Không thể đọc file: {str(e)}'
            }, status=400)

        # Tìm cột phù hợp
        available_columns = df.columns.tolist()
        
        ma_pvcm_columns = ['Mã phạm vi', 'ma_pham_vi', 'Ma pham vi']
        ten_chuyen_khoa_columns = ['Tên chuyên khoa', 'ten_chuyen_khoa', 'Ten chuyen khoa']
        hieu_luc_columns = ['Hiệu lực', 'hieu_luc', 'Hieu_luc', 'HIEU_LUC', 'hiệu lực', 'Hieu luc', 'Status', 'Active']
        
        ma_column = next((col for col in ma_pvcm_columns if col in available_columns), None)
        ten_column = next((col for col in ten_chuyen_khoa_columns if col in available_columns), None)
        hieu_luc_column = next((col for col in hieu_luc_columns if col in available_columns), None)
        
        if not ma_column or not ten_column:
            missing = []
            if not ma_column:
                missing.append(f'Cột mã PVCM (một trong: {", ".join(ma_pvcm_columns[:5])}...)')
            if not ten_column:
                missing.append(f'Cột tên PVCM (một trong: {", ".join(ma_pvcm_columns[:5])}...)')
            
            return JsonResponse({
                'status': 'error',
                'message': f'File thiếu các cột bắt buộc: {"; ".join(missing)}'
            }, status=400)

        # BƯỚC 1: Validate và chuẩn hóa dữ liệu
        valid_records = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Xử lý mã phạm vi chuyên môn
                ma_pvcm_raw = row[ma_column]
                if pd.isna(ma_pvcm_raw) or str(ma_pvcm_raw).strip() == '':
                    errors.append(f'Dòng {index + 2}: Mã PVCM không được để trống')
                    continue
                
                ma_pvcm = str(ma_pvcm_raw).strip().replace('.', ',')

                # Xử lý tên chuyên khoa
                ten_pvcm = str(row[ten_column]).strip() if pd.notna(row[ten_column]) else ''
                if not ten_pvcm or ten_pvcm.lower() in ['nan', 'null']:
                    errors.append(f'Dòng {index + 2}: Tên PVCM không được để trống')
                    continue
                
                # Xử lý hiệu lực
                hieu_luc = True
                if hieu_luc_column and pd.notna(row[hieu_luc_column]):
                    hieu_luc_value = str(row[hieu_luc_column]).strip().lower()
                    hieu_luc = hieu_luc_value in ['true', '1', 'có', 'yes', 'y', 'active', 'hiệu lực', 'hieu luc']
                
                valid_records.append({
                    'ma_pham_vi': ma_pvcm,
                    'ten_chuyen_khoa': ten_pvcm,
                    'hieu_luc': hieu_luc,
                    'row_number': index + 2
                })
                
            except Exception as e:
                errors.append(f'Dòng {index + 2}: {str(e)}')

        if not valid_records:
            return JsonResponse({
                'status': 'error',
                'message': 'Không có dữ liệu hợp lệ để import',
                'errors': errors[:10]
            }, status=400)

        # BƯỚC 2: Lấy danh sách mã tỉnh đã tồn tại
        existing_ma_pham_vi = set(
            PhamViChuyenMon.objects.filter(
                ma_pham_vi__in=[record['ma_pham_vi'] for record in valid_records]
            ).values_list('ma_pham_vi', flat=True)
        )

        # BƯỚC 3: Phân loại records thành create và update
        records_to_create = []
        records_to_update = []
        
        for record in valid_records:
            if record['ma_pham_vi'] in existing_ma_pham_vi:
                records_to_update.append(record)
            else:
                records_to_create.append(record)

        success_count = 0
        
        # BƯỚC 4: Bulk create cho records mới
        if records_to_create:
            try:
                new_objects = [
                    PhamViChuyenMon(
                        ma_pham_vi=record['ma_pham_vi'],
                        ten_chuyen_khoa=record['ten_chuyen_khoa'],
                        hieu_luc=record['hieu_luc']
                    )
                    for record in records_to_create
                ]
                
                # Sử dụng bulk_create với batch_size để tối ưu memory
                batch_size = 1000
                for i in range(0, len(new_objects), batch_size):
                    batch = new_objects[i:i + batch_size]
                    PhamViChuyenMon.objects.bulk_create(batch, ignore_conflicts=True)
                
                success_count += len(records_to_create)
                logger.info(f"Bulk created {len(records_to_create)} new provinces")
                
            except Exception as e:
                logger.error(f"Bulk create error: {str(e)}")
                errors.append(f"Lỗi khi tạo mới hàng loạt: {str(e)}")

        # BƯỚC 5: Bulk update cho records đã tồn tại
        if records_to_update:
            try:
                # Lấy tất cả objects cần update
                existing_objects = {
                    obj.ma_pham_vi: obj 
                    for obj in PhamViChuyenMon.objects.filter(
                        ma_pham_vi__in=[record['ma_pham_vi'] for record in records_to_update]
                    )
                }
                
                # Cập nhật thông tin
                objects_to_update = []
                for record in records_to_update:
                    if record['ma_pham_vi'] in existing_objects:
                        obj = existing_objects[record['ma_pham_vi']]
                        obj.ten_chuyen_khoa = record['ten_chuyen_khoa']
                        obj.hieu_luc = record['hieu_luc']
                        objects_to_update.append(obj)
                
                # Bulk update với batch_size
                if objects_to_update:
                    batch_size = 1000
                    for i in range(0, len(objects_to_update), batch_size):
                        batch = objects_to_update[i:i + batch_size]
                        PhamViChuyenMon.objects.bulk_update(
                            batch, 
                            ['ten_chuyen_khoa', 'hieu_luc'],
                            batch_size=len(batch)
                        )
                    
                    success_count += len(objects_to_update)
                    logger.info(f"Bulk updated {len(objects_to_update)} existing PVCM")
                
            except Exception as e:
                logger.error(f"Bulk update error: {str(e)}")
                errors.append(f"Lỗi khi cập nhật hàng loạt: {str(e)}")

        error_count = len(errors)
        message = f'Import thành công {success_count} bản ghi'
        if error_count > 0:
            message += f', thất bại {error_count} bản ghi'

        logger.info(f"User {request.user.username} đã import PVCM: {success_count} thành công, {error_count} lỗi")

        return JsonResponse({
            'status': 'success',
            'message': message,
            'success_count': success_count,
            'error_count': error_count,
            'created_count': len(records_to_create),
            'updated_count': len(records_to_update),
            'errors': errors[:10],
            'columns_used': {
                'ma_pham_vi': ma_column,
                'ten_chuyen_khoa': ten_column,
                'hieu_luc': hieu_luc_column
            }
        })

    except Exception as e:
        logger.error(f"Lỗi khi import Excel: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'Có lỗi xảy ra: {str(e)}'
        }, status=500)

@login_required
@require_POST
def pvcm_bulk_delete(request):
    """Xóa nhiều phạm vi chuyên môn cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        deleted_count = Tinh.objects.filter(id__in=ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Đã xóa {deleted_count} tỉnh/thành phố'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })

@login_required
@require_POST  
def pvcm_bulk_toggle(request):
    """Bật/tắt hiệu lực nhiều phạm vi chuyên môn cùng lúc"""
    try:
        ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
        action = request.POST.get('action')
        
        if not ids:
            return JsonResponse({
                'success': False,
                'message': 'Không có mục nào được chọn'
            })
            
        if action not in ['activate', 'deactivate']:
            return JsonResponse({
                'success': False,
                'message': 'Hành động không hợp lệ'
            })
        
        # Chuyển đổi string ids thành integers
        ids = [int(id) for id in ids if id.isdigit()]
        
        hieu_luc = action == 'activate'
        updated_count = PhamViChuyenMon.objects.filter(id__in=ids).update(hieu_luc=hieu_luc)
        
        action_text = 'kích hoạt' if hieu_luc else 'vô hiệu hóa'
        
        return JsonResponse({
            'success': True,
            'message': f'Đã {action_text} {updated_count} phạm vi chuyên môn'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Có lỗi xảy ra: {str(e)}'
        })
    