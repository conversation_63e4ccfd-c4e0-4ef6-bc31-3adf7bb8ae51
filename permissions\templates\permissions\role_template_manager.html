{% extends 'layouts/base.html' %}

{% block title %}Quản lý mẫu vai trò | Hospital Manager{% endblock %}

{% block page_title %}Quản lý mẫu vai trò{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'permissions:role_list' %}">Quản lý vai trò</a></li>
<li class="breadcrumb-item active">Quản lý mẫu vai trò</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Quản lý mẫu vai trò</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <!-- /.card-header -->
      <div class="card-body">
        <div class="alert alert-info">
          <i class="fas fa-info-circle mr-2"></i>
          Trang này cho phép bạn tùy chỉnh các quyền mặc định cho từng loại vai trò. Các thay đổi sẽ ảnh hưởng đến việc tạo vai trò mới từ mẫu.
        </div>

        {% if messages %}
        <div class="alert alert-success">
          {% for message in messages %}
            {{ message }}
          {% endfor %}
        </div>
        {% endif %}

        <div class="nav-tabs-custom">
          <ul class="nav nav-tabs" id="role-templates-tab" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="superadmin-tab" data-toggle="tab" href="#superadmin" role="tab" aria-controls="superadmin" aria-selected="true">
                <i class="fas fa-user-shield mr-1"></i> Super Admin
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="admin-tab" data-toggle="tab" href="#admin" role="tab" aria-controls="admin" aria-selected="false">
                <i class="fas fa-user-cog mr-1"></i> Admin
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="department-manager-tab" data-toggle="tab" href="#department-manager" role="tab" aria-controls="department-manager" aria-selected="false">
                <i class="fas fa-user-tie mr-1"></i> Quản lý khoa phòng
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="staff-tab" data-toggle="tab" href="#staff" role="tab" aria-controls="staff" aria-selected="false">
                <i class="fas fa-user mr-1"></i> Nhân viên
              </a>
            </li>
          </ul>
          <div class="tab-content" id="role-templates-content">
            <!-- Super Admin Tab -->
            <div class="tab-pane fade show active" id="superadmin" role="tabpanel" aria-labelledby="superadmin-tab">
              <form method="post" action="{% url 'permissions:save_role_template' 'superadmin' %}">
                {% csrf_token %}
                <div class="card">
                  <div class="card-header bg-danger">
                    <h5 class="card-title">Quyền mặc định cho Super Admin</h5>
                    <div class="card-tools">
                      <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                    <p class="text-muted">Super Admin thường có tất cả các quyền trong hệ thống. Bạn có thể tùy chỉnh quyền nếu cần.</p>
                    
                    <div class="form-group">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="superadmin-all" checked>
                        <label class="custom-control-label" for="superadmin-all"><strong>Chọn tất cả</strong></label>
                      </div>
                    </div>
                    
                    <div class="row">
                      {% for group_name, permissions in grouped_permissions.items %}
                      <div class="col-md-12 mb-3">
                        <div class="card">
                          <div class="card-header bg-light">
                            <h5 class="card-title">{{ group_name }}</h5>
                            <div class="card-tools">
                              <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input group-checkbox" id="superadmin-group-{{ forloop.counter }}" checked>
                                <label class="custom-control-label" for="superadmin-group-{{ forloop.counter }}"><strong>Chọn nhóm</strong></label>
                              </div>
                            </div>
                          </div>
                          <div class="card-body">
                            <div class="row">
                              {% for permission in permissions %}
                              <div class="col-md-6">
                                <div class="form-check">
                                  <input type="checkbox" class="form-check-input permission-checkbox" id="superadmin-permission-{{ permission.id }}"
                                         name="permissions" value="{{ permission.id }}" checked>
                                  <label class="form-check-label" for="superadmin-permission-{{ permission.id }}">
                                    {{ permission.name_vi|default:permission.name }}
                                  </label>
                                </div>
                              </div>
                              {% endfor %}
                            </div>
                          </div>
                        </div>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                  <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                    <button type="button" class="btn btn-default" id="reset-superadmin">Khôi phục mặc định</button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Admin Tab -->
            <div class="tab-pane fade" id="admin" role="tabpanel" aria-labelledby="admin-tab">
              <form method="post" action="{% url 'permissions:save_role_template' 'admin' %}">
                {% csrf_token %}
                <div class="card">
                  <div class="card-header bg-warning">
                    <h5 class="card-title">Quyền mặc định cho Admin</h5>
                    <div class="card-tools">
                      <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                    <p class="text-muted">Admin có hầu hết các quyền, ngoại trừ một số quyền nhạy cảm. Bạn có thể tùy chỉnh quyền nếu cần.</p>
                    
                    <div class="form-group">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="admin-all">
                        <label class="custom-control-label" for="admin-all"><strong>Chọn tất cả</strong></label>
                      </div>
                    </div>
                    
                    <div class="row">
                      {% for group_name, permissions in grouped_permissions.items %}
                      <div class="col-md-12 mb-3">
                        <div class="card">
                          <div class="card-header bg-light">
                            <h5 class="card-title">{{ group_name }}</h5>
                            <div class="card-tools">
                              <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input group-checkbox" id="admin-group-{{ forloop.counter }}">
                                <label class="custom-control-label" for="admin-group-{{ forloop.counter }}"><strong>Chọn nhóm</strong></label>
                              </div>
                            </div>
                          </div>
                          <div class="card-body">
                            <div class="row">
                              {% for permission in permissions %}
                              <div class="col-md-6">
                                <div class="form-check">
                                  <input type="checkbox" class="form-check-input permission-checkbox" id="admin-permission-{{ permission.id }}"
                                         name="permissions" value="{{ permission.id }}"
                                         {% if permission.id in admin_permissions %}checked{% endif %}>
                                  <label class="form-check-label" for="admin-permission-{{ permission.id }}">
                                    {{ permission.name_vi|default:permission.name }}
                                  </label>
                                </div>
                              </div>
                              {% endfor %}
                            </div>
                          </div>
                        </div>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                  <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                    <button type="button" class="btn btn-default" id="reset-admin">Khôi phục mặc định</button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Department Manager Tab -->
            <div class="tab-pane fade" id="department-manager" role="tabpanel" aria-labelledby="department-manager-tab">
              <!-- Tương tự như các tab khác -->
            </div>
            
            <!-- Staff Tab -->
            <div class="tab-pane fade" id="staff" role="tabpanel" aria-labelledby="staff-tab">
              <!-- Tương tự như các tab khác -->
            </div>
          </div>
        </div>
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    // Xử lý chọn tất cả
    $('#superadmin-all, #admin-all, #department-manager-all, #staff-all').on('change', function() {
      var tabId = $(this).attr('id').split('-')[0];
      $('#' + tabId + ' .permission-checkbox').prop('checked', $(this).prop('checked'));
      $('#' + tabId + ' .group-checkbox').prop('checked', $(this).prop('checked'));
    });
    
    // Xử lý chọn nhóm
    $('.group-checkbox').on('change', function() {
      var groupId = $(this).attr('id');
      var tabId = groupId.split('-')[0];
      var groupNum = groupId.split('-')[2];
      
      $(this).closest('.card').find('.permission-checkbox').prop('checked', $(this).prop('checked'));
      
      // Kiểm tra nếu tất cả các nhóm đều được chọn
      var allGroupsChecked = true;
      $('#' + tabId + ' .group-checkbox').each(function() {
        if (!$(this).prop('checked')) {
          allGroupsChecked = false;
          return false;
        }
      });
      
      $('#' + tabId + '-all').prop('checked', allGroupsChecked);
    });
    
    // Xử lý chọn quyền
    $('.permission-checkbox').on('change', function() {
      var tabId = $(this).attr('id').split('-')[0];
      var card = $(this).closest('.card');
      
      // Kiểm tra nếu tất cả các quyền trong nhóm đều được chọn
      var allPermissionsChecked = true;
      card.find('.permission-checkbox').each(function() {
        if (!$(this).prop('checked')) {
          allPermissionsChecked = false;
          return false;
        }
      });
      
      card.find('.group-checkbox').prop('checked', allPermissionsChecked);
      
      // Kiểm tra nếu tất cả các quyền đều được chọn
      var allChecked = true;
      $('#' + tabId + ' .permission-checkbox').each(function() {
        if (!$(this).prop('checked')) {
          allChecked = false;
          return false;
        }
      });
      
      $('#' + tabId + '-all').prop('checked', allChecked);
    });
    
    // Xử lý khôi phục mặc định
    $('#reset-superadmin').on('click', function() {
      $('#superadmin .permission-checkbox').prop('checked', true);
      $('#superadmin .group-checkbox').prop('checked', true);
      $('#superadmin-all').prop('checked', true);
    });
    
    $('#reset-admin').on('click', function() {
      // Đặt lại các quyền mặc định cho admin
      $('#admin .permission-checkbox').prop('checked', false);
      $('#admin .group-checkbox').prop('checked', false);
      $('#admin-all').prop('checked', false);
      
      // Chọn các quyền mặc định
      {% for permission_id in admin_default_permissions %}
      $('#admin-permission-{{ permission_id }}').prop('checked', true);
      {% endfor %}
      
      // Cập nhật trạng thái của các checkbox nhóm
      $('#admin .group-checkbox').each(function() {
        var card = $(this).closest('.card');
        var allChecked = true;
        card.find('.permission-checkbox').each(function() {
          if (!$(this).prop('checked')) {
            allChecked = false;
            return false;
          }
        });
        $(this).prop('checked', allChecked);
      });
      
      // Cập nhật trạng thái của checkbox chọn tất cả
      var allGroupsChecked = true;
      $('#admin .group-checkbox').each(function() {
        if (!$(this).prop('checked')) {
          allGroupsChecked = false;
          return false;
        }
      });
      $('#admin-all').prop('checked', allGroupsChecked);
    });
    
    // Tương tự cho các vai trò khác
  });
</script>
{% endblock %}
