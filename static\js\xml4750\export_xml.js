/*
 * Hà<PERSON> chuyển đổi tên trường từ camelCase sang UPPER_SNAKE_CASE cho XML
 * Dựa trên danh sách mapping từ utils.py
 * @param {string} fieldName - Tên trường dạng camelCase
 * @returns {string} - Tên trường dạng UPPER_SNAKE_CASE
 */
function fieldNameToXmlTag(fieldName) {
    // Danh sách các trường hợp đặc biệt
    const specialCases = {
        'lyDoVNT': 'LY_DO_VNT',
        'maLyDoVNT': 'MA_LY_DO_VNT',
        'maThuoc': 'MA_THUOC',
        'duPhong': 'DU_PHONG',
        'maLK': 'MA_LK',
        'maBN': 'MA_BN',
        'hoTen': 'HO_TEN',
        'soCCCD': 'SO_CCCD',
        'ngaySinh': 'NGAY_SINH',
        'gioiTinh': 'GIOI_TINH',
        'nhomMau': 'NHOM_MAU',
        'maQuocTich': 'MA_QUOCTICH',
        'maDanToc': 'MA_DANTOC',
        'maNgheNghiep': 'MA_NGHE_NGHIEP',
        'diaChi': 'DIA_CHI',
        'maTinhCuTru': 'MATINH_CU_TRU',
        'maHuyenCuTru': 'MAHUYEN_CU_TRU',
        'maXaCuTru': 'MAXA_CU_TRU',
        'dienThoai': 'DIEN_THOAI',
        'maTheBHYT': 'MA_THE_BHYT',
        'maDKBD': 'MA_DKBD',
        'gtTheTu': 'GT_THE_TU',
        'gtTheDen': 'GT_THE_DEN',
        'ngayMienCCT': 'NGAY_MIEN_CCT',
        'lyDoVV': 'LY_DO_VV',
        'chanDoanVao': 'CHAN_DOAN_VAO',
        'chanDoanRV': 'CHAN_DOAN_RV',
        'maBenhChinh': 'MA_BENH_CHINH',
        'maBenhKT': 'MA_BENH_KT',
        'maBenhYHCT': 'MA_BENH_YHCT',
        'maPTTTQT': 'MA_PTTT_QT',
        'maDoiTuongKCB': 'MA_DOITUONG_KCB',
        'maNoiDi': 'MA_NOI_DI',
        'maNoiDen': 'MA_NOI_DEN',
        'maTaiNan': 'MA_TAI_NAN',
        'ngayVao': 'NGAY_VAO',
        'ngayVaoNoiTru': 'NGAY_VAO_NOI_TRU',
        'ngayRa': 'NGAY_RA',
        'giayChuyenTuyen': 'GIAY_CHUYEN_TUYEN',
        'soNgayDtri': 'SO_NGAY_DTRI',
        'ppDieuTri': 'PP_DIEU_TRI',
        'ketQuaDtri': 'KET_QUA_DTRI',
        'maLoaiRV': 'MA_LOAI_RV',
        'ghiChu': 'GHI_CHU',
        'ngayTToan': 'NGAY_TTOAN',
        'tThuoc': 'T_THUOC',
        'tVTYT': 'T_VTYT',
        'tTongChiBV': 'T_TONGCHI_BV',
        'tTongChiBH': 'T_TONGCHI_BH',
        'tBNTT': 'T_BNTT',
        'tBNCCT': 'T_BNCCT',
        'tBHTT': 'T_BHTT',
        'tNguonKhac': 'T_NGUONKHAC',
        'tBHTTGDV': 'T_BHTT_GDV',
        'namQT': 'NAM_QT',
        'thangQT': 'THANG_QT',
        'maLoaiKCB': 'MA_LOAI_KCB',
        'maKhoa': 'MA_KHOA',
        'maCSKCB': 'MA_CSKCB',
        'maKhuVuc': 'MA_KHUVUC',
        'canNang': 'CAN_NANG',
        'canNangCon': 'CAN_NANG_CON',
        'namNamLienTuc': 'NAM_NAM_LIEN_TUC',
        'ngayTaiKham': 'NGAY_TAI_KHAM',
        'maHSBA': 'MA_HSBA',
        'maTTDV': 'MA_TTDV',
        'maDichVu': 'MA_DICH_VU',
        'tenDichVu': 'TEN_DICH_VU',
        'tenThuoc': 'TEN_THUOC',
        'maVatTu': 'MA_VAT_TU',
        'tenVatTu': 'TEN_VAT_TU',
        'ngayYL': 'NGAY_YL',
        // Các trường cho XML2
        'maPPCheBien': 'MA_PP_CHEBIEN',
        'maCSKCBThuoc': 'MA_CSKCB_THUOC',
        'maNhom': 'MA_NHOM',
        'donViTinh': 'DON_VI_TINH',
        'hamLuong': 'HAM_LUONG',
        'duongDung': 'DUONG_DUNG',
        'dangBaoChe': 'DANG_BAO_CHE',
        'lieuDung': 'LIEU_DUNG',
        'cachDung': 'CACH_DUNG',
        'soDangKy': 'SO_DANG_KY',
        'ttThau': 'TT_THAU',
        'phamVi': 'PHAM_VI',
        'tyLeTTBH': 'TYLE_TT_BH',
        'soLuong': 'SO_LUONG',
        'donGia': 'DON_GIA',
        'thanhTienBV': 'THANH_TIEN_BV',
        'thanhTienBH': 'THANH_TIEN_BH',
        'tNguonKhacNSNN': 'T_NGUONKHAC_NSNN',
        'tNguonKhacVTNN': 'T_NGUONKHAC_VTNN',
        'tNguonKhacVTTN': 'T_NGUONKHAC_VTTN',
        'tNguonKhacCL': 'T_NGUONKHAC_CL',
        'mucHuong': 'MUC_HUONG',
        'maBacSi': 'MA_BAC_SI',
        'ngayTHYL': 'NGAY_TH_YL',
        'maPTTT': 'MA_PTTT',
        'nguonCTra': 'NGUON_CTRA',
        'vetThuongTP': 'VET_THUONG_TP',
        // Các trường cho XML3-XML15
        'goiVTYT': 'GOI_VTYT',
        'maXangDau': 'MA_XANG_DAU',
        'tyLeTTDV': 'TYLE_TT_DV',
        'tTranTT': 'T_TRAN_TT',
        'maGiuong': 'MA_GIUONG',
        'nguoiThucHien': 'NGUOI_THUC_HIEN',
        'ngayKQ': 'NGAY_KQ',
        'ppVoCam': 'PP_VO_CAM',
        'viTriThDVKT': 'VI_TRI_TH_DVKT',
        'maMay': 'MA_MAY',
        'maHieuSP': 'MA_HIEU_SP',
        'taiSuDung': 'TAI_SU_DUNG',
        'donViDo': 'DON_VI_DO',
        'maBSDocKQ': 'MA_BS_DOC_KQ',
        'dienBienLS': 'DIEN_BIEN_LS',
        'giaiDoanBenh': 'GIAI_DOAN_BENH',
        'thoiDiemDBLS': 'THOI_DIEM_DBLS',
        'bdDTARV': 'BD_DTARV',
        'maPhacDoDieuTriBD': 'MA_PHAC_DO_DIEU_TRI_BD',
        'maBacPhacDoBD': 'MA_BAC_PHAC_DO_BD',
        'maLydoDtri': 'MA_LY_DO_DTRI',
        'sangLocLao': 'SANG_LOC_LAO',
        'phacDoDtriLao': 'PHAC_DO_DTRI_LAO',
        'ngayBDDTriLao': 'NGAY_BD_DTRI_LAO',
        'ngayKTDTriLao': 'NGAY_KT_DTRI_LAO',
        'ketQuaDTriLao': 'KET_QUA_DTRI_LAO',
        'maLydoXNTLVR': 'MA_LY_DO_XN_TLVR',
        'ngayXNTLVR': 'NGAY_XN_TLVR',
        'kqXNTLVR': 'KQ_XN_TLVR',
        'ngayKQXNTLVR': 'NGAY_KQ_XN_TLVR',
        'maLoaiBN': 'MA_LOAI_BN',
        'giaiDoanLamSang': 'GIAI_DOAN_LAM_SANG',
        'nhomDoiTuong': 'NHOM_DOI_TUONG',
        'maTinhTrangDK': 'MA_TINH_TRANG_DK',
        'lanXNPCR': 'LAN_XN_PCR',
        'ngayXNPCR': 'NGAY_XN_PCR',
        'ngayKQXNPCR': 'NGAY_KQ_XN_PCR',
        'maKQXNPCR': 'MA_KQ_XN_PCR',
        'ngayNhanTTMangThai': 'NGAY_NHAN_TT_MANG_THAI',
        'ngayBatDauDTCTX': 'NGAY_BAT_DAU_DT_CTX',
        'maXuTri': 'MA_XU_TRI',
        'ngayBatDauXuTri': 'NGAY_BAT_DAU_XU_TRI',
        'ngayKetThucXuTri': 'NGAY_KET_THUC_XU_TRI',
        'maPhacDoDieuTri': 'MA_PHAC_DO_DIEU_TRI',
        'maBacPhacDo': 'MA_BAC_PHAC_DO',
        'soNgayCapThuocARV': 'SO_NGAY_CAP_THUOC_ARV',
        'ngayChuyenPhacDo': 'NGAY_CHUYEN_PHAC_DO',
        'lyDoChuyenPhacDo': 'LY_DO_CHUYEN_PHAC_DO',
        'thoiGianDinhChi': 'THOI_GIAN_DINH_CHI',
        'tuoiThai': 'TUOI_THAI',
        'nguyenNhanDinhChi': 'NGUYEN_NHAN_DINH_CHI',
        'maBS': 'MA_BS',
        'tenBS': 'TEN_BS',
        'ngayCT': 'NGAY_CT',
        'maCha': 'MA_CHA',
        'maMe': 'MA_ME',
        'maTheTam': 'MA_THE_TAM',
        'hoTenCha': 'HO_TEN_CHA',
        'hoTenMe': 'HO_TEN_ME',
        'soNgayNghi': 'SO_NGAY_NGHI',
        'ngoaiTruTuNgay': 'NGOAI_TRU_TU_NGAY',
        'ngoaiTruDenNgay': 'NGOAI_TRU_DEN_NGAY',
        'donVi': 'DON_VI',
        'tomtatKQ': 'TOMTAT_KQ',
        'ngaySinhCon': 'NGAY_SINH_CON',
        'ngayConChet': 'NGAY_CON_CHET',
        'soConChet': 'SO_CON_CHET',
        'maBHXHNND': 'MA_BHXH_NND',
        'maTheNND': 'MA_THE_NND',
        'hoTenNND': 'HO_TEN_NND',
        'ngaySinhNND': 'NGAY_SINH_NND',
        'maDanTocNND': 'MA_DAN_TOC_NND',
        'soCCCDNND': 'SO_CCCD_NND',
        'ngayCapCCCDNND': 'NGAY_CAP_CCCD_NND',
        'noiCapCCCDNND': 'NOI_CAP_CCCD_NND',
        'noiCuTruNND': 'NOI_CU_TRU_NND',
        'hoTenCon': 'HO_TEN_CON',
        'gioiTinhCon': 'GIOI_TINH_CON',
        'soCon': 'SO_CON',
        'lanSinh': 'LAN_SINH',
        'soConSong': 'SO_CON_SONG',
        'noiSinhCon': 'NOI_SINH_CON',
        'tinhTrangCon': 'TINH_TRANG_CON',
        'sinhConPhauThuat': 'SINH_CON_PHAU_THUAT',
        'sinhConDuoi32Tuan': 'SINH_CON_DUOI_32_TUAN',
        'nguoiDoDe': 'NGUOI_DO_DE',
        'nguoiGhiPhieu': 'NGUOI_GHI_PHIEU',
        'so': 'SO',
        'quyenSo': 'QUYEN_SO',
        'soSeri': 'SO_SERI',
        'soCT': 'SO_CT',
        'soNgay': 'SO_NGAY',
        'tuNgay': 'TU_NGAY',
        'denNgay': 'DEN_NGAY',
        'soKCB': 'SO_KCB',
        'maBHXH': 'MA_BHXH',
        'maDinhChiThai': 'MA_DINH_CHI_THAI',
        'mauSo': 'MAU_SO',
        'nguoiChuTri': 'NGUOI_CHU_TRI',
        'chucVu': 'CHUC_VU',
        'ngayHop': 'NGAY_HOP',
        'ngayCapCCCD': 'NGAY_CAP_CCCD',
        'noiCapCCCD': 'NOI_CAP_CCCD',
        'ngheNghiep': 'NGHE_NGHIEP',
        'maDoiTuong': 'MA_DOI_TUONG',
        'khamGiamDinh': 'KHAM_GIAM_DINH',
        'soBienBan': 'SO_BIEN_BAN',
        'tyLeTTCTCu': 'TY_LE_TTCT_CU',
        'dangHuongCheDo': 'DANG_HUONG_CHE_DO',
        'ngayChungTu': 'NGAY_CHUNG_TU',
        'soGiayGioiThieu': 'SO_GIAY_GIOI_THIEU',
        'ngayDeNghi': 'NGAY_DE_NGHI',
        'maDonVi': 'MA_DON_VI',
        'gioiThieuCua': 'GIOI_THIEU_CUA',
        'ketQuaKham': 'KET_QUA_KHAM',
        'soVanBanCanCu': 'SO_VAN_BAN_CAN_CU',
        'tyLeTTCTMoi': 'TY_LE_TTCT_MOI',
        'tongTyLeTTCT': 'TONG_TY_LE_TTCT',
        'dangKhuyetTat': 'DANG_KHUYET_TAT',
        'mucDoKhuyetTat': 'MUC_DO_KHUYET_TAT',
        'deNghi': 'DE_NGHI',
        'duocXacDinh': 'DUOC_XAC_DINH',
        'soHoSo': 'SO_HO_SO',
        'soChuyenTuyen': 'SO_CHUYEN_TUYEN',
        'qtBenhLy': 'QT_BENH_LY',
        'huongDieuTri': 'HUONG_DIEU_TRI',
        'soPhieu': 'SO_PHIEU',
        'chanDoan': 'CHAN_DOAN',
        'ngayHenKham': 'NGAY_HEN_KHAM'
    };

    // Kiểm tra xem có trong danh sách đặc biệt không
    if (specialCases[fieldName]) {
        return specialCases[fieldName];
    }

    // Chuyển đổi camelCase thành UPPER_SNAKE_CASE
    return fieldName.replace(/([A-Z])/g, '_$1').toUpperCase();
}

/**
 * Cấu hình XML cho từng loại XML
 * Chứa thông tin về cấu trúc XML, tag gốc, tag wrapper và tag item
 */
const CLIENT_SIDE_XML_CONFIG = {
    'XML0': {
        rootTag: 'CHI_TIEU_TRANG_THAI_KCB',
        listWrapperTag: 'DSACH_TRANG_THAI_KCB',
        itemTag: 'TRANG_THAI_KCB',
        isSingleRecord: false
    },
    'XML1': {
        rootTag: 'TONG_HOP',
        itemTag: 'TONG_HOP',
        isSingleRecord: true
    },
    'XML2': {
        rootTag: 'CHITIEU_CHITIET_THUOC',
        listWrapperTag: 'DSACH_CHI_TIET_THUOC',
        itemTag: 'CHI_TIET_THUOC',
        isSingleRecord: false
    },
    'XML3': {
        rootTag: 'CHITIEU_CHITIET_DVKT_VTYT',
        listWrapperTag: 'DSACH_CHI_TIET_DVKT',
        itemTag: 'CHI_TIET_DVKT',
        isSingleRecord: false
    },
    'XML4': {
        rootTag: 'CHITIEU_CHITIET_DICHVUCANLAMSANG',
        listWrapperTag: 'DSACH_CHI_TIET_CLS',
        itemTag: 'CHI_TIET_CLS',
        isSingleRecord: false
    },
    'XML5': {
        rootTag: 'CHITIEU_CHITIET_DIENBIENLAMSANG',
        listWrapperTag: 'DSACH_CHI_TIET_DIEN_BIEN_BENH',
        itemTag: 'CHI_TIET_DIEN_BIEN_BENH',
        isSingleRecord: false
    },
    'XML6': {
        rootTag: 'CHI_TIEU_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS',
        listWrapperTag: 'DSACH_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS',
        itemTag: 'HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS',
        isSingleRecord: false
    },
    'XML7': {
        rootTag: 'CHI_TIEU_DU_LIEU_GIAY_RA_VIEN',
        itemTag: 'CHI_TIEU_DU_LIEU_GIAY_RA_VIEN',
        isSingleRecord: true
    },
    'XML8': {
        rootTag: 'CHI_TIEU_DU_LIEU_TOM_TAT_HO_SO_BENH_AN',
        itemTag: 'CHI_TIEU_DU_LIEU_TOM_TAT_HO_SO_BENH_AN',
        isSingleRecord: true
    },
    'XML9': {
        rootTag: 'CHI_TIEU_DU_LIEU_GIAY_CHUNG_SINH',
        listWrapperTag: 'DSACH_GIAYCHUNGSINH',
        itemTag: 'DU_LIEU_GIAY_CHUNG_SINH',
        isSingleRecord: false
    },
    'XML10': {
        rootTag: 'CHI_TIEU_DU_LIEU_GIAY_NGHI_DUONG_THAI',
        itemTag: 'CHI_TIEU_DU_LIEU_GIAY_NGHI_DUONG_THAI',
        isSingleRecord: true
    },
    'XML11': {
        rootTag: 'CHI_TIEU_DU_LIEU_GIAY_CHUNG_NHAN_NGHI_VIEC_HUONG_BAO_HIEM_XA_HOI',
        itemTag: 'CHI_TIEU_DU_LIEU_GIAY_CHUNG_NHAN_NGHI_VIEC_HUONG_BAO_HIEM_XA_HOI',
        isSingleRecord: true
    },
    'XML12': {
        rootTag: 'CHI_TIEU_DU_LIEU_GIAM_DINH_Y_KHOA',
        itemTag: 'CHI_TIEU_DU_LIEU_GIAM_DINH_Y_KHOA',
        isSingleRecord: true
    },
    'XML13': {
        rootTag: 'CHI_TIEU_GIAYCHUYENTUYEN',
        itemTag: 'CHI_TIEU_GIAYCHUYENTUYEN',
        isSingleRecord: true
    },
    'XML14': {
        rootTag: 'CHI_TIEU_GIAYHEN_KHAMLAI',
        itemTag: 'CHI_TIEU_GIAYHEN_KHAMLAI',
        isSingleRecord: true
    },
    'XML15': {
        rootTag: 'CHI_TIEU_DIEUTRI_BENHLAO',
        listWrapperTag: 'DSACH_CHITIET_DIEUTRI_BENHLAO',
        itemTag: 'CHITIET_DIEUTRI_BENHLAO',
        isSingleRecord: false
    }
};

// Hàm tạo timestamp theo định dạng yyyyMMddHHmmss
function getFormattedTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

// Hàm chuyển đổi dữ liệu từ Tabulator sang chuỗi XML cho XML1-15
function convertXML1to15DataToXML(dataMap) {
    // Tạo đối tượng XML document
    const xmlDoc = document.implementation.createDocument(null, "GIAMDINHHS", null);
    const rootElement = xmlDoc.documentElement;
    
    // Thêm thông tin đơn vị
    const donviElement = xmlDoc.createElement("THONGTINDONVI");
    rootElement.appendChild(donviElement);
    
    const maCSKCB = xmlDoc.createElement("MACSKCB");
    maCSKCB.textContent = document.getElementById("filter_ma_cskcb")?.value || "64020"; // Lấy từ filter hoặc giá trị mặc định
    donviElement.appendChild(maCSKCB);
    
    // Thêm thông tin hồ sơ
    const hosoElement = xmlDoc.createElement("THONGTINHOSO");
    rootElement.appendChild(hosoElement);
    
    const ngayLap = xmlDoc.createElement("NGAYLAP");
    ngayLap.textContent = new Date().toISOString().slice(0, 10).replace(/-/g, "");
    hosoElement.appendChild(ngayLap);
    
    // Tổ chức dữ liệu theo maLK (mã liên kết)
    const hosoByMaLK = {};
    
    // Nhóm dữ liệu theo maLK
    for (const xmlType in dataMap) {
        if (dataMap.hasOwnProperty(xmlType) && dataMap[xmlType].length > 0) {
            dataMap[xmlType].forEach(record => {
                const maLK = record.maLK || "unknown";
                if (!hosoByMaLK[maLK]) {
                    hosoByMaLK[maLK] = {};
                }
                if (!hosoByMaLK[maLK][xmlType]) {
                    hosoByMaLK[maLK][xmlType] = [];
                }
                hosoByMaLK[maLK][xmlType].push(record);
            });
        }
    }
    
    // Đếm số lượng hồ sơ
    const soLuong = xmlDoc.createElement("SOLUONGHOSO");
    soLuong.textContent = Object.keys(hosoByMaLK).length.toString();
    hosoElement.appendChild(soLuong);
    
    const danhSach = xmlDoc.createElement("DANHSACHHOSO");
    hosoElement.appendChild(danhSach);
    
    // Tạo các hồ sơ
    let hosoIndex = 0; // Biến đếm để tạo STT cho XML1
    
    for (const maLK in hosoByMaLK) {
        if (hosoByMaLK.hasOwnProperty(maLK)) {
            const hoSo = xmlDoc.createElement("HOSO");
            danhSach.appendChild(hoSo);
            
            // Thêm từng loại XML vào hồ sơ
            for (const xmlType in hosoByMaLK[maLK]) {
                if (hosoByMaLK[maLK].hasOwnProperty(xmlType)) {
                    const fileHoSo = xmlDoc.createElement("FILEHOSO");
                    hoSo.appendChild(fileHoSo);
                    
                    // Thêm loại hồ sơ
                    const loaiHoSo = xmlDoc.createElement("LOAIHOSO");
                    loaiHoSo.textContent = xmlType;
                    fileHoSo.appendChild(loaiHoSo);
                    
                    // Xử lý đặc biệt cho XML1 để thêm STT tăng dần
                    if (xmlType === 'XML1') {
                        // Đảm bảo mỗi bản ghi XML1 có STT tăng dần
                        hosoByMaLK[maLK][xmlType].forEach(record => {
                            // Thêm hoặc cập nhật STT
                            record.stt = (hosoIndex + 1).toString();
                        });
                        hosoIndex++; // Tăng STT cho hồ sơ tiếp theo
                    }
                    
                    // Tạo nội dung XML cho loại này
                    const xmlContent = createXMLContentForType(xmlType, hosoByMaLK[maLK][xmlType]);
                    
                    // Mã hóa Base64
                    const base64Content = btoa(unescape(encodeURIComponent(xmlContent)));
                    
                    // Thêm nội dung file
                    const noiDungFile = xmlDoc.createElement("NOIDUNGFILE");
                    noiDungFile.textContent = base64Content;
                    fileHoSo.appendChild(noiDungFile);
                }
            }
        }
    }
    
    // Chuyển đổi XML document thành chuỗi
    const serializer = new XMLSerializer();
    return '<?xml version="1.0" encoding="UTF-8"?>\n' + serializer.serializeToString(rootElement);
}

// Hàm chuyển đổi dữ liệu từ Tabulator sang chuỗi XML cho XML0
function convertXML0DataToXML(data) {
    // Tạo đối tượng XML document
    const xmlDoc = document.implementation.createDocument(null, "CHI_TIEU_TRANG_THAI_KCB", null);
    const rootElement = xmlDoc.documentElement;
    
    // Tạo phần wrapper cho danh sách
    const listWrapper = xmlDoc.createElement("DSACH_TRANG_THAI_KCB");
    rootElement.appendChild(listWrapper);
    
    // Danh sách các trường cần loại bỏ
    const excludedFields = ["id", "_id", "ngayTao", "ngayChinhSua", "trangThaiGuiBHXH"];
    
    // Thêm từng record
    data.forEach((row) => {
        const recordElement = xmlDoc.createElement("TRANG_THAI_KCB");
        
        // Thêm các trường dữ liệu
        for (const key in row) {
            if (row.hasOwnProperty(key) && !excludedFields.includes(key)) {
                // Chuyển đổi tên trường từ camelCase sang UPPER_SNAKE_CASE
                const xmlFieldName = fieldNameToXmlTag(key);
                const fieldElement = xmlDoc.createElement(xmlFieldName);
                
                // Xử lý giá trị null hoặc undefined
                const value = row[key] !== null && row[key] !== undefined ? row[key] : "";
                fieldElement.textContent = value;
                recordElement.appendChild(fieldElement);
            }
        }
        
        listWrapper.appendChild(recordElement);
    });
    
    // Chuyển đổi XML document thành chuỗi
    const serializer = new XMLSerializer();
    return '<?xml version="1.0" encoding="UTF-8"?>\n' + serializer.serializeToString(rootElement);
}

// Hàm tạo nội dung XML cho từng loại XML
function createXMLContentForType(xmlType, data) {
    // Cấu hình cho từng loại XML
    const xmlConfig = CLIENT_SIDE_XML_CONFIG[xmlType];
    if (!xmlConfig) {
        console.error(`Không tìm thấy cấu hình cho ${xmlType}`);
        return "";
    }
    
    // Tạo đối tượng XML document
    const xmlDoc = document.implementation.createDocument(null, xmlConfig.rootTag, null);
    const rootElement = xmlDoc.documentElement;
    
    // Danh sách các trường cần loại bỏ
    const excludedFields = ["id", "_id", "ngayTao", "ngayChinhSua", "trangThaiGuiBHXH", "maTinh", "_tempModalId"];
    
    if (xmlConfig.isSingleRecord) {
        // Xử lý cho loại XML chỉ có 1 record (như XML1, XML7, XML8, v.v.)
        if (data.length > 0) {
            const record = data[0];
            
            // Thêm các trường dữ liệu trực tiếp vào root hoặc vào item tag
            if (xmlConfig.itemTag && xmlConfig.itemTag !== xmlConfig.rootTag) {
                const itemElement = xmlDoc.createElement(xmlConfig.itemTag);
                
                // Thêm STT cho XML1 nếu chưa có
                if (xmlType === 'XML1') {
                    const sttElement = xmlDoc.createElement('STT');
                    // Sử dụng STT từ record nếu có, nếu không thì mặc định là 1
                    sttElement.textContent = record.stt || '1';
                    itemElement.appendChild(sttElement);
                }
                
                for (const key in record) {
                    if (record.hasOwnProperty(key) && !excludedFields.includes(key) && key !== 'stt') {
                        const xmlFieldName = fieldNameToXmlTag(key);
                        const fieldElement = xmlDoc.createElement(xmlFieldName);
                        const value = record[key] !== null && record[key] !== undefined ? record[key] : "";
                        fieldElement.textContent = value;
                        itemElement.appendChild(fieldElement);
                    }
                }
                
                rootElement.appendChild(itemElement);
            } else {
                // Thêm STT cho XML1 nếu chưa có
                if (xmlType === 'XML1') {
                    const sttElement = xmlDoc.createElement('STT');
                    // Sử dụng STT từ record nếu có, nếu không thì mặc định là 1
                    sttElement.textContent = record.stt || '1';
                    rootElement.appendChild(sttElement);
                }
                
                for (const key in record) {
                    if (record.hasOwnProperty(key) && !excludedFields.includes(key) && key !== 'stt') {
                        const xmlFieldName = fieldNameToXmlTag(key);
                        const fieldElement = xmlDoc.createElement(xmlFieldName);
                        const value = record[key] !== null && record[key] !== undefined ? record[key] : "";
                        fieldElement.textContent = value;
                        rootElement.appendChild(fieldElement);
                    }
                }
            }
        }
    } else {
        // Xử lý cho loại XML có nhiều record (như XML2, XML3, v.v.)
        // Tạo phần wrapper cho danh sách nếu có
        let listContainer = rootElement;
        if (xmlConfig.listWrapperTag) {
            listContainer = xmlDoc.createElement(xmlConfig.listWrapperTag);
            rootElement.appendChild(listContainer);
        }
        
        // Thêm từng record
        data.forEach((record, index) => {
            const recordElement = xmlDoc.createElement(xmlConfig.itemTag);
            
            // Thêm STT nếu chưa có
            if (!record.hasOwnProperty('stt')) {
                const sttElement = xmlDoc.createElement('STT');
                sttElement.textContent = (index + 1).toString();
                recordElement.appendChild(sttElement);
            } else {
                const sttElement = xmlDoc.createElement('STT');
                sttElement.textContent = record.stt;
                recordElement.appendChild(sttElement);
            }
            
            for (const key in record) {
                if (record.hasOwnProperty(key) && !excludedFields.includes(key) && key !== 'stt') {
                    const xmlFieldName = fieldNameToXmlTag(key);
                    const fieldElement = xmlDoc.createElement(xmlFieldName);
                    const value = record[key] !== null && record[key] !== undefined ? record[key] : "";
                    fieldElement.textContent = value;
                    recordElement.appendChild(fieldElement);
                }
            }
            
            listContainer.appendChild(recordElement);
        });
    }
    
    // Chuyển đổi XML document thành chuỗi
    const serializer = new XMLSerializer();
    return serializer.serializeToString(xmlDoc);
}
// Hàm chuyển đổi từ camelCase sang UPPER_SNAKE_CASE
function camelCaseToUpperSnakeCase(str) {
    // Xử lý các trường hợp đặc biệt
    const specialCases = {
        'maLK': 'MA_LK',
        'maBN': 'MA_BN',
        'hoTen': 'HO_TEN',
        'soCCCD': 'SO_CCCD',
        'ngaySinh': 'NGAY_SINH',
        'gioiTinh': 'GIOI_TINH',
        'maTheBHYT': 'MA_THE_BHYT',
        'maDKBD': 'MA_DKBD',
        'gtTheTu': 'GT_THE_TU',
        'gtTheDen': 'GT_THE_DEN',
        'maDoiTuongKCB': 'MA_DOITUONG_KCB',
        'ngayVao': 'NGAY_VAO',
        'maLoaiKCB': 'MA_LOAI_KCB',
        'maCSKCB': 'MA_CSKCB',
        'maDichVu': 'MA_DICH_VU',
        'tenDichVu': 'TEN_DICH_VU',
        'maThuoc': 'MA_THUOC',
        'tenThuoc': 'TEN_THUOC',
        'maVatTu': 'MA_VAT_TU',
        'tenVatTu': 'TEN_VAT_TU',
        'ngayYL': 'NGAY_YL',
        'duPhong': 'DU_PHONG'
    };
    
    if (specialCases[str]) {
        return specialCases[str];
    }
    
    return str.replace(/([A-Z])/g, '_$1').toUpperCase().replace(/^_/, '');
}

// Hàm tải xuống chuỗi XML dưới dạng file
function downloadXMLFile(xmlString, filename) {
    const blob = new Blob([xmlString], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // Dọn dẹp
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 0);
}

// Hàm lấy dữ liệu từ một bảng Tabulator cụ thể
function getDataFromTabulator(tableId) {
    return new Promise((resolve, reject) => {
        const tableElement = document.getElementById(tableId);
        if (!tableElement || !tableElement._tabulator) {
            console.warn(`Không tìm thấy bảng Tabulator cho ${tableId}`);
            resolve([]);
            return;
        }
        
        const tabulator = tableElement._tabulator;
        
        // Nếu bảng đang sử dụng pagination, cần lấy tất cả dữ liệu
        if (tabulator.options.pagination && tabulator.options.paginationMode === "remote") {
            // Đối với pagination remote, cần gọi API để lấy tất cả dữ liệu
            const xmlType = tableId.replace('-table', '').toUpperCase();
            const csrftoken = getCookie('csrftoken');
            
            $.ajax({
                url: '/xml4750/get_all_data/',
                method: 'POST',
                data: {
                    xml_type: xmlType,
                    filters: JSON.stringify(tabulator.getHeaderFilters() || {})
                },
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        console.error(`Lỗi khi lấy dữ liệu cho ${xmlType}:`, response.message);
                        resolve([]);
                    }
                },
                error: function(xhr, status, error) {
                    console.error(`Lỗi AJAX khi lấy dữ liệu cho ${xmlType}:`, error);
                    resolve([]);
                }
            });
        } else {
            // Đối với pagination local hoặc không có pagination, lấy dữ liệu trực tiếp từ Tabulator
            resolve(tabulator.getData());
        }
    });
}

// Hàm chính để xuất tất cả dữ liệu XML
async function exportAllXMLData() {
    // Hiển thị thông báo đang xử lý
    Swal.fire({
        title: 'Đang xử lý...',
        html: 'Vui lòng đợi trong khi chúng tôi chuẩn bị dữ liệu XML từ tất cả các tab.',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    try {
        // Tạo timestamp cho tên file
        const timestamp = getFormattedTimestamp();
        
        // Lấy dữ liệu từ tab XML0
        const xml0Data = await getDataFromTabulator('xml0-table');
        
        // Lấy dữ liệu từ các tab XML1-XML15
        const allXmlData = {};
        
        for (let i = 1; i <= 15; i++) {
            const xmlType = `XML${i}`;
            const tableId = `xml${i}-table`;
            const data = await getDataFromTabulator(tableId);
            
            if (data && data.length > 0) {
                allXmlData[xmlType] = data;
            }
        }
        
        // Tạo và tải xuống file XML0
        if (xml0Data && xml0Data.length > 0) {
            const xml0String = convertXML0DataToXML(xml0Data);
            downloadXMLFile(xml0String, `XML0_checkin_${timestamp}.xml`);
        }
        
        // Tạo và tải xuống file XML_TONGHOP cho tất cả dữ liệu
        if (Object.keys(allXmlData).length > 0) {
            const xml1to15String = convertXML1to15DataToXML(allXmlData);
            downloadXMLFile(xml1to15String, `XML_Tonghop_${timestamp}.xml`);
        }
        
        // Hiển thị thông báo thành công
        Swal.fire({
            icon: 'success',
            title: 'Thành công',
            html: `
                <p>Dữ liệu XML đã được tạo và tải xuống:</p>
                <ul style="text-align: left; margin-top: 10px;">
                    ${xml0Data && xml0Data.length > 0 ? `<li>XML0_checkin_${timestamp}.xml</li>` : ''}
                    ${Object.keys(allXmlData).length > 0 ? `<li>XML_Tonghop_${timestamp}.xml</li>` : ''}
                </ul>
            `,
        });
    } catch (error) {
        console.error('Lỗi khi xuất dữ liệu XML:', error);
        Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            text: 'Đã xảy ra lỗi khi xuất dữ liệu XML: ' + error.message,
        });
    }
}

// Hàm lấy CSRF token từ cookie
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Khởi tạo sự kiện cho nút xuất XML
document.addEventListener('DOMContentLoaded', function() {
    // Tìm nút xuất XML trong trang
    const exportXmlBtn = document.querySelector('button[title="Xuất XML"]');
    
    if (exportXmlBtn) {
        exportXmlBtn.addEventListener('click', function() {
            exportAllXMLData();
        });
    }
});

/* Thực hiện việc thu thập dữ liệu trên modal và xuất ra xml - FIXED */
function collectAllModalData() {
    const collectedData = {};
    const maLKFromModal = $('#modal_xml1_maLK').val();
    console.log('MaLK from modal:', maLKFromModal);
    if (!maLKFromModal) {
        console.error("Mã liên kết không tìm thấy trong modal.");
        // return null; // Hoặc throw error
    }

    for (let i = 0; i <= 15; i++) {
        const xmlTypeKey = 'XML' + i;
        const xmlTypeLower = xmlTypeKey.toLowerCase();
        let records = [];

        // Các XML dạng bảng trong modal
        if (['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML9', 'XML15'].includes(xmlTypeKey)) {
            const modalTableId = `modal_${xmlTypeLower}-edit-table`;
            const modalTableElement = document.getElementById(modalTableId);
            if (modalTableElement && modalTableElement._tabulator) {
                records = modalTableElement._tabulator.getData();
            }
        } else { // Các XML dạng form trong modal
            const formRecord = { maLK: maLKFromModal }; // Luôn thêm maLK
            let formHasData = false; // Kiểm tra xem form có dữ liệu thực sự không ngoài maLK
            
            // *** SPECIAL HANDLING FOR XML1 BHYT FIELDS ***
            if (xmlTypeKey === 'XML1') {
                console.log('=== Processing XML1 BHYT fields ===');
                
                // Check if we have multiple BHYT cards
                const maTheBHYT = $(`#modal_${xmlTypeLower}_maTheBHYT`).val();
                const hasMultipleCards = maTheBHYT && maTheBHYT.includes(';');
                
                console.log('maTheBHYT:', maTheBHYT);
                console.log('hasMultipleCards:', hasMultipleCards);
                
                if (hasMultipleCards) {
                    // Multiple cards - get data from _text inputs and convert
                    const gtTheTuText = $(`#modal_${xmlTypeLower}_gtTheTu_text`).val();
                    const gtTheDenText = $(`#modal_${xmlTypeLower}_gtTheDen_text`).val();
                    
                    console.log('Text input values:');
                    console.log('- gtTheTuText:', gtTheTuText);
                    console.log('- gtTheDenText:', gtTheDenText);
                    
                    // Convert from dd/MM/yyyy;dd/MM/yyyy to yyyyMMdd;yyyyMMdd
                    const convertedGtTheTu = convertMultipleDatesFromDisplayToYyyyMMdd(gtTheTuText);
                    const convertedGtTheDen = convertMultipleDatesFromDisplayToYyyyMMdd(gtTheDenText);
                    
                    console.log('Converted values:');
                    console.log('- convertedGtTheTu:', convertedGtTheTu);
                    console.log('- convertedGtTheDen:', convertedGtTheDen);
                    
                    // Set the converted values to the main fields
                    if (convertedGtTheTu) {
                        formRecord['gtTheTu'] = convertedGtTheTu;
                        formHasData = true;
                    }
                    if (convertedGtTheDen) {
                        formRecord['gtTheDen'] = convertedGtTheDen;
                        formHasData = true;
                    }
                } else {
                    // Single card - get from main inputs and convert if needed
                    const gtTheTu = $(`#modal_${xmlTypeLower}_gtTheTu`).val();
                    const gtTheDen = $(`#modal_${xmlTypeLower}_gtTheDen`).val();
                    
                    console.log('Single card date inputs:');
                    console.log('- gtTheTu (yyyy-MM-dd):', gtTheTu);
                    console.log('- gtTheDen (yyyy-MM-dd):', gtTheDen);
                    
                    // Convert from yyyy-MM-dd to yyyyMMdd if needed
                    if (gtTheTu) {
                        const convertedGtTheTu = gtTheTu.includes('-') ? convertDateInputToYyyyMMdd(gtTheTu) : gtTheTu;
                        formRecord['gtTheTu'] = convertedGtTheTu;
                        formHasData = true;
                        console.log('- convertedGtTheTu:', convertedGtTheTu);
                    }
                    if (gtTheDen) {
                        const convertedGtTheDen = gtTheDen.includes('-') ? convertDateInputToYyyyMMdd(gtTheDen) : gtTheDen;
                        formRecord['gtTheDen'] = convertedGtTheDen;
                        formHasData = true;
                        console.log('- convertedGtTheDen:', convertedGtTheDen);
                    }
                }
            }
            
            // Process all other fields normally
            $(`#modal-edit-${xmlTypeLower}`).find('input, select, textarea').each(function() {
                const el = $(this);
                const id = el.attr('id');
                if (id && id.startsWith(`modal_${xmlTypeLower}_`)) {
                    const fieldName = id.substring(`modal_${xmlTypeLower}_`.length);
                    
                    // Skip maLK if already processed
                    if (fieldName === 'maLK') return;
                    
                    // *** SKIP _text inputs - they are only for display ***
                    if (fieldName.endsWith('_text')) {
                        console.log(`Skipping _text input: ${fieldName}`);
                        return;
                    }
                    
                    // *** SKIP BHYT fields for XML1 if already processed above ***
                    if (xmlTypeKey === 'XML1' && (fieldName === 'gtTheTu' || fieldName === 'gtTheDen')) {
                        console.log(`Skipping BHYT field ${fieldName} - already processed`);
                        return;
                    }

                    let value = el.val();
                    
                    // Apply field transformers
                    if (fieldTransformers[xmlTypeKey] && fieldTransformers[xmlTypeKey][fieldName] && fieldTransformers[xmlTypeKey][fieldName].fromModal) {
                        console.log(`Applying transformer for ${xmlTypeKey}.${fieldName}, original value:`, value);
                        value = fieldTransformers[xmlTypeKey][fieldName].fromModal(value);
                        console.log(`Transformed value:`, value);
                    }
                    
                    // Set the field value
                    formRecord[fieldName] = (value === undefined || value === null) ? "" : value;
                    if (formRecord[fieldName] !== "") formHasData = true;
                    
                    console.log(`Field ${fieldName}: ${formRecord[fieldName]}`);
                }
            });
            
            // Only add record if has meaningful data or is XML0/XML1 with maLK
            if (formHasData || (maLKFromModal && (xmlTypeKey === 'XML0' || xmlTypeKey === 'XML1'))) {
                records.push(formRecord);
                console.log(`Added form record for ${xmlTypeKey}:`, formRecord);
            } else {
                console.log(`Skipped empty form record for ${xmlTypeKey}`);
            }
        }
        
        if (records.length > 0) {
            collectedData[xmlTypeKey] = records;
        }
    }
    
    console.log('=== Final collected data ===', collectedData);
    return collectedData;
}

// Helper function to convert multiple dates from dd/MM/yyyy;dd/MM/yyyy to yyyyMMdd;yyyyMMdd
function convertMultipleDatesFromDisplayToYyyyMMdd(datesString) {
    if (!datesString) return '';
    
    console.log('convertMultipleDatesFromDisplayToYyyyMMdd input:', datesString);
    
    // Split dates by semicolon
    const dates = datesString.split(';');
    const convertedDates = [];
    
    dates.forEach(dateStr => {
        const trimmedDate = dateStr.trim();
        if (trimmedDate) {
            // Convert from dd/MM/yyyy to yyyyMMdd
            const yyyyMMdd = convertToYyyyMMdd(trimmedDate);
            if (yyyyMMdd) {
                convertedDates.push(yyyyMMdd);
            }
        }
    });
    
    const result = convertedDates.join(';');
    console.log('convertMultipleDatesFromDisplayToYyyyMMdd output:', result);
    
    return result;
}

// Helper function to convert from dd/MM/yyyy to yyyyMMdd
function convertToYyyyMMdd(dateString) {
    if (!dateString) return '';
    
    let date;
    
    // If format is dd/MM/yyyy
    if (dateString.includes('/')) {
        const parts = dateString.split('/');
        if (parts.length === 3) {
            const day = parts[0].padStart(2, '0');
            const month = parts[1].padStart(2, '0');
            const year = parts[2];
            date = new Date(year, month - 1, day);
        }
    }
    // If format is yyyy-MM-dd
    else if (dateString.includes('-')) {
        date = new Date(dateString);
    }
    // If already yyyyMMdd format
    else if (dateString.length === 8 && /^\d{8}$/.test(dateString)) {
        return dateString;
    }
    
    if (date && !isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}${month}${day}`;
    }
    
    return dateString;
}

// Helper function to convert from yyyy-MM-dd to yyyyMMdd
function convertDateInputToYyyyMMdd(dateInputValue) {
    if (!dateInputValue) return '';
    
    const date = new Date(dateInputValue);
    if (isNaN(date.getTime())) return dateInputValue;
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}${month}${day}`;
}

/**
 * Generates the full XML content string for GIAMDINHHS from modal data.
 * @param {Object} modalData - Data collected from the modal, e.g., { XML0: [records], XML1: [records], ... }
 * @returns {string} The complete XML string.
 */
function generateXmlFromModalData(modalData) {
    if (!modalData || Object.keys(modalData).length === 0) {
        console.warn("generateXmlFromModalData: No data provided.");
        return "";
    }

    console.log("generateXmlFromModalData: Processing data:", modalData);

    let xmlString = '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n';
    xmlString += '<GIAMDINHHS>\n';

    // THONGTINDONVI
    xmlString += '  <THONGTINDONVI>\n';
    xmlString += '    <MACSKCB>64020</MACSKCB>\n'; // Mã cơ sở KCB
    xmlString += '  </THONGTINDONVI>\n';

    // THONGTINHOSO
    xmlString += '  <THONGTINHOSO>\n';

    // Ngày lập (current date in yyyyMMdd format)
    const currentDate = new Date();
    const ngayLap = currentDate.getFullYear().toString() +
                   (currentDate.getMonth() + 1).toString().padStart(2, '0') +
                   currentDate.getDate().toString().padStart(2, '0');
    xmlString += `    <NGAYLAP>${ngayLap}</NGAYLAP>\n`;

    // Đếm số hồ sơ (dựa trên XML1 hoặc XML0)
    let soLuongHoSo = 0;
    if (modalData['XML1'] && modalData['XML1'].length > 0) {
        soLuongHoSo = modalData['XML1'].length;
    } else if (modalData['XML0'] && modalData['XML0'].length > 0) {
        soLuongHoSo = modalData['XML0'].length;
    }
    xmlString += `    <SOLUONGHOSO>${soLuongHoSo}</SOLUONGHOSO>\n`;

    xmlString += '    <DANHSACHHOSO>\n';

    // Group data by maLK to create HOSO elements
    const hoSoMap = {};

    // Collect all maLK values
    for (let i = 0; i <= 15; i++) {
        const xmlTypeKey = `XML${i}`;
        if (modalData[xmlTypeKey] && modalData[xmlTypeKey].length > 0) {
            modalData[xmlTypeKey].forEach((record, index) => {
                const maLK = record.maLK || `MALK_${xmlTypeKey}_${index + 1}`;
                if (!hoSoMap[maLK]) {
                    hoSoMap[maLK] = {};
                }
                if (!hoSoMap[maLK][xmlTypeKey]) {
                    hoSoMap[maLK][xmlTypeKey] = [];
                }

                // Ensure STT starts from 1 for XML1
                if (xmlTypeKey === 'XML1') {
                    record.stt = index + 1;
                }

                hoSoMap[maLK][xmlTypeKey].push(record);
            });
        }
    }

    console.log("generateXmlFromModalData: Grouped by maLK:", hoSoMap);

    // Generate HOSO elements
    Object.keys(hoSoMap).forEach(maLK => {
        xmlString += '      <HOSO>\n';

        // Generate FILEHOSO for each XML type in this HOSO
        for (let i = 0; i <= 15; i++) {
            const xmlTypeKey = `XML${i}`;
            if (hoSoMap[maLK][xmlTypeKey] && hoSoMap[maLK][xmlTypeKey].length > 0) {

                // Check if data should be exported
                const shouldExport = shouldExportXMLType(xmlTypeKey, hoSoMap[maLK][xmlTypeKey]);

                if (shouldExport) {
                    xmlString += '        <FILEHOSO>\n';
                    xmlString += `          <LOAIHOSO>${xmlTypeKey}</LOAIHOSO>\n`;
                    xmlString += '          <NOIDUNGFILE>';

                    // Generate inner XML content
                    const innerXmlContent = createXMLContentForType(xmlTypeKey, hoSoMap[maLK][xmlTypeKey]);

                    // Base64 encode the inner XML content
                    const base64EncodedContent = btoa(unescape(encodeURIComponent(innerXmlContent)));
                    xmlString += base64EncodedContent;

                    xmlString += '</NOIDUNGFILE>\n';
                    xmlString += '        </FILEHOSO>\n';
                } else {
                    console.log(`Skipping ${xmlTypeKey} for maLK ${maLK} - insufficient data`);
                }
            }
        }

        xmlString += '      </HOSO>\n';
    });

    xmlString += '    </DANHSACHHOSO>\n';
    xmlString += '  </THONGTINHOSO>\n';
    xmlString += '</GIAMDINHHS>';

    console.log("generateXmlFromModalData: Generated XML structure");
    return xmlString;
}

/**
 * Check if XML type should be exported based on data content
 * @param {string} xmlType - XML type (XML0, XML1, etc.)
 * @param {Array} data - Array of records for this XML type
 * @returns {boolean} - True if should export, false otherwise
 */
function shouldExportXMLType(xmlType, data) {
    if (!data || data.length === 0) {
        return false;
    }

    // Tabulator XML types - always export if has data
    const tabulatorTypes = ['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML9', 'XML15'];
    if (tabulatorTypes.includes(xmlType)) {
        return true;
    }

    // Form-based XML types - check for meaningful data
    const formTypes = ['XML0', 'XML1', 'XML7', 'XML8', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14'];
    if (formTypes.includes(xmlType)) {
        for (const record of data) {
            // Count non-empty fields excluding maLK
            let meaningfulFieldCount = 0;

            for (const [key, value] of Object.entries(record)) {
                if (key !== 'maLK' && value !== null && value !== undefined && value !== '') {
                    meaningfulFieldCount++;
                }
            }

            // Special case for XML11: need at least mauSo besides maLK
            if (xmlType === 'XML11') {
                const soChungTu = record.soCT && record.soCT.trim() !== '';
                if (!soChungTu) {
                    console.log(`XML11 skipped - no mauSo field for maLK: ${record.maLK}`);
                    continue;
                }
                // For XML11, if has soCT, it's valid
                return true;
            }

            // For other form types, need at least 1 meaningful field besides maLK
            if (meaningfulFieldCount > 0) {
                return true;
            }
        }

        console.log(`${xmlType} skipped - only maLK field present or no meaningful data`);
        return false;
    }

    // Default: export if has data
    return true;
}