from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from .models import Department, Position
from .forms import DepartmentForm, PositionForm
import json
from django.views.decorators.http import require_POST, require_GET

@login_required
def department_list(request):
    """View for listing all departments"""
    return render(request, 'departments/department_list.html')

@login_required
def department_data_api(request):
    departments = Department.objects.all().values('id', 'name', 'code', 'description', 'is_used')
    return JsonResponse(list(departments), safe=False)

@login_required
def department_create_api(request):
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        data = json.loads(request.body)
        department = Department.objects.create(
            name=data['name'],
            code=data['code'],
            description=data.get('description', ''),
            is_used=data.get('is_used', False)
        )
        return JsonResponse({
            'status': 'success',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'description': department.description,
                'is_used': department.is_used
            }
        })
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def department_edit_api(request, pk):
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
        
    try:
        data = json.loads(request.body)
        department = get_object_or_404(Department, pk=pk)

        department.name = data.get('name', department.name)
        department.code = data.get('code', department.code)
        department.description = data.get('description', department.description)
        department.is_used = data.get('is_used', department.is_used)
        department.save()

        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def department_delete_api(request, pk):
    """API endpoint for deleting a department"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        department = get_object_or_404(Department, pk=pk)
        department.delete()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def department_toggle_api(request, pk):
    """API endpoint for toggling the 'is_used' status of a department"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        department = get_object_or_404(Department, pk=pk)
        department.is_used = not department.is_used
        department.save()
        return JsonResponse({'status': 'success', 'is_used': department.is_used})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def position_list(request):
    """Đi đến trang quản lý chức vụ"""
    return render(request, 'departments/position_list.html')

@login_required
def position_list_data_api(request):
    """API endpoint for listing all positions"""
    positions = Position.objects.all().values('id', 'name', 'code', 'description', 'is_used')
    return JsonResponse(list(positions), safe=False)

@login_required
def position_create_api(request):
    """API endpoint for creating a new position"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        data = json.loads(request.body)
        position = Position.objects.create(
            name=data['name'],
            code=data['code'],
            description=data.get('description', ''),
            is_used=data.get('is_used', False)
        )
        return JsonResponse({
            'status': 'success',
            'position': {
                'id': position.id,
                'name': position.name,
                'code': position.code,
                'description': position.description,
                'is_used': position.is_used
            }
        })
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
def position_edit_api(request, pk):
    """API endpoint for editing a position"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
        
    try:
        data = json.loads(request.body)
        position = get_object_or_404(Position, pk=pk)

        position.name = data.get('name', position.name)
        position.code = data.get('code', position.code)
        position.description = data.get('description', position.description)
        position.is_used = data.get('is_used', position.is_used)
        position.save()

        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})
    
@login_required
def position_delete_api(request, pk):
    """API endpoint for deleting a position"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        position = get_object_or_404(Position, pk=pk)
        position.delete()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def position_toggle_api(request, pk):
    """API endpoint for toggling the 'is_used' status of a position"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        position = get_object_or_404(Position, pk=pk)
        position.is_used = not position.is_used
        position.save()
        return JsonResponse({'status': 'success', 'is_used': position.is_used})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

# Dictionary ánh xạ tên danh mục với model tương ứng
CATEGORY_MODEL_MAP = {
    'department': Department,
    'posittion': Position,
}

@login_required
@require_GET
def api_get_category_data(request):
    """
    API endpoint chung để lấy dữ liệu từ các model danh mục

    Tham số:
        category: Tên danh mục (ví dụ: department, posittion, ...)
        active_only: Chỉ lấy các mục có hiệu lực (mặc định: True)
        format: Định dạng label ('name', 'id')

    Trả về:
        Danh sách các mục dưới dạng JSON với cấu trúc:
        {
            "success": true,
            "data": [
                {"value": "id", "label": "name"},
                ...
            ]
        }
    """
    try:
        # Lấy tên danh mục từ tham số
        category = request.GET.get('category')
        active_only = request.GET.get('active_only', 'true').lower() == 'true'

        # Kiểm tra xem danh mục có tồn tại không
        if not category or category not in CATEGORY_MODEL_MAP:
            return JsonResponse({
                'success': False,
                'error': f'Danh mục không hợp lệ. Các danh mục hợp lệ: {", ".join(CATEGORY_MODEL_MAP.keys())}'
            }, status=400)

        # Lấy model tương ứng với danh mục
        model = CATEGORY_MODEL_MAP[category]

        # Lấy dữ liệu từ model
        queryset = model.objects.all().order_by('id')

        # Lọc theo trạng thái hiệu lực nếu cần
        if active_only:
            queryset = queryset.filter(hieu_luc=True)

        # Chuyển đổi thành danh sách các dict với mã và tên/diễn giải tùy theo format
        result = []
        for item in queryset:
            label = f"{item.id} - {item.name}"

            result.append({
                'value': item.id,  # Giá trị sẽ được lưu vào database
                'label': label     # Nhãn hiển thị trong dropdown
            })

        return JsonResponse({'success': True, 'data': result})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)