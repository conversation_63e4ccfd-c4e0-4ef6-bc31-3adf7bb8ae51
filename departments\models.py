from django.db import models
from django.utils.translation import gettext_lazy as _

class Department(models.Model):
    """Model for hospital departments"""
    name = models.CharField(max_length=100, verbose_name=_('Tên khoa/phòng ban'))
    code = models.CharField(max_length=20, unique=True, verbose_name=_('Mã khoa/phòng ban'))
    description = models.TextField(blank=True, null=True, verbose_name=_('Mô tả'))
    is_used = models.BooleanField(default=True, verbose_name=_('Sử dụng'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('<PERSON><PERSON><PERSON> tạo'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('Cập nhật lần cuối'))

    class Meta:
        verbose_name = _('Khoa/Phòng ban')
        verbose_name_plural = _('Khoa/Phòng ban')
        ordering = ['name']

    def __str__(self):
        return self.name

class Position(models.Model):
    """Model for staff positions"""
    name = models.CharField(max_length=100, verbose_name=_('T<PERSON><PERSON> chức vụ'))
    code = models.CharField(max_length=20, unique=True, verbose_name=_('Mã chức vụ'))
    description = models.TextField(blank=True, null=True, verbose_name=_('Mô tả'))
    is_used = models.BooleanField(default=True, verbose_name=_('Sử dụng'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('Ngày tạo'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('Cập nhật lần cuối'))

    class Meta:
        verbose_name = _('Chức vụ')
        verbose_name_plural = _('Chức vụ')
        ordering = ['name']

    def __str__(self):
        return self.name
