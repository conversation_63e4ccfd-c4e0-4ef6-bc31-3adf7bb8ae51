from django.urls import path
from . import views

app_name = 'departments'

urlpatterns = [
    # Department URLs
    path('', views.department_list, name='department_list'),
    path('api/departments/data/', views.department_data_api, name='department_data_api'),
    path('api/departments/create/', views.department_create_api, name='department_create_api'),
    path('api/departments/<int:pk>/edit/', views.department_edit_api, name='department_edit_api'),
    path('api/departments/<int:pk>/delete/', views.department_delete_api, name='department_delete_api'),
    path('api/departments/<int:pk>/toggle/', views.department_toggle_api, name='department_toggle_api'),

    # Position URLs
    path('positions/', views.position_list, name='position_list'),
    path('positions/api/data/', views.position_list_data_api, name='position_list_data_api'),
    path('positions/api/create/', views.position_create_api, name='position_create_api'),
    path('positions/api/<int:pk>/edit/', views.position_edit_api, name='position_edit_api'),
    path('positions/api/<int:pk>/delete/', views.position_delete_api, name='position_delete_api'),
    path('positions/api/<int:pk>/toggle/', views.position_toggle_api, name='position_toggle_api'),

    # API endpoint để load category
    path('api/get-user-category-data/', views.api_get_category_data, name='api_get_category_data'),
]
