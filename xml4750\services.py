from datetime import datetime
from collections import defaultdict
from openpyxl.styles import Align<PERSON>, Font, Border, Side

from danhmucbv.models import Thuoc, VatTuYTe
from .models import XML1Model, XML2Model, XML3Model

# Báo cáo 3660
# Header cho báo cáo 3360
bc_3360_header = [
    "STT", "MA_BN", "HO_TEN", "NGAY_SINH", "GIOI_TINH", "DIA_CHI", "MA_THE", "MA_DKBD",
    "GT_THE_TU", "GT_THE_DEN", "MA_BENH", "MA_BENHKHAC", "MA_LYDO_VVIEN", "MA_NOI_CHUYEN",
    "NGAY_VAO", "NGAY_RA", "SO_NGAY_DTRI", "KET_QUA_DTRI", "TINH_TRANG_RV", "T_TONGCHI", 
    "T_XN", "T_<PERSON><PERSON>", "T_THUOC", "T_MAU", "T_PTTT", "T_VTYT", "T_DVKT_TYLE", "T_THUOC_TYLE", 
    "T_VTYT_TYLE", "T_KHAM", "T_GIUONG", "T_VCHUYEN", "T_BNTT", "T_BHTT", "T_NGOAIDS", 
    "MA_KHOA", "NAM_QT", "THANG_QT", "MA_KHUVUC", "MA_LOAIKCB", "MA_CSKCB", "T_NGUONKHAC"
]

# Header cho báo cáo 19,20,21
bc_19_header = ['stt','ma_vtyt','ten_vtyt','ten_thuongmai','quy_cach','don_vi','gia_mua','sl_noitru','sl_ngoaitru',	'gia_thanhtoan', 'thanh_tien']
bc_20_header = ['stt','ma_thuoc','ten_hoatchat','ten_thuoc','duong_dung','ham_luong','so_dky','don_vi','sl_noitru','sl_ngoaitru','don_gia','thanh_tien']
bc_21_header = ['stt','ma_dvkt','ten_dvkt','sl_noitru','sl_ngoaitru','don_gia','thanh_tien']

key_loai_kcb_ngoai_tru = ["01", "07"]
key_loai_kcb_dieu_tri_ngoai_tru = ["02", "05", "06", "08"]
key_loai_kcb_noi_tru = ["03", "04", "09"]

# Cấu hình dành cho excel
bold_font = Font(size=12, bold=True)
bold_font_large = Font(size=14, bold=True)
italic_font = Font(size=12, italic=True)
regular_font = Font(size=12, bold=False)

center_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
left_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)

# Định nghĩa border một lần
thin_border = Border(left=Side(style='thin'),
                            right=Side(style='thin'),
                            top=Side(style='thin'),
                            bottom=Side(style='thin'))

# Hàm lấy dữ liệu từ XML1, XML2, XML3 và chuyển đổi thành định dạng báo cáo 3360
# Dữ liệu đầu vào là khoảng thời gian và đối tượng, trả về dict chứa danh sách các bản ghi từ XML1, XML2, XML3
# Trả về dict chứa danh sách các bản ghi từ XML1, XML2, XML3
def get_bao_cao_3360_data(ngay_bat_dau, ngay_ket_thuc, doi_tuong):
    """
    Lấy dữ liệu từ XML1, XML2, XML3 theo khoảng thời gian và đối tượng, trả về dict.
    """
    query = XML1Model.objects.filter(ngayTToan__range=[ngay_bat_dau, ngay_ket_thuc])

    if doi_tuong == 1:
        query = query.exclude(maTinh=97)  # BHXH VN
    elif doi_tuong == 2:
        query = query.filter(maTinh=97)   # BHXH BQP

    xml1_records = list(query)
    # Lấy danh sách maLK và ngayTao từ XML1
    maLKs, ngay_tao = zip(*[(x.maLK, x.ngayTao) for x in xml1_records]) if xml1_records else ([], [])

    # Từ dữ liệu maLKs và ngay_tạo, lấy dữ liệu từ XML2 và XML3 dựa theo maLKs và ngay_tao của XML1
    xml2_records = XML2Model.objects.filter(maLK__in=maLKs, ngayTao__in=ngay_tao) if maLKs else []
    xml3_records = XML3Model.objects.filter(maLK__in=maLKs, ngayTao__in=ngay_tao) if maLKs else []

    return {
        "xml1": xml1_records,
        "xml2": xml2_records,
        "xml3": xml3_records
    }

# Hàm chuyển đổi dữ liệu XML sang định dạng báo cáo 3360
# Dữ liệu đầu vào là dict chứa danh sách các bản ghi từ XML1, XML2, XML3
# Trả về danh sách các dict tương ứng với từng dòng báo cáo
# Mỗi dict chứa các thông tin cần thiết cho mỗi dòng báo cáo
def convert_xml_to_3360(xml_data: dict) -> list[dict]:
    xml1_list = xml_data.get("xml1", [])
    xml2_list = xml_data.get("xml2", [])
    xml3_list = xml_data.get("xml3", [])

    # Nhóm dữ liệu XML2, XML3 theo maLK
    xml2_dict = defaultdict(list)
    for item in xml2_list:
        xml2_dict[item.maLK].append(item)

    xml3_dict = defaultdict(list)
    for item in xml3_list:
        xml3_dict[item.maLK].append(item)

    # Các nhóm mã
    xml2_thuoc = {4, 5}
    xml2_thuoc_tyle = {6}
    xml2_mau = {7, 17}

    xml3_xn = {1}
    xml3_cdha = {2, 3}
    xml3_pttt = {8, 18}
    xml3_dvkt_tyle = {9}
    xml3_vtyt = {10}
    xml3_vtyt_tyle = {11}
    xml3_vanchuyen = {12}
    xml3_kham = {13}
    xml3_giuong = {14, 15, 16}

    ds_bc = []
    stt = 1
    for xml1 in xml1_list:
        maLK = xml1.maLK
        xml2s = xml2_dict.get(maLK, [])
        xml3s = xml3_dict.get(maLK, [])

        # Tổng tiền xml2
        tong_thuoc = sum(x.thanhTienBH for x in xml2s if x.maNhom in xml2_thuoc)
        tong_thuoc_tyle = sum(x.thanhTienBH for x in xml2s if x.maNhom in xml2_thuoc_tyle)
        tong_mau = sum(x.thanhTienBH for x in xml2s if x.maNhom in xml2_mau)

        # Tổng tiền xml3
        tong_xn = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_xn)
        tong_cdha = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_cdha)
        tong_pttt = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_pttt)
        tong_dvkt_tyle = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_dvkt_tyle)
        tong_vtyt = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_vtyt)
        tong_vtyt_tyle = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_vtyt_tyle)
        tien_kham = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_kham)
        tien_giuong = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_giuong)
        tien_vanchuyen = sum(x.thanhTienBH for x in xml3s if x.maNhom in xml3_vanchuyen)

        # Tạo dict cho mỗi dòng báo cáo
        ds_bc.append({
            "stt": stt,
            "ma_bn": xml1.maBN,
            "ho_ten": xml1.hoTen,
            "ngay_sinh": xml1.ngaySinh[:8] if xml1.ngaySinh and len(xml1.ngaySinh) > 4 else xml1.ngaySinh,
            "gioi_tinh": xml1.gioiTinh,
            "dia_chi": xml1.diaChi,
            "ma_the": xml1.maTheBHYT[:15],
            "ma_dkbd": xml1.maDKBD[:5],
            "gt_the_tu": xml1.gtTheTu[:10],
            "gt_the_den": xml1.gtTheDen[:10],
            "ma_benh": xml1.maBenhChinh,
            "ma_benhkhac": xml1.maBenhKT,
            "ma_lydo_vvien": xml1.maDoiTuongKCB[:1] if xml1.maDoiTuongKCB else "",
            "ma_noi_chuyen": xml1.maNoiDen,
            "ngay_vao": xml1.ngayVao,
            "ngay_ra": xml1.ngayRa,
            "so_ngay_dtri": xml1.soNgayDtri,
            "ket_qua_dtri": xml1.ketQuaDtri,
            "tinh_trang_rv": xml1.maLoaiRV,
            "t_tongchi": xml1.tTongChiBH,
            "t_xn": tong_xn,
            "t_cdha": tong_cdha,
            "t_thuoc": tong_thuoc,
            "t_mau": tong_mau,
            "t_pttt": tong_pttt,
            "t_vtyt": tong_vtyt,
            "t_dvkt_tyle": tong_dvkt_tyle,
            "t_thuoc_tyle": tong_thuoc_tyle,
            "t_vtyt_tyle": tong_vtyt_tyle,
            "t_kham": tien_kham,
            "t_giuong": tien_giuong,
            "t_vchuyen": tien_vanchuyen,
            "t_bntt": xml1.tBNTT,
            "t_bncct": xml1.tBNCCT,
            "t_bntt_total": xml1.tBNCCT + xml1.tBNTT,
            "t_bhtt": xml1.tBHTT,
            "t_ngoaids": 0,
            "ma_khoa": xml1.maKhoa,
            "nam_qt": xml1.namQT,
            "thang_qt": xml1.thangQT,
            "ma_khuvuc": xml1.maKhuVuc,
            "ma_loaikcb": xml1.maLoaiKCB,
            "ma_cskcb": xml1.maCSKCB,
            "t_nguonkhac": xml1.tNguonKhac,
        })
        stt += 1

    return ds_bc

# Báo cáo 102
def bc_102_content_generate(lst, r, worksheet):
    place = r
    r = r + 1
    _from = r
    _to = r + len(lst) - 1
    count = 1
    
    if lst: # nếu list có giá trị
        for bc in lst:
            worksheet[f"A{r}"] = count
            worksheet[f"B{r}"] = bc['ho_ten']
            worksheet[f"C{r}"] = bc['ngay_sinh'] if len(bc['ngay_sinh']) == 4 else datetime.strptime(bc['ngay_sinh'], "%Y%m%d").strftime("%d/%m/%Y")
            worksheet[f"D{r}"] = bc['gioi_tinh']
            worksheet[f"E{r}"] = bc['ma_the'][:15]
            worksheet[f"F{r}"] = bc['ma_benh']
            worksheet[f"G{r}"] = datetime.strptime(bc['ngay_vao'], "%Y%m%d%H%M").strftime("%d/%m/%Y")
            worksheet[f"H{r}"] = datetime.strptime(bc['ngay_ra'], "%Y%m%d%H%M").strftime("%d/%m/%Y")
            worksheet[f"I{r}"] = bc['so_ngay_dtri']
            worksheet[f"J{r}"] = bc['t_tongchi']
            worksheet[f"K{r}"] = bc['t_kham']
            worksheet[f"L{r}"] = bc['t_giuong']
            worksheet[f"M{r}"] = bc['t_xn']
            worksheet[f"N{r}"] = bc['t_cdha']
            worksheet[f"O{r}"] = bc['t_pttt']
            worksheet[f"P{r}"] = bc['t_mau']
            worksheet[f"Q{r}"] = bc['t_thuoc'] + bc['t_thuoc_tyle']
            worksheet[f"R{r}"] = bc['t_vtyt'] + bc['t_vtyt_tyle']
            worksheet[f"S{r}"] = bc['t_vchuyen']
            worksheet[f"T{r}"] = bc['t_bhtt']
            worksheet[f"U{r}"] = 0  # Tại trung ương
            worksheet[f"V{r}"] = 0  # Nghị định 70
            worksheet[f"W{r}"] = bc['t_bncct']
            worksheet[f"X{r}"] = bc['t_bntt']
            worksheet[f"Y{r}"] = bc['t_ngoaids']
            worksheet[f"Z{r}"] = 0  # Hỗ trợ tài trợ
            worksheet[f"AA{r}"] = 0  # Ngoài phạm vi BHYT
            count += 1
            r += 1
    
        # Thêm công thức
        for col in ['I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA']:
            worksheet[f"{col}{place}"] = f"=SUM({col}{_from}:{col}{_to})"
    else:  # Nếu lst rỗng, gán các ô bằng 0
        for col in ['I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA']:
            worksheet[f"{col}{place}"] = 0

    # Thiết lập font size
    for row in worksheet[f"A{_from}:AA{_to}"]:
        for cell in row:
            cell.font = Font(size=10)

def bc_102_header_generate(worksheet, header_start, header_group, header_group_ct, header_number, thoi_gian_title):
    # Thiết lập tiêu đề
    worksheet["A1"] = "Tên cơ sở KCB"
    worksheet["A2"] = "Mã số"
    worksheet["A4"] = "TỔNG HỢP CHI PHÍ KHÁM BỆNH, CHỮA BỆNH NGƯỜI THAM GIA BẢO HIỂM Y TẾ"
    worksheet["A4"].font = Font(size=16)
    worksheet["A5"] = thoi_gian_title
    worksheet["A5"].font = Font(size=14)
    worksheet["C1"] = "Bệnh viện Quân y 211"
    worksheet["C2"] = "64020"
    worksheet["V1"] = "Mẫu số C79-HD"
    worksheet["V1"].font = Font(size=10)
    worksheet["V2"] = "(Ban hành theo thông tư 102/2018/TT-BYT ngày 14 tháng 11 năm 2018 của Bộ Tài chính)"
    worksheet["V2"].font = Font(size=9)
    worksheet["V2"].alignment = Alignment(wrap_text=True)

    # Headers
    worksheet[f"A{header_start}"] = "STT"
    worksheet[f"B{header_start}"] = "Họ và tên"
    worksheet[f"C{header_start}"] = "Năm sinh"
    worksheet[f"D{header_start}"] = "Giới tính"
    worksheet[f"E{header_start}"] = "Mã thẻ BHYT"
    worksheet[f"F{header_start}"] = "Mã bệnh"
    worksheet[f"G{header_start}"] = "Ngày vào"
    worksheet[f"H{header_start}"] = "Ngày ra"
    worksheet[f"I{header_start}"] = "Số ngày điều trị"
    worksheet[f"J{header_start}"] = "CHI PHÍ TRONG PHẠM VI BHYT"
    worksheet[f"AA{header_start}"] = "Chi phí ngoài phạm vi BHYT"

    # Group chi phí
    worksheet[f"J{header_group}"] = "Tổng cộng"
    worksheet[f"K{header_group}"] = "Theo nhóm chi phí"
    worksheet[f"T{header_group}"] = "Quỹ BHYT trả"
    worksheet[f"W{header_group}"] = "Người bệnh"
    worksheet[f"Y{header_group}"] = "Nguồn khác"

    # Group chi tiết
    worksheet[f"K{header_group_ct}"] = "Khám bệnh"
    worksheet[f"L{header_group_ct}"] = "Ngày giường"
    worksheet[f"M{header_group_ct}"] = "Xét nghiệm"
    worksheet[f"N{header_group_ct}"] = "CĐHA TDCN"
    worksheet[f"O{header_group_ct}"] = "Thủ thuật, Phẫu thuật"
    worksheet[f"P{header_group_ct}"] = "Máu"
    worksheet[f"Q{header_group_ct}"] = "Thuốc dịch"
    worksheet[f"R{header_group_ct}"] = "VTYT"
    worksheet[f"S{header_group_ct}"] = "Vận chuyển"
    worksheet[f"T{header_group_ct}"] = "Tại tỉnh, thành phố"
    worksheet[f"U{header_group_ct}"] = "Tại Trung ương"
    worksheet[f"V{header_group_ct}"] = "NĐ 70"
    worksheet[f"W{header_group_ct}"] = "Cùng chi trả"
    worksheet[f"X{header_group_ct}"] = "Tự trả"
    worksheet[f"Y{header_group_ct}"] = "NSĐP"
    worksheet[f"Z{header_group_ct}"] = "Hỗ trợ, tài trợ"

    # Header number
    worksheet[f"A{header_number}"] = "A"
    worksheet[f"B{header_number}"] = "B"
    worksheet[f"C{header_number}"] = "C"
    worksheet[f"D{header_number}"] = "D"
    worksheet[f"E{header_number}"] = "E"
    worksheet[f"F{header_number}"] = "G"
    worksheet[f"G{header_number}"] = "H"
    worksheet[f"H{header_number}"] = "I"
    worksheet[f"I{header_number}"] = "K"
    header_cols = ["J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "AA"]
    for idx, col in enumerate(header_cols, start=1):
        worksheet[f"{col}{header_number}"] = idx

    # Căn giữa tất cả các headers
    for row in worksheet[f"A{header_start}:AA{header_number}"]:
        for cell in row:
            cell.alignment = Alignment(horizontal="center", vertical="center")

def bc_102_filter_groups(lst_bc7980a, maCSKCB, maTinh):
    # Danh sách các bệnh nhân đăng kí ban đầu ngoại trú
    lstDKBDNg = [
        x for x in lst_bc7980a
        if (x['ma_dkbd'] == maCSKCB) and
        (x['ma_loaikcb'] in key_loai_kcb_ngoai_tru or x['ma_loaikcb'] in key_loai_kcb_dieu_tri_ngoai_tru) and
        (x['ma_the'][3:5] == maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân nội tỉnh đến ngoại trú
    lstNoiTinhDenNg = [
        x for x in lst_bc7980a
        if (x['ma_dkbd'] != maCSKCB) and
        (x['ma_loaikcb'] in key_loai_kcb_ngoai_tru or x['ma_loaikcb'] in key_loai_kcb_dieu_tri_ngoai_tru) and
        (x['ma_the'][3:5] == maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân đa tuyến đến ngoại tỉnh ngoại trú
    lstNgoaiTinhDenNg = [
        x for x in lst_bc7980a
        if (x['ma_loaikcb'] in key_loai_kcb_ngoai_tru or x['ma_loaikcb'] in key_loai_kcb_dieu_tri_ngoai_tru) and
        (x['ma_the'][3:5] != maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân theo nghị định 70 ngoại trú
    lstND70Ng = [
        x for x in lst_bc7980a
        if (x['ma_loaikcb'] in key_loai_kcb_ngoai_tru or x['ma_loaikcb'] in key_loai_kcb_dieu_tri_ngoai_tru) and
        (x['ma_the'][3:5] != maTinh) and
        (x['ma_the'][0:2] in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân đăng kí ban đầu Nội trú
    lstDKBDNoi = [
        x for x in lst_bc7980a
        if (x['ma_dkbd'] == maCSKCB) and
        (x['ma_loaikcb'] in key_loai_kcb_noi_tru) and
        (x['ma_the'][3:5] == maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân nội tỉnh đến Nội trú
    lstNoiTinhDenNoi = [
        x for x in lst_bc7980a
        if (x['ma_dkbd'] != maCSKCB) and
        (x['ma_loaikcb'] in key_loai_kcb_noi_tru) and
        (x['ma_the'][3:5] == maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân đa tuyến đến ngoại tỉnh Nội trú
    lstNgoaiTinhDenNoi = [
        x for x in lst_bc7980a
        if (x['ma_loaikcb'] in key_loai_kcb_noi_tru) and
        (x['ma_the'][3:5] != maTinh) and
        (x['ma_the'][0:2] not in ["QN", "CA", "CY"])
    ]

    # Danh sách các bệnh nhân theo nghị định 70 Nội trú
    lstND70Noi = [
        x for x in lst_bc7980a
        if (x['ma_loaikcb'] in key_loai_kcb_noi_tru) and
        (x['ma_the'][3:5] != maTinh) and
        (x['ma_the'][0:2] in ["QN", "CA", "CY"])
    ]

    return {
        "lstDKBDNg": lstDKBDNg,
        "lstNoiTinhDenNg": lstNoiTinhDenNg,
        "lstNgoaiTinhDenNg": lstNgoaiTinhDenNg,
        "lstND70Ng": lstND70Ng,
        "lstDKBDNoi": lstDKBDNoi,
        "lstNoiTinhDenNoi": lstNoiTinhDenNoi,
        "lstNgoaiTinhDenNoi": lstNgoaiTinhDenNoi,
        "lstND70Noi": lstND70Noi
    }

def bc_102_ngoai_tru(worksheet, rA, ds_dkbd_ngoai, ds_noi_tinh_den, ds_ngoai_tinh_den, ds_nghi_dinh_70):
    # Bắt đầu với phần điều trị ngoại trú
    # Đối tượng theo nghị định 146 đăng kí ban đầu tại bệnh viện ngoại trú
    # rA = headerNumber + 1  # dòng khám, chữa bệnh ngoại trú
    rA1 = rA + 1           # A.1: Đối tượng theo nghị định 146
    rA1I = rA1 + 1         # I. Người bệnh đăng kí ban đầu tại cơ sở KCB

    worksheet[f"A{rA}"] = "A. KHÁM, CHỮA BỆNH NGOẠI TRÚ"
    worksheet[f"A{rA1}"] = "A.1"
    worksheet[f"B{rA1}"] = "ĐỐI TƯỢNG THEO NGHỊ ĐỊNH 146"
    worksheet[f"A{rA1I}"] = "I"
    worksheet[f"B{rA1I}"] = f"NGƯỜI BỆNH ĐKBĐ TẠI CƠ SỞ KCB: {len(ds_dkbd_ngoai)} LƯỢT"
    bc_102_content_generate(ds_dkbd_ngoai, rA1I, worksheet)

    # Danh sách các bệnh nhân đa tuyến đến nội tỉnh ngoại trú
    rA1II = rA1I + len(ds_dkbd_ngoai) + 1
    worksheet[f"A{rA1II}"] = "II"
    worksheet[f"B{rA1II}"] = f"NGƯỜI BỆNH ĐKBĐ TẠI CƠ SỞ KCB: {len(ds_noi_tinh_den)} LƯỢT"
    bc_102_content_generate(ds_noi_tinh_den, rA1II, worksheet)

    # Danh sách các bệnh nhân đa tuyến đến ngoại tỉnh ngoại trú
    rA1III = rA1II + len(ds_noi_tinh_den) + 1
    worksheet[f"A{rA1III}"] = "III"
    worksheet[f"B{rA1III}"] = f"BỆNH NHÂN NGOẠI TỈNH ĐẾN: {len(ds_ngoai_tinh_den)} LƯỢT"
    bc_102_content_generate(ds_ngoai_tinh_den, rA1III, worksheet)

    # Đối tượng theo nghị định 70 ngoại trú
    rA2 = rA1III + len(ds_ngoai_tinh_den) + 1
    rA2III = rA2 + 1
    worksheet[f"A{rA2}"] = "A2."
    worksheet[f"B{rA2}"] = "ĐỐI TƯỢNG THEO NGHỊ ĐỊNH 70"
    worksheet[f"A{rA2III}"] = "III."
    worksheet[f"B{rA2III}"] = f"BỆNH NHÂN NGOẠI TỈNH ĐẾN: {len(ds_nghi_dinh_70)} LƯỢT"
    bc_102_content_generate(ds_nghi_dinh_70, rA2III, worksheet)
    # Tổng đối tượng 146 ngoại trú
    columns = ["I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "AA"]
    for col in columns:
        worksheet[f"{col}{rA2}"].value = f"={col}{rA2III}"
    # Tổng đối tượng nd70 ngoại trú
    for col in columns:
        worksheet[f"{col}{rA1}"].value = f"={col}{rA1I}+{col}{rA1II}+{col}{rA1III}"
    # Tổng cộng khám chữa bệnh ngoại trú
    for col in columns:
        worksheet[f"{col}{rA}"].value = f"={col}{rA1}+{col}{rA2}"
    #endregion
    return {
        "rB": rA2III + len(ds_nghi_dinh_70) + 1,
        "rA1": rA1,
        "rA1I":rA1I,
        "rA1II":rA1II,
        "rA1III":rA1III,
        "rA2":rA2,
        "rA2III":rA2III,
    }
        
def bc_102_noi_tru(worksheet, rB, ds_dkbd_noi, ds_noi_tinh_den, ds_ngoai_tinh_den, ds_nd70_noi):
    # Bắt đầu với phần điều trị nội trú
    # Đối tượng theo nghị định 146 đăng kí ban đầu tại bệnh viện nội trú
    # rB = rA2III + len(ds_nd70_ngoai_tru) + 1  # dòng Điều trị nội trú
    rB1 = rB + 1                      # B.1: Đối tượng theo nghị định 146
    rB1I = rB1 + 1                     # I. Người bệnh đăng kí ban đầu tại cơ sở KCB

    worksheet[f"A{rB}"].value = "B. ĐIỀU TRỊ NỘI TRÚ"
    worksheet[f"A{rB1}"].value = "B.1"
    worksheet[f"B{rB1}"].value = "ĐỐI TƯỢNG THEO NGHỊ ĐỊNH 146"
    worksheet[f"A{rB1I}"].value = "I"
    worksheet[f"B{rB1I}"].value = f"NGƯỜI BỆNH ĐKBĐ TẠI CƠ SỞ KCB: {len(ds_dkbd_noi)} LƯỢT"
    bc_102_content_generate(ds_dkbd_noi, rB1I, worksheet)

    # Danh sách các bệnh nhân đa tuyến đến nội tỉnh nội trú
    rB1II = rB1I + len(ds_dkbd_noi) + 1
    worksheet[f"A{rB1II}"].value = "II"
    worksheet[f"B{rB1II}"].value = f"NGƯỜI BỆNH NGOẠI TỈNH ĐẾN: {len(ds_noi_tinh_den)} LƯỢT"
    bc_102_content_generate(ds_noi_tinh_den, rB1II, worksheet)

    # Danh sách các bệnh nhân đa tuyến đến ngoại tỉnh nội trú
    rB1III = rB1II + len(ds_noi_tinh_den) + 1
    worksheet[f"A{rB1III}"].value = "III"
    worksheet[f"B{rB1III}"].value = f"BỆNH NHÂN NGOẠI TỈNH ĐẾN: {len(ds_ngoai_tinh_den)} LƯỢT"
    bc_102_content_generate(ds_ngoai_tinh_den, rB1III, worksheet)

    # Đối tượng theo nghị định 70 nội trú
    rB2 = rB1III + len(ds_ngoai_tinh_den) + 1
    rB2III = rB2 + 1
    worksheet[f"A{rB2}"].value = "B2."
    worksheet[f"B{rB2}"].value = "ĐỐI TƯỢNG THEO NGHỊ ĐỊNH 70"
    worksheet[f"A{rB2III}"].value = "III."
    worksheet[f"B{rB2III}"].value = f"BỆNH NHÂN NGOẠI TỈNH ĐẾN: {len(ds_nd70_noi)} LƯỢT"
    bc_102_content_generate(ds_nd70_noi, rB2III, worksheet)

    # Tổng tiền nội trú
    # Formula nội trú - Tổng đối tượng 146 nội trú
    columns = ["I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "AA"]
    for col in columns:
        worksheet[f"{col}{rB2}"].value = f"={col}{rB2III}"

    # Tổng đối tượng nd70 nội trú
    for col in columns:
        worksheet[f"{col}{rB1}"].value = f"={col}{rB1I}+{col}{rB1II}+{col}{rB1III}"

    # Tổng cộng khám chữa bệnh nội trú
    for col in columns:
        worksheet[f"{col}{rB}"].value = f"={col}{rB1}+{col}{rB2}"

    return {
        "rTC": rB2III+len(ds_nd70_noi) +1,
        "rB1": rB1,
        "rB1I":rB1I,
        "rB1II":rB1II,
        "rB1III":rB1III,
        "rB2":rB2,
        "rB2III":rB2III,
    }

# Các hàm hỗ trợ định dạng Excel
def merge_cell_range(worksheet, cell_range, font_style="Regular", horizontal="center", vertical="center", is_wrap_text=True):
    # Gộp các ô
    worksheet.merge_cells(cell_range)
    
    # Lấy ô bắt đầu của vùng gộp
    start_cell = worksheet[cell_range.split(":")[0]]
    
    # Định dạng căn chỉnh
    start_cell.alignment = Alignment(horizontal=horizontal, vertical=vertical, wrap_text=is_wrap_text)
    
    # Áp dụng font style
    if font_style == "Bold":
        start_cell.font = bold_font
    elif font_style == "Bold_Large":
        start_cell.font = bold_font_large
    elif font_style == "Italic":
        start_cell.font = italic_font
    else:
        start_cell.font = regular_font

def excel_row_bold(ws, row_number, column_range=None, font=bold_font):
    for cell in ws[row_number]:
        if column_range:
            if cell.column in column_range:
                cell.font = font
        else:
            cell.font = font

def number_to_text(input_number, suffix=True):
    '''
    Thực hiện việc chuyển dữ liệu từ số thành chữ
    '''
    unit_numbers = ["không", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"]
    place_values = ["", "nghìn", "triệu", "tỷ"]
    is_negative = False

    s_number = f"{int(input_number)}"
    number = float(s_number)
    if number < 0:
        number = -number
        s_number = str(number)
        is_negative = True

    ones, tens, hundreds = 0, 0, 0
    position_digit = len(s_number)  # last -> first
    result = " "

    if position_digit == 0:
        result = unit_numbers[0] + result
    else:
        place_value = 0

        while position_digit > 0:
            tens = hundreds = -1
            ones = int(s_number[position_digit - 1])
            position_digit -= 1
            if position_digit > 0:
                tens = int(s_number[position_digit - 1])
                position_digit -= 1
                if position_digit > 0:
                    hundreds = int(s_number[position_digit - 1])
                    position_digit -= 1

            if (ones > 0) or (tens > 0) or (hundreds > 0) or (place_value == 3):
                result = place_values[place_value] + result

            place_value += 1
            if place_value > 3:
                place_value = 1

            if (ones == 1) and (tens > 1):
                result = "một " + result
            else:
                if (ones == 5) and (tens > 0):
                    result = "lăm " + result
                elif ones > 0:
                    result = unit_numbers[ones] + " " + result

            if tens < 0:
                break
            else:
                if (tens == 0) and (ones > 0):
                    result = "lẻ " + result
                if tens == 1:
                    result = "mười " + result
                if tens > 1:
                    result = unit_numbers[tens] + " mươi " + result

            if hundreds < 0:
                break
            else:
                if (hundreds > 0) or (tens > 0) or (ones > 0):
                    result = unit_numbers[hundreds] + " trăm " + result
            result = " " + result

    result = result.strip()

    str1 = result[0]
    str2 = result[1:]
    str1 = str1.upper()
    result = str1 + str2

    if is_negative:
        result = "Âm " + result
    return result + (" đồng chẵn ./." if suffix else "")

# Lấy dữ liệu cho báo cáo 19
def get_quy_cach():
    '''
    Thực hiện việc lấy quy cách để xuất dữ liệu báo cáo 19 từ model danhmucbv
    Lấy ma_vat_tu, ten_vat_tu, quy_cach từ VatTuYTe model
    '''
    query = VatTuYTe.objects.all().values("ma_vat_tu", "ten_vat_tu", "quy_cach")
    return list(query)

def get_bao_cao_19_data(ngay_bat_dau, ngay_ket_thuc, doi_tuong):
    '''
    Lấy dữ liệu cho báo cáo 19 từ các model XML3 dựa trên maLK
    :param ngay_bat_dau: Thời gian bắt đầu lấy dữ liệu
    :param ngay_ket_thuc: Thời gian kết thúc lấy dữ liệu
    :param doiTuong: 0: lấy tất cả, 1:BHXH VN, 2:BHXH BQP
    '''
    xml3_data_ngoai = []
    xml3_data_noi = []
    # Lấy dữ liệu maLK từ XML1
    query = XML1Model.objects.filter(ngayTToan__range=[ngay_bat_dau, ngay_ket_thuc])

    if doi_tuong is not None or doi_tuong > 0:
        if doi_tuong == 1:
            query = query.filter(XML1Model.maTinh != 97)
        elif doi_tuong == 2:
            query = query.filter(XML1Model.maTinh == 97)
    # Lấy danh sách maLKs dưới dạng list
    xml1_data = query.all()
    malk_ngoai = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_ngoai_tru or record.maLoaiKCB in key_loai_kcb_dieu_tri_ngoai_tru]
    malk_noi = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_noi_tru]
    # Nếu có maLKs, tiếp tục truy vấn xml2, xml3
    if malk_ngoai:
        xml3_data_ngoai = XML3Model.objects.filter(
            maLK__in=malk_ngoai,
            maVatTu__isnull=False
        ).exclude(maVatTu='').values(
            'maVatTu', 'tenVatTu', 'donViTinh', 'donGiaBH', 'soLuong', 'thanhTienBH'
        )
    if malk_noi:
        xml3_data_noi = XML3Model.objects.filter(
            maLK__in=malk_noi,
            maVatTu__isnull=False
        ).exclude(maVatTu='').values(
            'maVatTu', 'tenVatTu', 'donViTinh', 'donGiaBH', 'soLuong', 'thanhTienBH'
        )

    return xml3_data_ngoai, xml3_data_noi
def merge_bao_cao_19_data(xml3_data_ngoai, xml3_data_noi, ds_quy_cach):
    # Tạo dict quy_cach từ list[dict]
    quy_cach_dict = {
        (item["ma_vat_tu"], item["ten_vat_tu"]): item["quy_cach"]
        for item in ds_quy_cach
    }

    bao_cao_dict = {}

    # Ngoại trú
    for item in xml3_data_ngoai:
        key = (item["maVatTu"], item["tenVatTu"], item["donGiaBH"])
        quy_cach_key = (item["maVatTu"], item["tenVatTu"])

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_vtyt": item["maVatTu"],
                "ten_vtyt": item["tenVatTu"],
                "don_vi": item["donViTinh"],
                "gia_mua": item["donGiaBH"],
                "quy_cach": quy_cach_dict.get(quy_cach_key, ""),
                "gia_thanhtoan": item["donGiaBH"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0
            }

        bao_cao_dict[key]["sl_ngoaitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]

    # Nội trú
    for item in xml3_data_noi:
        key = (item["maVatTu"], item["tenVatTu"], item["donGiaBH"])
        quy_cach_key = (item["maVatTu"], item["tenVatTu"])

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_vtyt": item["maVatTu"],
                "ten_vtyt": item["tenVatTu"],
                "don_vi": item["donViTinh"],
                "gia_mua": item["donGiaBH"],
                "quy_cach": quy_cach_dict.get(quy_cach_key, ""),
                "gia_thanhtoan": item["donGiaBH"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0
            }

        bao_cao_dict[key]["sl_noitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]

    return list(bao_cao_dict.values())

# Lấy dữ liệu cho báo cáo 20
def get_ten_hoat_chat():
    '''
    Thực hiện việc lấy tên hoạt chất để đưa vào báo cáo 20
    Lấy ma_thuoc, ten_thuoc, ten_hoat_chat từ Thuoc model
    '''
    query = Thuoc.objects.all().values("ma_thuoc", "ten_thuoc", "so_dang_ky", "don_gia", "ten_hoat_chat")
    return list(query)

def get_bao_cao_20_data(ngay_bat_dau, ngay_ket_thuc, doi_tuong):
    '''
        Lấy dữ liệu cho báo cáo 20 từ các model XML2 dựa trên maLK
        :param ngay_bat_dau: Thời gian bắt đầu lấy dữ liệu
        :param ngay_ket_thuc: Thời gian kết thúc lấy dữ liệu
        :param doiTuong: 0: lấy tất cả, 1:BHXH VN, 2:BHXH BQP
    '''
    xml2_data_ngoai = []
    xml2_data_noi = []
    # Lấy dữ liệu maLK từ XML1
    query = XML1Model.objects.filter(ngayTToan__range=[ngay_bat_dau, ngay_ket_thuc])

    if doi_tuong is not None or doi_tuong > 0:
        if doi_tuong == 1:
            query = query.filter(XML1Model.maTinh != 97)
        elif doi_tuong == 2:
            query = query.filter(XML1Model.maTinh == 97)
    # Lấy danh sách maLKs dưới dạng list
    xml1_data = query.all()
    malk_ngoai = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_ngoai_tru or record.maLoaiKCB in key_loai_kcb_dieu_tri_ngoai_tru]
    malk_noi = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_noi_tru]
    # Nếu có maLKs, tiếp tục truy vấn xml2
    if malk_ngoai:
        xml2_data_ngoai = XML2Model.objects.filter(
            maLK__in=malk_ngoai,
            maThuoc__isnull=False
        ).exclude(maThuoc='').values(
            'maThuoc', 'tenThuoc', 'duongDung', 'soDangKy', 'donViTinh', 'soLuong', 'donGia', 'thanhTienBH', 'hamLuong',
        )
    if malk_noi:
        xml2_data_noi = XML2Model.objects.filter(
            maLK__in=malk_noi,
            maThuoc__isnull=False
        ).exclude(maThuoc='').values(
            'maThuoc', 'tenThuoc', 'duongDung', 'soDangKy', 'donViTinh', 'soLuong', 'donGia', 'thanhTienBH', 'hamLuong',
        )

    return xml2_data_ngoai, xml2_data_noi

def merge_bao_cao_20_data(xml2_data_ngoai, xml2_data_noi, ds_ten_hoat_chat):
    # Chuyển list dict -> dict với key là tuple (ma_thuoc, ten_thuoc, so_dang_ky, don_gia)
    ten_hc_dict = {
        (item["ma_thuoc"], item["ten_thuoc"], item["so_dang_ky"], item["don_gia"]): item["ten_hoat_chat"]
        for item in ds_ten_hoat_chat
    }

    bao_cao_dict = {}

    # Xử lý dữ liệu ngoại trú
    for item in xml2_data_ngoai:
        key = (item["maThuoc"], item["tenThuoc"], item["soDangKy"], item["donGia"])
        ten_hoatchat = ten_hc_dict.get(key, "")

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_thuoc": item["maThuoc"],
                "ten_hoatchat": ten_hoatchat,
                "ten_thuoc": item["tenThuoc"],
                "duong_dung": item["duongDung"],
                "ham_luong": item["hamLuong"],
                "so_dky": item["soDangKy"],
                "don_vi": item["donViTinh"],
                "don_gia": item["donGia"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0
            }

        bao_cao_dict[key]["sl_ngoaitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]

    # Xử lý dữ liệu nội trú
    for item in xml2_data_noi:
        key = (item["maThuoc"], item["tenThuoc"], item["soDangKy"], item["donGia"])
        ten_hoatchat = ten_hc_dict.get(key, "")

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_thuoc": item["maThuoc"],
                "ten_hoatchat": ten_hoatchat,
                "ten_thuoc": item["tenThuoc"],
                "duong_dung": item["duongDung"],
                "ham_luong": item["hamLuong"],
                "so_dky": item["soDangKy"],
                "don_vi": item["donViTinh"],
                "don_gia": item["donGia"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0
            }

        bao_cao_dict[key]["sl_noitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]

    return list(bao_cao_dict.values())

# lấy dữ liệu cho báo cáo 21
def get_bao_cao_21_data(ngay_bat_dau, ngay_ket_thuc, doi_tuong):
    '''
        Lấy dữ liệu từ xml1 theo khoảng thời gian và lấy thông tin thanh toán từ xml3 theo maLK để làm báo cáo 21.
        :param ngay_bat_dau: Thời gian bắt đầu lấy dữ liệu
        :param ngay_ket_thuc: Thời gian kết thúc lấy dữ liệu
        :param doiTuong: 0: lấy tất cả, 1:BHXH VN, 2:BHXH BQP
    '''
    xml3_data_ngoai = []
    xml3_data_noi = []
    # Lấy dữ liệu maLK từ XML1
    query = XML1Model.objects.filter(ngayTToan__range=[ngay_bat_dau, ngay_ket_thuc])

    if doi_tuong is not None or doi_tuong > 0:
        if doi_tuong == 1:
            query = query.filter(XML1Model.maTinh != 97)
        elif doi_tuong == 2:
            query = query.filter(XML1Model.maTinh == 97)
    # Lấy danh sách maLKs dưới dạng list
    xml1_data = query.all()
    malk_ngoai = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_ngoai_tru or record.maLoaiKCB in key_loai_kcb_dieu_tri_ngoai_tru]
    malk_noi = [record.maLK for record in xml1_data if record.maLoaiKCB in key_loai_kcb_noi_tru]
    # Nếu có maLKs, tiếp tục truy vấn xml2, xml3
    if malk_ngoai:
        xml3_data_ngoai = XML3Model.objects.filter(
            maLK__in=malk_ngoai,
            maDichVu__isnull=False
        ).exclude(maDichVu='').values(
            'maDichVu', 'tenDichVu', 'donGiaBH', 'soLuong', 'thanhTienBH', 'thanhTienBV', 'tyLeTTDV'
        )
    if malk_noi:
        xml3_data_noi = XML3Model.objects.filter(
            maLK__in=malk_noi,
            maDichVu__isnull=False
        ).exclude(maDichVu='').values(
            'maDichVu', 'tenDichVu', 'donGiaBH', 'soLuong', 'thanhTienBH', 'thanhTienBV', 'tyLeTTDV'
        )

    return xml3_data_ngoai, xml3_data_noi

def merge_bao_cao_21_data(xml3_data_ngoai, xml3_data_noi):
    bao_cao_dict = {}

    # Xử lý dữ liệu ngoại trú
    for item in xml3_data_ngoai:
        key = item["maDichVu"]

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_dvkt": item["maDichVu"],
                "ten_dvkt": item["tenDichVu"],
                "don_gia": item["donGiaBH"],
                "ty_le": item["tyLeTTDV"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0,
                "thanh_tien_bv": 0
            }

        bao_cao_dict[key]["sl_ngoaitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]
        bao_cao_dict[key]["thanh_tien_bv"] += item["thanhTienBV"]

    # Xử lý dữ liệu nội trú
    for item in xml3_data_noi:
        key = item["maDichVu"]

        if key not in bao_cao_dict:
            bao_cao_dict[key] = {
                "ma_dvkt": item["maDichVu"],
                "ten_dvkt": item["tenDichVu"],
                "don_gia": item["donGiaBH"],
                "ty_le": item["tyLeTTDV"],
                "sl_noitru": 0,
                "sl_ngoaitru": 0,
                "thanh_tien": 0,
                "thanh_tien_bv": 0
            }

        bao_cao_dict[key]["sl_noitru"] += item["soLuong"]
        bao_cao_dict[key]["thanh_tien"] += item["thanhTienBH"]
        bao_cao_dict[key]["thanh_tien_bv"] += item["thanhTienBV"]

    return list(bao_cao_dict.values())
