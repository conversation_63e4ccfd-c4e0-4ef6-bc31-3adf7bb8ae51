{% extends 'layouts/base.html' %}

{% block title %}Thêm người dùng mới | LAN Insight Guardian{% endblock %}

{% block extra_css %}
<!-- Select2 -->
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
{% endblock %}

{% block page_title %}Thêm người dùng mới{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:list' %}">Quản lý người dùng</a></li>
<li class="breadcrumb-item active">Thêm người dùng</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary">
      <div class="card-header">
        <h3 class="card-title">Thông tin người dùng</h3>
      </div>
      <!-- /.card-header -->
      <!-- form start -->
      <form role="form" method="post">
        {% csrf_token %}
        <div class="card-body">
          {% if form.non_field_errors %}
          <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_username">Tên đăng nhập</label>
                <input type="text" name="username" class="form-control {% if form.username.errors %}is-invalid{% endif %}" id="id_username" placeholder="Nhập tên đăng nhập" value="{{ form.username.value|default:'' }}">
                {% if form.username.errors %}
                <div class="invalid-feedback">
                  {% for error in form.username.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_email">Email</label>
                <input type="email" name="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" id="id_email" placeholder="Nhập email" value="{{ form.email.value|default:'' }}">
                {% if form.email.errors %}
                <div class="invalid-feedback">
                  {% for error in form.email.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_first_name">Tên</label>
                <input type="text" name="first_name" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" id="id_first_name" placeholder="Nhập tên" value="{{ form.first_name.value|default:'' }}">
                {% if form.first_name.errors %}
                <div class="invalid-feedback">
                  {% for error in form.first_name.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_last_name">Họ</label>
                <input type="text" name="last_name" class="form-control {% if form.last_name.errors %}is-invalid{% endif %}" id="id_last_name" placeholder="Nhập họ" value="{{ form.last_name.value|default:'' }}">
                {% if form.last_name.errors %}
                <div class="invalid-feedback">
                  {% for error in form.last_name.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_password1">Mật khẩu</label>
                <input type="password" name="password1" class="form-control {% if form.password1.errors %}is-invalid{% endif %}" id="id_password1" placeholder="Nhập mật khẩu">
                {% if form.password1.errors %}
                <div class="invalid-feedback">
                  {% for error in form.password1.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">
                  Mật khẩu phải có ít nhất 8 ký tự và không được quá đơn giản.
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="id_password2">Xác nhận mật khẩu</label>
                <input type="password" name="password2" class="form-control {% if form.password2.errors %}is-invalid{% endif %}" id="id_password2" placeholder="Nhập lại mật khẩu">
                {% if form.password2.errors %}
                <div class="invalid-feedback">
                  {% for error in form.password2.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Vai trò hệ thống</label>
                <div class="custom-control custom-checkbox">
                  <input class="custom-control-input" type="checkbox" id="id_is_staff" name="is_staff" {% if form.is_staff.value %}checked{% endif %}>
                  <label for="id_is_staff" class="custom-control-label">Nhân viên (có quyền truy cập trang quản trị)</label>
                </div>
                <div class="custom-control custom-checkbox">
                  <input class="custom-control-input" type="checkbox" id="id_is_superuser" name="is_superuser" {% if form.is_superuser.value %}checked{% endif %}>
                  <label for="id_is_superuser" class="custom-control-label">Quản trị viên (có tất cả quyền)</label>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Trạng thái</label>
                <div class="custom-control custom-checkbox">
                  <input class="custom-control-input" type="checkbox" id="id_is_active" name="is_active" {% if form.is_active.value %}checked{% endif %} checked>
                  <label for="id_is_active" class="custom-control-label">Hoạt động</label>
                </div>
              </div>
            </div>
          </div>

          <!-- Phân quyền người dùng -->
          <div class="card card-primary card-outline">
            <div class="card-header">
              <h3 class="card-title">Phân quyền người dùng</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label>Vai trò người dùng</label>
                <div class="input-group mb-3">
                  <select id="role_selector" name="user_role" class="form-control select2">
                    <option value="">-- Chọn vai trò --</option>
                    {% for role in available_roles %}
                    <option value="{{ role.id }}">
                      {{ role.name }} - {{ role.description|default:"" }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
                <small class="form-text text-muted">Chọn vai trò để tự động chọn các quyền tương ứng.</small>
              </div>

              <div class="form-group">
                <label>Quyền chi tiết</label>
                <div class="row">
                  {% for group_name, permissions_list in grouped_permissions.items %}
                  <div class="col-md-4">
                    <div class="card">
                      <div class="card-header">
                        <h3 class="card-title">{{ group_name }}</h3>
                      </div>
                      <div class="card-body">
                        {% for permission in permissions_list %}
                        <div class="custom-control custom-checkbox">
                          <input class="custom-control-input permission-checkbox" type="checkbox" id="permission_{{ permission.id }}" name="user_permissions" value="{{ permission.id }}">
                          <label for="permission_{{ permission.id }}" class="custom-control-label">{{ permission.name_vi }}</label>
                        </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>

        </div>
        <!-- /.card-body -->

        <div class="card-footer">
          <button type="submit" class="btn btn-primary" id="save-button">
            <i class="fas fa-save"></i> Lưu
          </button>
          <a href="{% url 'users:list' %}" class="btn btn-default">
            <i class="fas fa-times"></i> Hủy
          </a>
        </div>
      </form>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 -->
<script src="{{ STATIC_URL }}AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js"></script>
<script>
  $(function () {
    $('.select2').select2();

    // Tạo đối tượng JavaScript từ dữ liệu Python
    var rolesPermissions = {
      {% for role_id, permissions in roles_permissions.items %}
        "{{ role_id }}": {{ permissions|safe }},
      {% endfor %}
    };

    console.log('Dữ liệu quyền của vai trò:', rolesPermissions);

    // Xử lý sự kiện khi chọn vai trò
    $('#role_selector').on('change', function() {
      var roleId = $(this).val();
      console.log('Vai trò được chọn:', roleId);

      // Bỏ chọn tất cả các quyền hiện tại
      $('.permission-checkbox').prop('checked', false);

      if (!roleId) {
        // Nếu không chọn vai trò nào, bỏ chọn tất cả các quyền
        return;
      }

      // Lấy danh sách quyền của vai trò từ dữ liệu đã có
      var permissions = rolesPermissions[roleId];
      console.log('Quyền của vai trò:', permissions);

      if (permissions && permissions.length > 0) {
        console.log('Số lượng quyền:', permissions.length);
        console.log('Số lượng checkbox quyền:', $('.permission-checkbox').length);

        // Chọn các quyền của vai trò
        permissions.forEach(function(permissionId) {
          var checkboxId = '#permission_' + permissionId;
          var checkbox = $(checkboxId);
          console.log('Chọn quyền:', checkboxId, 'Tồn tại:', checkbox.length > 0);
          checkbox.prop('checked', true);
        });
      } else {
        console.log('Không có quyền nào cho vai trò này hoặc mảng rỗng');
      }
    });

    // Xử lý nút lưu thay đổi
    $('#save-button').on('click', function(e) {
      e.preventDefault();

      // Kiểm tra các trường bắt buộc
      var username = $('#id_username').val();
      var password1 = $('#id_password1').val();
      var password2 = $('#id_password2').val();

      if (!username) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng nhập tên đăng nhập',
        });
        return;
      }

      if (!password1 || !password2) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng nhập đầy đủ mật khẩu và xác nhận mật khẩu',
        });
        return;
      }

      if (password1 !== password2) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Mật khẩu xác nhận không khớp với mật khẩu mới',
        });
        return;
      }

      // Hiển thị thông báo xác nhận
      Swal.fire({
        title: 'Xác nhận tạo người dùng',
        text: 'Bạn có chắc chắn muốn tạo người dùng mới?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Tạo người dùng',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Hiển thị loading
          Swal.fire({
            title: 'Đang xử lý...',
            html: 'Vui lòng đợi trong giây lát',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });

          // Submit form
          $(this).closest('form').submit();
        }
      });
    });
  });
</script>
{% endblock %}