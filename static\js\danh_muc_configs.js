/**
 * <PERSON><PERSON><PERSON> hình cho các danh mục khác nhau
 */

// C<PERSON>u hình cho quản lý Tỉnh/Thành phố
const TINH_CONFIG = {
    // URLs
    listApiUrl: '/danhmuckhac/tinh/api/',
    createUrl: '/danhmuckhac/tinh/create/',
    editUrl: '/danhmuckhac/tinh/{id}/edit/',
    deleteUrl: '/danhmuckhac/tinh/{id}/delete/',
    toggleUrl: '/danhmuckhac/tinh/{id}/toggle-hieu-luc/',
    exportUrl: '/danhmuckhac/tinh/export/',
    importUrl: '/danhmuckhac/tinh/import/',
    getDataUrl: '/danhmuckhac/api/get-data/?category=tinh',
  
    // Table config
    tableId: '#tinhTable',
    columns: [
      {title: "Mã tỉnh", field: "ma_tinh", sorter: "string", width: 120},
      {title: "Tên tỉnh", field: "ten_tinh", sorter: "string"},
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        width: 150,
        formatter: function(cell) {
          const value = cell.getValue();
          const id = cell.getRow().getData().id;
          const checked = value ? 'checked' : '';
          const switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        }
      }
    ],
  
    // Form config
    createFormId: '#createForm',
    editFormId: '#editForm',
    importFormId: '#importForm',
    createModalId: '#createModal',
    editModalId: '#editModal',
    importModalId: '#importModal',
  
    // Field mappings
    fields: {
      ma: 'ma_tinh',
      ten: 'ten_tinh',
      hieu_luc: 'hieu_luc'
    },
  
    // Labels
    labels: {
      ma: 'Mã tỉnh',
      ten: 'Tên tỉnh',
      hieu_luc: 'Hiệu lực',
      entity: 'tỉnh/thành phố',
      entityPlural: 'tỉnh/thành phố'
    },
  
    // Validation
    validation: {
      maPattern: /^\d{2}$/,
      maLength: 2,
      tenMinLength: 2
    },
  
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };
  
// Cấu hình cho quản lý Quận/Huyện
const QUAN_HUYEN_CONFIG = {
    // URLs
    listApiUrl: '/danhmuckhac/quanhuyen/api/',
    createUrl: '/danhmuckhac/quanhuyen/create/',
    editUrl: '/danhmuckhac/quanhuyen/{id}/edit/',
    deleteUrl: '/danhmuckhac/quanhuyen/{id}/delete/',
    toggleUrl: '/danhmuckhac/quanhuyen/{id}/toggle-hieu-luc/',
    exportUrl: '/danhmuckhac/quanhuyen/export/',
    importUrl: '/danhmuckhac/quanhuyen/import/',
    getDataUrl: '/danhmuckhac/api/get-data/?category=quanhuyen',
  
    // Table config
    tableId: '#quanHuyenTable',
    columns: [
      {title: "Mã quận/huyện", field: "ma_quan_huyen", sorter: "string", width: 170},
      {title: "Tên quận/huyện", field: "ten_quan_huyen", sorter: "string"},
      {
        title: "Tỉnh/Thành phố", 
        field: "ma_tinh", // API quận/huyện trả về trường 'ma_tinh' (mã 2 chữ số của tỉnh)
        sorter: "string", // Sắp xếp dựa trên giá trị được trả về bởi sorterParams.format
        width: 200,
        formatter: function(cell, formatterParams, onRendered) {
            const maTinh = cell.getValue(); // Lấy mã tỉnh từ dữ liệu dòng
            if (window.provincesCache && window.provincesCache.loaded && window.provincesCache.map) {
                const tenTinh = window.provincesCache.map.get(String(maTinh));
                return tenTinh ? tenTinh : (maTinh ? `Mã: ${maTinh}` : "Không rõ");
            }
            return maTinh ? "Đang tải..." : "Không có"; // Hiển thị trong khi chờ cache
        },
        sorterParams: {
            format: function(value, data, cell, row, sorterParams){
                // value ở đây là ma_tinh
                if (window.provincesCache && window.provincesCache.loaded && window.provincesCache.map) {
                    const tenTinh = window.provincesCache.map.get(String(value));
                    return tenTinh ? tenTinh.toLowerCase() : ""; // Sắp xếp theo tên tỉnh (lowercase để case-insensitive)
                }
                return "";
            }
        }
      },
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        width: 150,
        formatter: function(cell) {
          const value = cell.getValue();
          const id = cell.getRow().getData().id;
          const checked = value ? 'checked' : '';
          const switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        }
      }
    ],
  
    // Field mappings
    fields: {
      ma: 'ma_quan_huyen',
      ten: 'ten_quan_huyen',
      hieu_luc: 'hieu_luc',
      tinh: 'tinh'
    },
  
    // Labels
    labels: {
      ma: 'Mã quận/huyện',
      ten: 'Tên quận/huyện',
      hieu_luc: 'Hiệu lực',
      entity: 'quận/huyện',
      entityPlural: 'quận/huyện'
    },
  
    // Validation
    validation: {
      maPattern: /^\d{3}$/,
      maLength: 3,
      tenMinLength: 2
    },
  
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };  
  
// Cấu hình cho quản lý Phường/Xã
const PHUONG_XA_CONFIG = {
    // URLs
    listApiUrl: '/danhmuckhac/xaphuong/api/',
    createUrl: '/danhmuckhac/xaphuong/create/',
    editUrl: '/danhmuckhac/xaphuong/{id}/edit/',
    deleteUrl: '/danhmuckhac/xaphuong/{id}/delete/',
    toggleUrl: '/danhmuckhac/xaphuong/{id}/toggle-hieu-luc/',
    exportUrl: '/danhmuckhac/xaphuong/export/',
    importUrl: '/danhmuckhac/xaphuong/import/',
    getDataUrl: '/danhmuckhac/api/get-data/?category=xaphuong',
  
    // Table config
    tableId: '#phuongXaTable',
    columns: [
      {title: "Mã P/X", field: "ma_xa_phuong", sorter: "string", width: 100},
      {title: "Tên phường/xã", field: "ten_xa_phuong", sorter: "string"},
      {
        title: "Quận/Huyện", field: "ma_quan_huyen", sorter: "string", width: 150, 
        formatter: function(cell, formatterParams, onRendered) {
            const maQuanHuyen = cell.getValue(); // Lấy mã quận huyện từ dữ liệu dòng
            if (window.allDistrictsCache && window.allDistrictsCache.loaded && window.allDistrictsCache.map) {
                const tenQuanHuyen = window.allDistrictsCache.map.get(String(maQuanHuyen));
                return tenQuanHuyen ? tenQuanHuyen : (maQuanHuyen ? `Mã: ${maQuanHuyen}` : "Không rõ");
            }
            return maQuanHuyen ? "Đang tải..." : "Không có"; // Hiển thị trong khi chờ cache
        },
      },
      {
        title: "Tỉnh/Thành phố", field: "ma_tinh", sorter: "string", width: 200,
        formatter: function(cell, formatterParams, onRendered) {
            const maTinh = cell.getValue(); // Lấy mã tỉnh từ dữ liệu dòng
            if (window.provincesCache && window.provincesCache.loaded && window.provincesCache.map) {
                const tenTinh = window.provincesCache.map.get(String(maTinh));
                return tenTinh ? tenTinh : (maTinh ? `Mã: ${maTinh}` : "Không rõ");
            }
            return maTinh ? "Đang tải..." : "Không có"; // Hiển thị trong khi chờ cache
        },
      },
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        width: 120,
        formatter: function(cell) {
          const value = cell.getValue();
          const id = cell.getRow().getData().id;
          const checked = value ? 'checked' : '';
          const switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        }
      }
    ],
  
    // Field mappings
    fields: {
      ma: 'ma_xa_phuong',
      ten: 'ten_xa_phuong',
      hieu_luc: 'hieu_luc',
      quan_huyen: 'quan_huyen',
      tinh: 'tinh'
    },
  
    // Labels
    labels: {
      ma: 'Mã phường/xã',
      ten: 'Tên phường/xã',
      hieu_luc: 'Hiệu lực',
      entity: 'phường/xã',
      entityPlural: 'phường/xã'
    },
  
    // Validation
    validation: {
      maPattern: /^\d{5}$/,
      maLength: 5,
      tenMinLength: 2
    },
  
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };
  
  
  // Cấu hình cho nơi đăng kí ban đầu
  const NOI_DKBD_CONFIG = {
    // URLs
    listApiUrl: "/danhmuckhac/noi-dkbd/api/",
    createUrl: "/danhmuckhac/noi-dkbd/create/",
    editUrl: "/danhmuckhac/noi-dkbd/0/edit/",
    deleteUrl: "/danhmuckhac/noi-dkbd/0/delete/",
    toggleUrl: "/danhmuckhac/noi-dkbd/0/toggle-hieu-luc/",
    exportUrl: "/danhmuckhac/noi-dkbd/export/",
    importUrl: "/danhmuckhac/noi-dkbd/import/",
    getDataUrl: "/danhmuckhac/api/get-data/?category=noi-dkbd",
    
    // Table config
    tableId: '#noiDKBDTable',
    columns: [
      {title: "Mã ĐKBD", field: "ma", sorter: "string"},
      {title: "Tên ĐKBĐ", field: "ten", sorter: "string"},
      {title: "Tuyến CMKT", field: "tuyen_cmkt", sorter: "string"},
      {title: "Hạng bệnh viện", field: "hang_benh_vien", sorter: "string"},
      {title: "Điểm", field: "diem", sorter: "string"},
      {title: "Địa chỉ", field: "dia_chi", sorter: "string"},
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        formatter: function(cell) {
          var value = cell.getValue();
          var id = cell.getRow().getData().id;
          var checked = value ? 'checked' : '';
          var switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        },
        headerSort: true
      }
    ],
    
    // Field mappings
    fields: {
      ma: 'ma',
      ten: 'ten',
      tuyen_cmkt: 'tuyen_cmkt',
      hang_benh_vien: 'hang_benh_vien',
      diem: 'diem',
      dia_chi: 'dia_chi',
      hieu_luc: 'hieu_luc'
    },
    
    // Labels
    labels: {
      ma: 'Mã ĐKBĐ',
      ten: 'Tên ĐKBĐ',
      hieu_luc: 'Hiệu lực',
      entity: 'Nơi ĐKBĐ',
      entityPlural: 'Nơi ĐKBĐ'
    },
    
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };
  
  // Cấu hình cho phạm vi chuyên môn
  const PHAM_VI_CHUYEN_MON_CONFIG = {
    // URLs
    listApiUrl: "/danhmuckhac/pvcm/api/",
    createUrl: "/danhmuckhac/pvcm/create/",
    editUrl: "/danhmuckhac/pvcm/0/edit/",
    deleteUrl: "/danhmuckhac/pvcm/0/delete/",
    toggleUrl: "/danhmuckhac/pvcm/0/toggle-hieu-luc/",
    exportUrl: "/danhmuckhac/pvcm/export/",
    importUrl: "/danhmuckhac/pvcm/import/",
    getDataUrl: "/danhmuckhac/api/get-data/?category=pvcm",
    
    // Table config
    tableId: '#pvcmTable',
    columns: [
      {title: "Mã phạm vi", field: "ma_pham_vi", sorter: "string"},
      {title: "Tên chuyên khoa", field: "ten_chuyen_khoa", sorter: "string"},
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        formatter: function(cell) {
          var value = cell.getValue();
          var id = cell.getRow().getData().id;
          var checked = value ? 'checked' : '';
          var switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        },
        headerSort: true
      }
    ],
    
    // Field mappings
    fields: {
      ma_pham_vi: 'ma_pham_vi',
      ten_chuyen_khoa: 'ten_chuyen_khoa',
      hieu_luc: 'hieu_luc'
    },
    
    // Labels
    labels: {
      ma_pham_vi: 'Mã phạm vi',
      ten_chuyen_khoa: 'Tên chuyên khoa',
      hieu_luc: 'Hiệu lực',
      entity: 'Phạm vi chuyên môn',
      entityPlural: 'Phạm vi chuyên môn'
    },
    
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };
  
  // Cấu hình cho Nghề nghiệp
  const ngheNghiepConfig = {
    // URLs
    listApiUrl: "/danhmuckhac/nghe-nghiep/api/",
    createUrl: "/danhmuckhac/nghe-nghiep/create/",
    editUrl: "/danhmuckhac/nghe-nghiep/0/edit/",
    deleteUrl: "/danhmuckhac/nghe-nghiep/0/delete/",
    toggleUrl: "/danhmuckhac/nghe-nghiep/0/toggle-hieu-luc/",
    exportUrl: "/danhmuckhac/nghe-nghiep/export/",
    importUrl: "/danhmuckhac/nghe-nghiep/import/",
    getDataUrl: "/danhmuckhac/api/get-data/?category=nghe_nghiep",
    
    // Table config
    tableId: '#ngheNghiepTable',
    columns: [
      {title: "Mã nghề nghiệp", field: "ma_nghe_nghiep", sorter: "string"},
      {title: "Tên nghề nghiệp", field: "ten_nghe_nghiep", sorter: "string"},
      {title: "Mô tả", field: "mo_ta", sorter: "string", width: 200},
      {
        title: "Hiệu lực", 
        field: "hieu_luc", 
        sorter: "boolean",
        formatter: function(cell) {
          var value = cell.getValue();
          var id = cell.getRow().getData().id;
          var checked = value ? 'checked' : '';
          var switchId = 'switch_' + id;
          
          return `
            <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
              <input type="checkbox" class="custom-control-input hieu-luc-switch" id="${switchId}" data-id="${id}" ${checked}>
              <label class="custom-control-label" for="${switchId}"></label>
            </div>
          `;
        },
        headerSort: true
      }
    ],
    
    // Field mappings
    fields: {
      ma: 'ma_nghe_nghiep',
      ten: 'ten_nghe_nghiep',
      mo_ta: 'mo_ta',
      hieu_luc: 'hieu_luc'
    },
    
    // Labels
    labels: {
      ma: 'Mã nghề nghiệp',
      ten: 'Tên nghề nghiệp',
      mo_ta: 'Mô tả',
      hieu_luc: 'Hiệu lực',
      entity: 'nghề nghiệp',
      entityPlural: 'nghề nghiệp'
    },
    
    // Validation
    validation: {
      maPattern: /^\d{3}$/,
      maLength: 3,
      tenMinLength: 2
    },
    
    // Features
    features: {
      bulkOperations: true,
      contextMenu: true,
      dragDrop: true,
      autoSave: true,
      accessibility: true
    }
  };
  
  // Export configs
  window.DanhMucConfigs = {
    tinh: TINH_CONFIG,
    quan_huyen: QUAN_HUYEN_CONFIG,
    phuong_xa: PHUONG_XA_CONFIG,
    noi_dkbd: NOI_DKBD_CONFIG,
    pham_vi_chuyen_mon: PHAM_VI_CHUYEN_MON_CONFIG,
    nghe_nghiep: ngheNghiepConfig
  };
  
  // Helper function để lấy config theo loại
  window.getDanhMucConfig = function(type) {
    return window.DanhMucConfigs[type] || null;
  };
  
  console.log('📋 Danh mục configs loaded successfully!');
  
  