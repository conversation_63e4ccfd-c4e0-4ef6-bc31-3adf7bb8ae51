{% extends 'layouts/base.html' %}

{% block title %}<PERSON><PERSON> sơ cá nhân | LAN Insight Guardian{% endblock %}

{% block page_title %}<PERSON><PERSON> sơ cá nhân{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active"><PERSON><PERSON> sơ cá nhân</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-3">

    <!-- Profile Image -->
    <div class="card card-primary card-outline">
      <div class="card-body box-profile">
        <div class="text-center">
          <img class="profile-user-img img-fluid img-circle"
               src="/static/AdminLTE-3.0.1/dist/img/user4-128x128.jpg"
               alt="User profile picture">
        </div>

        <h3 class="profile-username text-center">{{ user.get_full_name }}</h3>

        <p class="text-muted text-center">
          {% if user.profile.position %}
          {{ user.profile.position.name }}
          {% elif user.is_superuser %}
          Qu<PERSON>n trị viên
          {% elif user.is_staff %}
          Nhân viên
          {% else %}
          Người dùng
          {% endif %}
        </p>

        <ul class="list-group list-group-unbordered mb-3">
          <li class="list-group-item">
            <b>Tên đăng nhập</b> <a class="float-right">{{ user.username }}</a>
          </li>
          <li class="list-group-item">
            <b>Email</b> <a class="float-right">{{ user.email }}</a>
          </li>
          <li class="list-group-item">
            <b>Khoa/Phòng ban</b> <a class="float-right">{{ user.profile.department.name|default:"Chưa cập nhật" }}</a>
          </li>
          <li class="list-group-item">
            <b>Chức vụ</b> <a class="float-right">{{ user.profile.position.name|default:"Chưa cập nhật" }}</a>
          </li>
          <li class="list-group-item">
            <b>Chứng chỉ hành nghề</b> <a class="float-right">{{ user.profile.practice_certificate|default:"Chưa cập nhật" }}</a>
          </li>
          <li class="list-group-item">
            <b>Số điện thoại</b> <a class="float-right">{{ user.profile.phone_number|default:"Chưa cập nhật" }}</a>
          </li>
          <li class="list-group-item">
            <b>Ngày tham gia</b> <a class="float-right">{{ user.date_joined|date:"d/m/Y" }}</a>
          </li>
        </ul>

        <a href="{% url 'users:change_password_self' %}" class="btn btn-primary btn-block"><b>Đổi mật khẩu</b></a>
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
  <div class="col-md-9">
    <div class="card">
      <div class="card-header p-2">
        <ul class="nav nav-pills">
          <li class="nav-item"><a class="nav-link active" href="#settings" data-toggle="tab">Cài đặt</a></li>
          <li class="nav-item"><a class="nav-link" href="#activity" data-toggle="tab">Hoạt động gần đây</a></li>
        </ul>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content">
          <div class="active tab-pane" id="settings">
            <form class="form-horizontal" method="post" action="{% url 'users:profile' %}" enctype="multipart/form-data">
              {% csrf_token %}
              {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}

              {% if messages %}
              <div class="alert alert-success">
                {% for message in messages %}
                  {{ message }}
                {% endfor %}
              </div>
              {% endif %}

              <div class="form-group row">
                <label for="id_first_name" class="col-sm-2 col-form-label">Tên</label>
                <div class="col-sm-10">
                  <input type="text" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" id="id_first_name" name="first_name" placeholder="Tên" value="{{ form.first_name.value }}">
                  {% if form.first_name.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.first_name.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group row">
                <label for="id_last_name" class="col-sm-2 col-form-label">Họ</label>
                <div class="col-sm-10">
                  <input type="text" class="form-control {% if form.last_name.errors %}is-invalid{% endif %}" id="id_last_name" name="last_name" placeholder="Họ" value="{{ form.last_name.value }}">
                  {% if form.last_name.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.last_name.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group row">
                <label for="id_email" class="col-sm-2 col-form-label">Email</label>
                <div class="col-sm-10">
                  <input type="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" id="id_email" name="email" placeholder="Email" value="{{ form.email.value }}">
                  {% if form.email.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.email.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group row">
                <label for="{{ profile_form.phone_number.id_for_label }}" class="col-sm-2 col-form-label">Số điện thoại</label>
                <div class="col-sm-10">
                  {{ profile_form.phone_number }}
                  {% if profile_form.phone_number.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.phone_number.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-group row">
                <label for="{{ profile_form.department.id_for_label }}" class="col-sm-2 col-form-label">Khoa/Phòng ban</label>
                <div class="col-sm-10">
                  {{ profile_form.department }}
                  {% if profile_form.department.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.department.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-group row">
                <label for="{{ profile_form.position.id_for_label }}" class="col-sm-2 col-form-label">Chức vụ</label>
                <div class="col-sm-10">
                  {{ profile_form.position }}
                  {% if profile_form.position.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.position.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-group row">
                <label for="{{ profile_form.practice_certificate.id_for_label }}" class="col-sm-2 col-form-label">Chứng chỉ hành nghề</label>
                <div class="col-sm-10">
                  {{ profile_form.practice_certificate }}
                  {% if profile_form.practice_certificate.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.practice_certificate.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-group row">
                <label for="{{ profile_form.profile_image.id_for_label }}" class="col-sm-2 col-form-label">Ảnh đại diện</label>
                <div class="col-sm-10">
                  {{ profile_form.profile_image }}
                  {% if profile_form.profile_image.errors %}
                  <div class="invalid-feedback">
                    {% for error in profile_form.profile_image.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-group row">
                <div class="offset-sm-2 col-sm-10">
                  <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                </div>
              </div>
            </form>
          </div>
          <!-- /.tab-pane -->

          <div class="tab-pane" id="activity">
            <!-- Activity Timeline -->
            <div class="timeline timeline-inverse">
              <!-- timeline time label -->
              <div class="time-label">
                <span class="bg-danger">
                  10/04/2025
                </span>
              </div>
              <!-- /.timeline-label -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-envelope bg-primary"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 12:05</span>

                  <h3 class="timeline-header"><a href="#">Hệ thống</a> ghi nhận hoạt động</h3>

                  <div class="timeline-body">
                    Bạn đã đăng nhập vào hệ thống
                  </div>
                </div>
              </div>
              <!-- END timeline item -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-user bg-info"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 09:30</span>

                  <h3 class="timeline-header border-0"><a href="#">Bạn</a> đã cập nhật thông tin cá nhân
                  </h3>
                </div>
              </div>
              <!-- END timeline item -->
              <!-- timeline time label -->
              <div class="time-label">
                <span class="bg-success">
                  09/04/2025
                </span>
              </div>
              <!-- /.timeline-label -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-desktop bg-warning"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 15:20</span>

                  <h3 class="timeline-header"><a href="#">Hệ thống</a> phát hiện hoạt động</h3>

                  <div class="timeline-body">
                    Bạn đã kết nối USB mới vào máy trạm DESKTOP-001
                  </div>
                </div>
              </div>
              <!-- END timeline item -->
              <div>
                <i class="far fa-clock bg-gray"></i>
              </div>
            </div>
            <!-- /.timeline -->
          </div>
          <!-- /.tab-pane -->
        </div>
        <!-- /.tab-content -->
      </div><!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
</div>
<!-- /.row -->
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    $('.select2').select2();
  });
</script>
{% endblock %}