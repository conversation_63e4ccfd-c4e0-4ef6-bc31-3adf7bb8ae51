from django.urls import path
from . import views

app_name = 'xml4750'

urlpatterns = [
    path('', views.index, name='index'),
    path('list/', views.list_xml, name='list_xml'),
    # path('edit_xml/<str:xml_type>/<str:maLK>/', views.edit_xml, name='edit_xml'),
    path('import/', views.import_xml, name='import_xml'),
    path('export/', views.export_xml, name='export_xml'),
    # path('delete/', views.delete_xml, name='delete_xml'),
    path('delete/<str:xml_type>/<int:record_id>/', views.delete_xml, name='delete_xml_record'),
    path('delete-by-malk/<str:xml_type>/<str:maLK>/', views.delete_xml_by_malk, name='delete_xml_by_malk'),
    path('delete_selected/<str:xml_type>/', views.delete_selected_records, name='delete_selected_records'),
    # path('update_field/', views.update_field, name='update_field'),
    path('save_row/', views.save_row, name='save_row'),
    path('list-new/', views.list_xml_new, name='list_xml_new'),

    path('save_preview_data/', views.save_preview_data, name='save_preview_data'),
    # API endpoints mới
    path('api/xml-data/', views.xml_data_api, name='xml_data_api'),
    path('edit_xml_view/<str:ma_lk>/>', views.edit_xml_view, name='edit_xml_tong_hop'),
    path('save_edited_data_from_modal/', views.save_edited_data_from_modal, name='save_edited_data_from_modal'),
    path('api/hoso-by-malk/<str:ma_lk>/<str:ngayTao>/', views.api_get_hoso_by_malk_view, name='api_get_hoso_by_malk'),
    path('api/delete-selected/', views.delete_selected_by_malk, name='delete_selected_by_malk'),
    path('export-excel/', views.export_excel, name='export_excel'),

    # Thêm các URL cho validation
    path('api/get_all_field_configs/', views.get_all_field_configs, name='get_all_field_configs'),
    path('api/get_validation_rules/', views.get_validation_rules, name='get_validation_rules'),
    path('api/save_validation_rules/', views.save_validation_rules, name='save_validation_rules'),
    path('api/save_single_validation_rule/', views.save_single_validation_rule, name='save_single_validation_rule'),
    path('api/delete_validation_rule/', views.delete_validation_rule, name='delete_validation_rule'),
    path('api/get_xml_fields/', views.get_xml_fields, name='get_xml_fields'),
    path('api/get_field_config/', views.get_field_config, name='get_field_config'),
    path('api/save_field_config/', views.save_field_config, name='save_field_config'),
    path('api/get_xml_type_config/', views.get_xml_type_config, name='get_xml_type_config'),
    path('validation_config/', views.validation_config, name='validation_config'),

    # Thêm các URL cho Report
    path('xml_reports/', views.xml_reports, name='xml_reports'),
    path('export_bao_cao_excel/', views.export_bao_cao_excel, name='export_bao_cao_excel'),
    path('preview_report/', views.preview_report, name='preview_report'),

    # Thêm các URL cho Đối chiếu hồ sơ BHYT
    path('doi_chieu_hs_bh/', views.doi_chieu_hs_bh, name='doi_chieu_hs_bh'),
    path('api_doi_chieu_ho_so/', views.api_doi_chieu_ho_so, name='api_doi_chieu_ho_so'),
]
