{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Quản <PERSON>ý chức vụ{% endblock %}

{% block page_title %}<PERSON><PERSON> sách chức vụ{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active"><PERSON>h sách chức vụ</li>
{% endblock %}

{% block extra_css %}
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<style>
  .switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  input:checked + .slider {
    background-color: #28a745;
  }

  input:checked + .slider:before {
    transform: translateX(16px);
  }
</style>
{% endblock %}

{% block content %}
<div class="card">
  <div class="card-header d-flex justify-content-between">
    <button id="add-position-btn" class="btn btn-primary">
      <i class="fas fa-plus mr-1"></i> Thêm khoa/phòng ban
    </button>
  </div>
  <div class="card-body">
    <div id="position-table"></div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
<script>
document.addEventListener("DOMContentLoaded", function () {
  const csrfToken = "{{ csrf_token }}";

  const table = new Tabulator("#position-table", {
    ajaxURL: "{% url 'departments:position_list_data_api' %}",
    layout: "fitDataFill",
    pagination: "local",
    paginationSize: 10,
    headerFilterLiveFilterDelay: 300,
    resizableColumnFit: true,
    columns: [
      { title: "Tên chức vụ", field: "name", editor: "input" , headerFilter:true},
      { title: "Mã chức vụ", field: "code", editor: "input" , headerFilter:true},
      { title: "Giới thiệu", field: "description", editor: "input", headerFilter:true},
      { title: "Trạng thái", field: "is_used", hozAlign: "center",
        formatter: function(cell) {
          const checked = cell.getValue() ? "checked" : "";
          return `<label class="switch">
                    <input type="checkbox" class="toggle-switch" data-id="${cell.getRow().getData().id}" ${checked}>
                    <span class="slider round"></span>
                  </label>`;
        },
        cellClick: function(e, cell) {
          const checkbox = e.target.closest(".toggle-switch");
          if (checkbox) {
            const positionId = checkbox.dataset.id;
            const isChecked = checkbox.checked;
            fetch("{% url 'departments:position_toggle_api' 0 %}".replace("0", positionId), {
              method: "POST",
              headers: {
                "X-CSRFToken": csrfToken,
                "Content-Type": "application/json",
              },
              body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
              if (data.status === 'success') {
                cell.setValue(data.is_used);
              } else {
                checkbox.checked = !isChecked;
                Swal.fire("Lỗi", "Không thể cập nhật trạng thái!", "error");
              }
            })
            .catch(() => {
              checkbox.checked = !isChecked;
              Swal.fire("Lỗi", "Kết nối thất bại!", "error");
            });
          }
        }
      },
      { title: "Thao tác", field: "actions",
        formatter: function(cell) {
          const edited = cell.getRow().getData()._edited;
          return `
            <button class="btn btn-sm btn-danger delete-btn">Xóa</button>
            <button class="btn btn-sm btn-success save-btn" style="${edited ? 'display:inline-block;' : 'display:none;'}">Lưu</button>`;
        },
        cellClick: function(e, cell) {
          const row = cell.getRow();
          const data = row.getData();
          if (e.target.classList.contains("delete-btn")) {
            Swal.fire({
              title: `Xóa ${data.name}?`,
              text: "Hành động này không thể hoàn tác!",
              icon: "warning",
              showCancelButton: true,
              confirmButtonText: "Xóa",
              cancelButtonText: "Hủy"
            }).then(result => {
              if (result.isConfirmed) {
                fetch(`/positions/${data.id}/delete/`, {
                  method: "POST",
                  headers: {
                    "X-CSRFToken": csrfToken,
                  }
                })
                .then(resp => resp.json())
                .then(r => {
                  if (r.status === 'success') {
                    row.delete();
                    Swal.fire("Đã xóa!", "Dữ liệu đã được xóa.", "success");
                  } else {
                    Swal.fire("Lỗi", "Không thể xóa dữ liệu!", "error");
                  }
                });
              }
            });
          }
          if (e.target.classList.contains("save-btn")) {
            fetch("{% url 'departments:position_edit_api' 0 %}".replace("0", data.id), {
              method: "POST",
              headers: {
                "X-CSRFToken": csrfToken,
                "Content-Type": "application/json",
              },
              body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(resp => {
              if (resp.status === 'success') {
                data._edited = false;
                row.update(data).then(() => table.redraw(true));
                Swal.fire({
                  toast: true,
                  icon: 'success',
                  title: 'Đã lưu',
                  timer: 1500,
                  position: 'top-end',
                  showConfirmButton: false
                });
              } else {
                Swal.fire("Lỗi", "Không thể lưu thay đổi!", "error");
              }
            });
          }
        }
      }
    ]
  });

  // Thêm event listener để debug
  table.on("cellEdited", function(cell) {
    const row = cell.getRow();
    const data = row.getData();
    
    // Gắn cờ đã chỉnh sửa
    if (!data._edited) {
      data._edited = true;
      
      // Cập nhật data
      row.update(data).then(() => {
        // Force refresh cột actions
        const actionsCell = row.getCell("actions");
        if (actionsCell) {
          const formatter = actionsCell.getColumn().getDefinition().formatter;
          actionsCell.getElement().innerHTML = formatter(actionsCell);
        }
      }).catch(error => {
        console.error("Error updating row:", error);
      });
    }
  });

  document.getElementById("add-position-btn").addEventListener("click", function () {
    Swal.fire({
      title: 'Thêm khoa/phòng ban',
      html: `
        <input id="swal-name" class="swal2-input" placeholder="Tên khoa/phòng ban">
        <input id="swal-code" class="swal2-input" placeholder="Mã khoa/phòng ban">
        <input id="swal-description" class="swal2-input" placeholder="Giới thiệu">
        <label class="swal2-checkbox">
          <input type="checkbox" id="swal-is-used" checked> Sử dụng
        </label>
      `,
      focusConfirm: false,
      showCancelButton: true,
      confirmButtonText: 'Thêm',
      preConfirm: () => {
        const name = document.getElementById('swal-name').value;
        const code = document.getElementById('swal-code').value;
        const description = document.getElementById('swal-description').value;
        const is_used = document.getElementById('swal-is-used').checked;

        if (!name || !code) {
          Swal.showValidationMessage('Vui lòng nhập tên và mã khoa/phòng ban');
          return false;
        }

        return fetch("{% url 'departments:position_create_api' %}", {
          method: "POST",
          headers: {
            "X-CSRFToken": csrfToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name, code, description, is_used })
        })
        .then(response => response.json())
        .then(data => {
          if (data.status === 'success') {
            table.addData([data.position], true);
          } else {
            throw new Error('Thêm thất bại');
          }
        })
        .catch(() => {
          Swal.showValidationMessage('Không thể thêm khoa/phòng ban!');
        });
      }
    });
  });
});
</script>
{% endblock %}
