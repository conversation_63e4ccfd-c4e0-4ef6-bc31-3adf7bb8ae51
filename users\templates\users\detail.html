{% extends 'layouts/base.html' %}

{% block title %}Chi tiết người dùng | LAN Insight Guardian{% endblock %}

{% block page_title %}Chi tiết người dùng{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'users:list' %}">Quản lý người dùng</a></li>
<li class="breadcrumb-item active">Chi tiết người dùng</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-4">
    <!-- Profile Image -->
    <div class="card card-primary card-outline">
      <div class="card-body box-profile">
        <div class="text-center">
          <img class="profile-user-img img-fluid img-circle"
               src="/static/AdminLTE-3.0.1/dist/img/user4-128x128.jpg"
               alt="User profile picture">
        </div>

        <h3 class="profile-username text-center">{{ user.get_full_name }}</h3>

        <p class="text-muted text-center">
          {% if user.is_superuser %}
          Quản trị viên
          {% elif user.is_staff %}
          Nhân viên
          {% else %}
          Người dùng
          {% endif %}
        </p>

        <ul class="list-group list-group-unbordered mb-3">
          <li class="list-group-item">
            <b>Tên đăng nhập</b> <a class="float-right">{{ user.username }}</a>
          </li>
          <li class="list-group-item">
            <b>Email</b> <a class="float-right">{{ user.email }}</a>
          </li>
          <li class="list-group-item">
            <b>Trạng thái</b>
            <span class="float-right">
              {% if user.is_active %}
              <span class="badge badge-success">Hoạt động</span>
              {% else %}
              <span class="badge badge-secondary">Vô hiệu</span>
              {% endif %}
            </span>
          </li>
          <li class="list-group-item">
            <b>Ngày tham gia</b> <a class="float-right">{{ user.date_joined|date:"d/m/Y" }}</a>
          </li>
          <li class="list-group-item">
            <b>Đăng nhập cuối</b> <a class="float-right">{{ user.last_login|date:"d/m/Y H:i" }}</a>
          </li>
        </ul>

        <div class="btn-group btn-block">
          <a href="{% url 'users:edit' user.id %}" class="btn btn-primary"><b>Chỉnh sửa</b></a>
          <a href="{% url 'users:delete' user.id %}" class="btn btn-danger" data-confirm="delete"
             data-title="Xác nhận xóa người dùng"
             data-text="Bạn có chắc chắn muốn xóa người dùng {{ user.username }}?"><b>Xóa</b></a>
        </div>
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
  <div class="col-md-8">
    <div class="card">
      <div class="card-header p-2">
        <ul class="nav nav-pills">
          <li class="nav-item"><a class="nav-link active" href="#activity" data-toggle="tab">Hoạt động gần đây</a></li>
          <li class="nav-item"><a class="nav-link" href="#permissions" data-toggle="tab">Quyền hạn</a></li>
        </ul>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content">
          <div class="active tab-pane" id="activity">
            <!-- Activity Timeline -->
            <div class="timeline timeline-inverse">
              <!-- timeline time label -->
              <div class="time-label">
                <span class="bg-danger">
                  10/04/2025
                </span>
              </div>
              <!-- /.timeline-label -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-envelope bg-primary"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 12:05</span>

                  <h3 class="timeline-header"><a href="#">Hệ thống</a> ghi nhận hoạt động</h3>

                  <div class="timeline-body">
                    Người dùng đã đăng nhập vào hệ thống
                  </div>
                </div>
              </div>
              <!-- END timeline item -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-user bg-info"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 09:30</span>

                  <h3 class="timeline-header border-0"><a href="#">Quản trị viên</a> đã cập nhật thông tin người dùng
                  </h3>
                </div>
              </div>
              <!-- END timeline item -->
              <!-- timeline time label -->
              <div class="time-label">
                <span class="bg-success">
                  09/04/2025
                </span>
              </div>
              <!-- /.timeline-label -->
              <!-- timeline item -->
              <div>
                <i class="fas fa-desktop bg-warning"></i>

                <div class="timeline-item">
                  <span class="time"><i class="far fa-clock"></i> 15:20</span>

                  <h3 class="timeline-header"><a href="#">Hệ thống</a> phát hiện hoạt động</h3>

                  <div class="timeline-body">
                    Kết nối USB mới được phát hiện trên máy trạm DESKTOP-001
                  </div>
                </div>
              </div>
              <!-- END timeline item -->
              <div>
                <i class="far fa-clock bg-gray"></i>
              </div>
            </div>
            <!-- /.timeline -->
          </div>
          <!-- /.tab-pane -->

          <div class="tab-pane" id="permissions">
            <div class="row">
              <div class="col-12">
                <h5>Nhóm quyền</h5>
                <div class="post">
                  <div class="user-block">
                    <span class="username">
                      <a href="#">Quản trị viên</a>
                    </span>
                    <span class="description">Nhóm có tất cả quyền trong hệ thống</span>
                  </div>
                  <!-- /.user-block -->
                  <p>
                    Người dùng thuộc nhóm này có thể thực hiện tất cả các thao tác quản trị, bao gồm quản lý người dùng,
                    quản lý thiết bị, cấu hình hệ thống và xem tất cả các báo cáo.
                  </p>
                </div>

                <h5>Quyền cụ thể</h5>
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Quyền</th>
                        <th>Mô tả</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Quản lý người dùng</td>
                        <td>Có thể xem, thêm, sửa, xóa người dùng</td>
                      </tr>
                      <tr>
                        <td>Quản lý thiết bị</td>
                        <td>Có thể xem, thêm, sửa, xóa thiết bị</td>
                      </tr>
                      <tr>
                        <td>Giám sát hoạt động</td>
                        <td>Có thể xem tất cả hoạt động của người dùng</td>
                      </tr>
                      <tr>
                        <td>Cấu hình hệ thống</td>
                        <td>Có thể thay đổi cấu hình hệ thống</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- /.tab-pane -->
        </div>
        <!-- /.tab-content -->
      </div><!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
</div>
<!-- /.row -->
{% endblock %}
